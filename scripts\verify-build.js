#!/usr/bin/env node

/**
 * 构建验证脚本
 * 检查构建输出是否正确
 */

const fs = require('fs');
const path = require('path');

const distDir = path.join(__dirname, '..', 'dist');

// 必需的文件列表
const requiredFiles = [
  'manifest.json',
  'background.js',
  'content.js',
  'popup.js',
  'popup.html',
  'options.js',
  'options.html'
];

console.log('🔍 验证构建输出...\n');

// 检查 dist 目录是否存在
if (!fs.existsSync(distDir)) {
  console.error('❌ dist 目录不存在');
  process.exit(1);
}

console.log('✅ dist 目录存在');

// 检查必需文件
let allFilesExist = true;
for (const file of requiredFiles) {
  const filePath = path.join(distDir, file);
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath);
    console.log(`✅ ${file} (${(stats.size / 1024).toFixed(2)} KB)`);
  } else {
    console.error(`❌ ${file} 不存在`);
    allFilesExist = false;
  }
}

// 检查 manifest.json 内容
try {
  const manifestPath = path.join(distDir, 'manifest.json');
  const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
  
  console.log('\n📋 Manifest 信息:');
  console.log(`   名称: ${manifest.name}`);
  console.log(`   版本: ${manifest.version}`);
  console.log(`   清单版本: ${manifest.manifest_version}`);
  
  // 检查必需的权限
  const requiredPermissions = ['storage', 'activeTab', 'scripting'];
  const hasAllPermissions = requiredPermissions.every(perm => 
    manifest.permissions && manifest.permissions.includes(perm)
  );
  
  if (hasAllPermissions) {
    console.log('✅ 权限配置正确');
  } else {
    console.error('❌ 权限配置不完整');
    allFilesExist = false;
  }
  
} catch (error) {
  console.error('❌ manifest.json 解析失败:', error.message);
  allFilesExist = false;
}

// 检查 chunks 目录
const chunksDir = path.join(distDir, 'chunks');
if (fs.existsSync(chunksDir)) {
  const chunks = fs.readdirSync(chunksDir);
  console.log(`\n📦 代码分块: ${chunks.length} 个文件`);
  chunks.forEach(chunk => {
    const chunkPath = path.join(chunksDir, chunk);
    const stats = fs.statSync(chunkPath);
    console.log(`   ${chunk} (${(stats.size / 1024).toFixed(2)} KB)`);
  });
}

console.log('\n' + '='.repeat(50));

if (allFilesExist) {
  console.log('🎉 构建验证通过！');
  console.log('\n📖 加载插件步骤:');
  console.log('1. 打开 Chrome 浏览器');
  console.log('2. 访问 chrome://extensions/');
  console.log('3. 开启"开发者模式"');
  console.log('4. 点击"加载已解压的扩展程序"');
  console.log('5. 选择 dist 文件夹');
  process.exit(0);
} else {
  console.log('❌ 构建验证失败！');
  process.exit(1);
}
