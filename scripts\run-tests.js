/**
 * 测试运行脚本
 * 解决Jest配置问题并运行测试
 */

const { spawn } = require('child_process');
const path = require('path');

// 测试配置
const testConfig = {
  // 单元测试
  unit: {
    pattern: 'tests/unit/**/*.test.ts',
    description: '单元测试'
  },
  // 集成测试
  integration: {
    pattern: 'tests/integration/**/*.test.ts',
    description: '集成测试'
  },
  // 兼容性测试
  compatibility: {
    pattern: 'tests/compatibility/**/*.test.ts',
    description: '兼容性测试'
  },
  // 端到端测试
  e2e: {
    pattern: 'tests/e2e/**/*.test.ts',
    description: '端到端测试'
  },
  // 所有测试
  all: {
    pattern: 'tests/**/*.test.ts',
    description: '所有测试'
  }
};

/**
 * 运行Jest测试
 */
function runJest(pattern, options = {}) {
  return new Promise((resolve, reject) => {
    const jestArgs = [
      '--testPathPattern=' + pattern,
      '--verbose',
      '--detectOpenHandles',
      '--forceExit',
      '--maxWorkers=1'
    ];

    if (options.watch) {
      jestArgs.push('--watch');
    }

    if (options.coverage) {
      jestArgs.push('--coverage');
    }

    console.log(`🚀 运行测试: ${pattern}`);
    console.log(`📝 Jest参数: ${jestArgs.join(' ')}`);

    const jest = spawn('npx', ['jest', ...jestArgs], {
      stdio: 'inherit',
      shell: true,
      cwd: process.cwd()
    });

    jest.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ 测试完成: ${pattern}`);
        resolve(code);
      } else {
        console.log(`❌ 测试失败: ${pattern} (退出码: ${code})`);
        reject(new Error(`测试失败，退出码: ${code}`));
      }
    });

    jest.on('error', (error) => {
      console.error(`❌ Jest运行错误:`, error);
      reject(error);
    });
  });
}

/**
 * 运行特定类型的测试
 */
async function runTestType(type, options = {}) {
  const config = testConfig[type];
  if (!config) {
    console.error(`❌ 未知的测试类型: ${type}`);
    console.log(`可用的测试类型: ${Object.keys(testConfig).join(', ')}`);
    process.exit(1);
  }

  console.log(`\n📊 开始${config.description}`);
  console.log('='.repeat(50));

  try {
    await runJest(config.pattern, options);
    console.log(`\n✅ ${config.description}完成\n`);
  } catch (error) {
    console.error(`\n❌ ${config.description}失败:`, error.message);
    process.exit(1);
  }
}

/**
 * 运行所有测试
 */
async function runAllTests(options = {}) {
  const testTypes = ['unit', 'integration', 'compatibility', 'e2e'];
  
  console.log('\n🎯 开始运行完整测试套件');
  console.log('='.repeat(50));

  for (const type of testTypes) {
    try {
      await runTestType(type, options);
    } catch (error) {
      console.error(`❌ ${type}测试失败，停止后续测试`);
      process.exit(1);
    }
  }

  console.log('\n🎉 所有测试完成！');
}

/**
 * 检查测试环境
 */
function checkTestEnvironment() {
  console.log('🔍 检查测试环境...');
  
  // 检查必要的文件
  const fs = require('fs');
  const requiredFiles = [
    'package.json',
    'jest.config.js',
    'tsconfig.json'
  ];

  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      console.error(`❌ 缺少必要文件: ${file}`);
      process.exit(1);
    }
  }

  // 检查测试目录
  if (!fs.existsSync('tests')) {
    console.error('❌ 测试目录不存在');
    process.exit(1);
  }

  console.log('✅ 测试环境检查通过');
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
🧪 测试运行脚本

用法:
  node scripts/run-tests.js [测试类型] [选项]

测试类型:
  unit          - 运行单元测试
  integration   - 运行集成测试
  compatibility - 运行兼容性测试
  e2e          - 运行端到端测试
  all          - 运行所有测试

选项:
  --watch      - 监视模式
  --coverage   - 生成覆盖率报告
  --help       - 显示帮助信息

示例:
  node scripts/run-tests.js unit
  node scripts/run-tests.js all --coverage
  node scripts/run-tests.js unit --watch
`);
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }

  // 检查测试环境
  checkTestEnvironment();

  // 解析参数
  const testType = args.find(arg => !arg.startsWith('--')) || 'all';
  const options = {
    watch: args.includes('--watch'),
    coverage: args.includes('--coverage')
  };

  console.log(`\n🎯 测试配置:`);
  console.log(`  类型: ${testType}`);
  console.log(`  监视模式: ${options.watch ? '是' : '否'}`);
  console.log(`  覆盖率报告: ${options.coverage ? '是' : '否'}`);

  // 运行测试
  if (testType === 'all') {
    await runAllTests(options);
  } else {
    await runTestType(testType, options);
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runTestType,
  runAllTests,
  testConfig
};
