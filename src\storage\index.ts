/**
 * 存储服务主入口
 * 统一管理所有存储相关功能
 */

import { DatabaseManager } from './database';
import { ConversationStore } from './conversation-store';
import { SearchIndexManager } from './search-index';
import { SettingsStore } from './settings-store';
import { ExportManager } from './export-manager';
import { Logger } from '@shared/logger';

export class StorageService {
  private db: DatabaseManager;
  private conversationStore: ConversationStore;
  private searchIndex: SearchIndexManager;
  private settingsStore: SettingsStore;
  private exportManager: ExportManager;
  private logger: Logger;
  private isInitialized = false;

  constructor() {
    this.logger = new Logger('StorageService');
    this.db = new DatabaseManager();
    this.searchIndex = new SearchIndexManager(this.db);
    this.conversationStore = new ConversationStore(this.db, this.searchIndex);
    this.settingsStore = new SettingsStore(this.db);
    this.exportManager = new ExportManager(this.db);
  }

  /**
   * 初始化存储服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      this.logger.warn('存储服务已经初始化');
      return;
    }

    try {
      this.logger.info('初始化存储服务...');
      
      // 初始化数据库
      await this.db.initialize();
      
      // 初始化设置
      await this.settingsStore.initialize();
      
      // 检查是否需要重建搜索索引
      const lastIndexVersion = await this.settingsStore.get('searchIndexVersion', '0');
      const currentIndexVersion = '1.0.0';
      
      if (lastIndexVersion !== currentIndexVersion) {
        this.logger.info('检测到搜索索引版本更新，重建索引...');
        await this.searchIndex.rebuildIndex();
        await this.settingsStore.set('searchIndexVersion', currentIndexVersion);
      }

      this.isInitialized = true;
      this.logger.info('存储服务初始化完成');
    } catch (error) {
      this.logger.error('存储服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取会话存储管理器
   */
  get conversations(): ConversationStore {
    this.ensureInitialized();
    return this.conversationStore;
  }

  /**
   * 获取搜索管理器
   */
  get search(): SearchIndexManager {
    this.ensureInitialized();
    return this.searchIndex;
  }

  /**
   * 获取设置存储管理器
   */
  get settings(): SettingsStore {
    this.ensureInitialized();
    return this.settingsStore;
  }

  /**
   * 获取导出管理器
   */
  get export(): ExportManager {
    this.ensureInitialized();
    return this.exportManager;
  }

  /**
   * 获取数据库管理器
   */
  get database(): DatabaseManager {
    this.ensureInitialized();
    return this.db;
  }

  /**
   * 检查是否已初始化
   */
  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new Error('存储服务未初始化，请先调用 initialize() 方法');
    }
  }

  /**
   * 清理所有数据
   */
  async clearAllData(): Promise<void> {
    this.ensureInitialized();
    
    try {
      this.logger.warn('开始清理所有数据...');
      
      const tables: (keyof import('./database').DatabaseSchema)[] = [
        'conversations',
        'messages', 
        'tags',
        'searchIndex',
        'settings'
      ];

      for (const table of tables) {
        await this.db.clear(table);
        this.logger.debug(`已清空表: ${table}`);
      }

      this.logger.warn('所有数据已清理完成');
    } catch (error) {
      this.logger.error('清理数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats(): Promise<{
    conversations: number;
    messages: number;
    tags: number;
    searchIndices: number;
    settings: number;
    estimatedSize: string;
  }> {
    this.ensureInitialized();

    try {
      const [conversations, messages, tags, searchIndices, settings] = await Promise.all([
        this.db.count('conversations'),
        this.db.count('messages'),
        this.db.count('tags'),
        this.db.count('searchIndex'),
        this.db.count('settings')
      ]);

      // 估算存储大小（简单估算）
      const estimatedBytes = 
        conversations * 1000 + // 每个会话约1KB
        messages * 500 + // 每条消息约500B
        tags * 100 + // 每个标签约100B
        searchIndices * 200 + // 每个搜索索引约200B
        settings * 100; // 每个设置约100B

      const estimatedSize = this.formatBytes(estimatedBytes);

      return {
        conversations,
        messages,
        tags,
        searchIndices,
        settings,
        estimatedSize
      };
    } catch (error) {
      this.logger.error('获取存储统计失败:', error);
      throw error;
    }
  }

  /**
   * 格式化字节大小
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 备份数据
   */
  async backupData(): Promise<{
    conversations: any[];
    messages: any[];
    tags: any[];
    settings: any[];
    timestamp: string;
    version: string;
  }> {
    this.ensureInitialized();

    try {
      this.logger.info('开始备份数据...');

      const [conversations, messages, tags, settings] = await Promise.all([
        this.db.getAll('conversations'),
        this.db.getAll('messages'),
        this.db.getAll('tags'),
        this.db.getAll('settings')
      ]);

      const backup = {
        conversations,
        messages,
        tags,
        settings,
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      };

      this.logger.info(`数据备份完成: ${conversations.length} 个会话, ${messages.length} 条消息`);
      return backup;
    } catch (error) {
      this.logger.error('数据备份失败:', error);
      throw error;
    }
  }

  /**
   * 恢复数据
   */
  async restoreData(backup: {
    conversations: any[];
    messages: any[];
    tags: any[];
    settings: any[];
    timestamp: string;
    version: string;
  }): Promise<void> {
    this.ensureInitialized();

    try {
      this.logger.info('开始恢复数据...');

      // 验证备份格式
      if (!backup.conversations || !backup.messages || !backup.version) {
        throw new Error('备份数据格式无效');
      }

      // 清空现有数据
      await this.clearAllData();

      // 恢复数据
      const operations = [];

      // 恢复会话
      backup.conversations.forEach(conversation => {
        operations.push({
          type: 'add' as const,
          storeName: 'conversations' as const,
          data: {
            ...conversation,
            createdAt: new Date(conversation.createdAt),
            updatedAt: new Date(conversation.updatedAt)
          }
        });
      });

      // 恢复消息
      backup.messages.forEach(message => {
        operations.push({
          type: 'add' as const,
          storeName: 'messages' as const,
          data: {
            ...message,
            timestamp: new Date(message.timestamp)
          }
        });
      });

      // 恢复标签
      backup.tags.forEach(tag => {
        operations.push({
          type: 'add' as const,
          storeName: 'tags' as const,
          data: {
            ...tag,
            createdAt: new Date(tag.createdAt)
          }
        });
      });

      // 恢复设置
      backup.settings.forEach(setting => {
        operations.push({
          type: 'add' as const,
          storeName: 'settings' as const,
          data: {
            ...setting,
            updatedAt: new Date(setting.updatedAt)
          }
        });
      });

      // 批量执行恢复操作
      await this.db.batch(operations);

      // 重建搜索索引
      await this.searchIndex.rebuildIndex();

      this.logger.info(`数据恢复完成: ${backup.conversations.length} 个会话, ${backup.messages.length} 条消息`);
    } catch (error) {
      this.logger.error('数据恢复失败:', error);
      throw error;
    }
  }

  /**
   * 关闭存储服务
   */
  close(): void {
    if (this.db) {
      this.db.close();
    }
    this.isInitialized = false;
    this.logger.info('存储服务已关闭');
  }

  /**
   * 优化存储服务
   */
  async optimize(): Promise<void> {
    this.ensureInitialized();

    try {
      this.logger.info('开始优化存储服务...');

      // 优化数据库
      await this.db.optimize();

      // 重建搜索索引
      await this.searchIndex.rebuild();

      // 清理过期数据
      await this.cleanupExpiredData();

      this.logger.info('存储服务优化完成');
    } catch (error) {
      this.logger.error('存储服务优化失败:', error);
      throw error;
    }
  }

  /**
   * 清理过期数据
   */
  private async cleanupExpiredData(): Promise<void> {
    try {
      // 清理超过90天的已删除会话
      const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
      await this.conversations.permanentDeleteBefore(ninetyDaysAgo);

      // 清理过期的搜索缓存
      await this.searchIndex.clearExpiredCache();

      this.logger.debug('过期数据清理完成');
    } catch (error) {
      this.logger.error('过期数据清理失败:', error);
    }
  }

  /**
   * 获取所有会话
   */
  async getAllConversations(): Promise<any[]> {
    this.ensureInitialized();
    return await this.db.getAll('conversations');
  }

  /**
   * 获取单个会话
   */
  async getConversation(id: string): Promise<any | null> {
    this.ensureInitialized();
    return await this.conversations.getConversation(id);
  }

  /**
   * 检查存储服务状态
   */
  isReady(): boolean {
    return this.isInitialized && this.db.isConnected();
  }
}

// 导出单例实例
export const storageService = new StorageService();

// 导出类型和接口
export * from './database';
export * from './conversation-store';
export * from './search-index';
export * from './settings-store';
export * from './export-manager';
