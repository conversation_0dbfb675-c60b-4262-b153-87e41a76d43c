/**
 * 数据导出管理器
 * 支持多种格式的对话数据导出功能
 */

import { ConversationData, MessageData } from '@types/conversation';
import { Logger } from '@utils/logger';

export type ExportFormat = 'markdown' | 'pdf' | 'json' | 'txt' | 'html' | 'csv';

export interface ExportOptions {
  format: ExportFormat;
  conversations: ConversationData[];
  includeMetadata?: boolean;
  includeTimestamps?: boolean;
  includeTags?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  platforms?: string[];
  customTemplate?: string;
  filename?: string;
}

export interface ExportResult {
  success: boolean;
  filename: string;
  content?: string;
  blob?: Blob;
  size: number;
  conversationCount: number;
  messageCount: number;
  error?: string;
}

export class ExportManager {
  private logger: Logger;

  constructor() {
    this.logger = new Logger('ExportManager');
  }

  /**
   * 导出对话数据
   */
  async exportConversations(options: ExportOptions): Promise<ExportResult> {
    this.logger.info('开始导出对话数据', {
      format: options.format,
      conversationCount: options.conversations.length
    });

    try {
      // 过滤对话数据
      const filteredConversations = this.filterConversations(options);

      // 根据格式选择导出方法
      let result: ExportResult;
      
      switch (options.format) {
        case 'markdown':
          result = await this.exportToMarkdown(filteredConversations, options);
          break;
        case 'pdf':
          result = await this.exportToPDF(filteredConversations, options);
          break;
        case 'json':
          result = await this.exportToJSON(filteredConversations, options);
          break;
        case 'txt':
          result = await this.exportToText(filteredConversations, options);
          break;
        case 'html':
          result = await this.exportToHTML(filteredConversations, options);
          break;
        case 'csv':
          result = await this.exportToCSV(filteredConversations, options);
          break;
        default:
          throw new Error(`不支持的导出格式: ${options.format}`);
      }

      this.logger.info('导出完成', {
        format: options.format,
        filename: result.filename,
        size: result.size,
        conversationCount: result.conversationCount,
        messageCount: result.messageCount
      });

      return result;

    } catch (error) {
      this.logger.error('导出失败:', error);
      return {
        success: false,
        filename: '',
        size: 0,
        conversationCount: 0,
        messageCount: 0,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 过滤对话数据
   */
  private filterConversations(options: ExportOptions): ConversationData[] {
    let conversations = [...options.conversations];

    // 日期范围过滤
    if (options.dateRange) {
      conversations = conversations.filter(conv => {
        const timestamp = new Date(conv.timestamp);
        return timestamp >= options.dateRange!.start && timestamp <= options.dateRange!.end;
      });
    }

    // 平台过滤
    if (options.platforms && options.platforms.length > 0) {
      conversations = conversations.filter(conv => 
        options.platforms!.includes(conv.platform)
      );
    }

    return conversations;
  }

  /**
   * 导出为Markdown格式
   */
  private async exportToMarkdown(conversations: ConversationData[], options: ExportOptions): Promise<ExportResult> {
    const lines: string[] = [];
    let messageCount = 0;

    // 添加标题
    lines.push('# AI对话导出');
    lines.push('');
    lines.push(`导出时间: ${new Date().toLocaleString()}`);
    lines.push(`对话数量: ${conversations.length}`);
    lines.push('');

    // 添加目录
    lines.push('## 目录');
    lines.push('');
    conversations.forEach((conv, index) => {
      lines.push(`${index + 1}. [${conv.title}](#对话-${index + 1}-${this.sanitizeAnchor(conv.title)})`);
    });
    lines.push('');

    // 添加对话内容
    conversations.forEach((conversation, index) => {
      lines.push(`## 对话 ${index + 1}: ${conversation.title}`);
      lines.push('');

      // 添加元数据
      if (options.includeMetadata) {
        lines.push('**对话信息:**');
        lines.push(`- 平台: ${conversation.platform}`);
        lines.push(`- 时间: ${new Date(conversation.timestamp).toLocaleString()}`);
        lines.push(`- URL: ${conversation.url}`);
        
        if (options.includeTags && conversation.metadata?.tags?.length) {
          lines.push(`- 标签: ${conversation.metadata.tags.join(', ')}`);
        }
        lines.push('');
      }

      // 添加消息
      conversation.messages.forEach((message, msgIndex) => {
        const roleIcon = message.role === 'user' ? '👤' : '🤖';
        const roleName = message.role === 'user' ? '用户' : 'AI助手';
        
        lines.push(`### ${roleIcon} ${roleName} ${msgIndex + 1}`);
        
        if (options.includeTimestamps) {
          lines.push(`*时间: ${new Date(message.timestamp).toLocaleString()}*`);
          lines.push('');
        }
        
        lines.push(message.content);
        lines.push('');
        messageCount++;
      });

      lines.push('---');
      lines.push('');
    });

    const content = lines.join('\n');
    const filename = options.filename || `ai-conversations-${Date.now()}.md`;
    const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });

    return {
      success: true,
      filename,
      content,
      blob,
      size: blob.size,
      conversationCount: conversations.length,
      messageCount
    };
  }

  /**
   * 导出为JSON格式
   */
  private async exportToJSON(conversations: ConversationData[], options: ExportOptions): Promise<ExportResult> {
    const exportData = {
      exportInfo: {
        timestamp: new Date().toISOString(),
        format: 'json',
        version: '1.0',
        conversationCount: conversations.length,
        messageCount: conversations.reduce((sum, conv) => sum + conv.messages.length, 0)
      },
      conversations: conversations.map(conv => ({
        ...conv,
        metadata: options.includeMetadata ? conv.metadata : undefined,
        messages: conv.messages.map(msg => ({
          ...msg,
          timestamp: options.includeTimestamps ? msg.timestamp : undefined
        }))
      }))
    };

    const content = JSON.stringify(exportData, null, 2);
    const filename = options.filename || `ai-conversations-${Date.now()}.json`;
    const blob = new Blob([content], { type: 'application/json;charset=utf-8' });

    return {
      success: true,
      filename,
      content,
      blob,
      size: blob.size,
      conversationCount: conversations.length,
      messageCount: exportData.exportInfo.messageCount
    };
  }

  /**
   * 导出为纯文本格式
   */
  private async exportToText(conversations: ConversationData[], options: ExportOptions): Promise<ExportResult> {
    const lines: string[] = [];
    let messageCount = 0;

    lines.push('AI对话导出');
    lines.push('='.repeat(50));
    lines.push(`导出时间: ${new Date().toLocaleString()}`);
    lines.push(`对话数量: ${conversations.length}`);
    lines.push('');

    conversations.forEach((conversation, index) => {
      lines.push(`对话 ${index + 1}: ${conversation.title}`);
      lines.push('-'.repeat(30));

      if (options.includeMetadata) {
        lines.push(`平台: ${conversation.platform}`);
        lines.push(`时间: ${new Date(conversation.timestamp).toLocaleString()}`);
        if (options.includeTags && conversation.metadata?.tags?.length) {
          lines.push(`标签: ${conversation.metadata.tags.join(', ')}`);
        }
        lines.push('');
      }

      conversation.messages.forEach((message, msgIndex) => {
        const roleName = message.role === 'user' ? '用户' : 'AI助手';
        lines.push(`[${roleName} ${msgIndex + 1}]`);
        
        if (options.includeTimestamps) {
          lines.push(`时间: ${new Date(message.timestamp).toLocaleString()}`);
        }
        
        lines.push(message.content);
        lines.push('');
        messageCount++;
      });

      lines.push('');
    });

    const content = lines.join('\n');
    const filename = options.filename || `ai-conversations-${Date.now()}.txt`;
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });

    return {
      success: true,
      filename,
      content,
      blob,
      size: blob.size,
      conversationCount: conversations.length,
      messageCount
    };
  }

  /**
   * 导出为HTML格式
   */
  private async exportToHTML(conversations: ConversationData[], options: ExportOptions): Promise<ExportResult> {
    const messageCount = conversations.reduce((sum, conv) => sum + conv.messages.length, 0);
    
    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI对话导出</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
        h2 { color: #555; margin-top: 30px; }
        .conversation { margin-bottom: 40px; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; }
        .conversation-header { background: #f8f9fa; padding: 15px; border-bottom: 1px solid #e0e0e0; }
        .message { padding: 15px; border-bottom: 1px solid #f0f0f0; }
        .message:last-child { border-bottom: none; }
        .user-message { background: #e3f2fd; }
        .assistant-message { background: #f3e5f5; }
        .message-role { font-weight: bold; margin-bottom: 8px; }
        .message-content { white-space: pre-wrap; }
        .metadata { font-size: 0.9em; color: #666; margin-bottom: 10px; }
        .timestamp { font-size: 0.8em; color: #999; }
        .export-info { background: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI对话导出</h1>
        
        <div class="export-info">
            <strong>导出信息:</strong><br>
            导出时间: ${new Date().toLocaleString()}<br>
            对话数量: ${conversations.length}<br>
            消息数量: ${messageCount}
        </div>

        ${conversations.map((conversation, index) => `
            <div class="conversation">
                <div class="conversation-header">
                    <h2>📝 ${conversation.title}</h2>
                    ${options.includeMetadata ? `
                        <div class="metadata">
                            <strong>平台:</strong> ${conversation.platform}<br>
                            <strong>时间:</strong> ${new Date(conversation.timestamp).toLocaleString()}<br>
                            ${conversation.metadata?.tags?.length ? `<strong>标签:</strong> ${conversation.metadata.tags.join(', ')}<br>` : ''}
                        </div>
                    ` : ''}
                </div>
                
                ${conversation.messages.map((message, msgIndex) => `
                    <div class="message ${message.role}-message">
                        <div class="message-role">
                            ${message.role === 'user' ? '👤 用户' : '🤖 AI助手'}
                        </div>
                        ${options.includeTimestamps ? `
                            <div class="timestamp">${new Date(message.timestamp).toLocaleString()}</div>
                        ` : ''}
                        <div class="message-content">${this.escapeHtml(message.content)}</div>
                    </div>
                `).join('')}
            </div>
        `).join('')}
    </div>
</body>
</html>`;

    const filename = options.filename || `ai-conversations-${Date.now()}.html`;
    const blob = new Blob([html], { type: 'text/html;charset=utf-8' });

    return {
      success: true,
      filename,
      content: html,
      blob,
      size: blob.size,
      conversationCount: conversations.length,
      messageCount
    };
  }

  /**
   * 导出为CSV格式
   */
  private async exportToCSV(conversations: ConversationData[], options: ExportOptions): Promise<ExportResult> {
    const rows: string[] = [];
    let messageCount = 0;

    // CSV头部
    const headers = [
      '对话ID',
      '对话标题',
      '平台',
      '对话时间',
      '消息ID',
      '消息角色',
      '消息内容',
      '消息时间'
    ];

    if (options.includeTags) {
      headers.push('标签');
    }

    rows.push(headers.map(h => `"${h}"`).join(','));

    // 添加数据行
    conversations.forEach(conversation => {
      conversation.messages.forEach(message => {
        const row = [
          conversation.id,
          conversation.title.replace(/"/g, '""'),
          conversation.platform,
          new Date(conversation.timestamp).toISOString(),
          message.id,
          message.role === 'user' ? '用户' : 'AI助手',
          message.content.replace(/"/g, '""').replace(/\n/g, '\\n'),
          new Date(message.timestamp).toISOString()
        ];

        if (options.includeTags) {
          row.push((conversation.metadata?.tags || []).join(';'));
        }

        rows.push(row.map(cell => `"${cell}"`).join(','));
        messageCount++;
      });
    });

    const content = rows.join('\n');
    const filename = options.filename || `ai-conversations-${Date.now()}.csv`;
    const blob = new Blob(['\ufeff' + content], { type: 'text/csv;charset=utf-8' }); // 添加BOM

    return {
      success: true,
      filename,
      content,
      blob,
      size: blob.size,
      conversationCount: conversations.length,
      messageCount
    };
  }

  /**
   * 导出为PDF格式（简化版本）
   */
  private async exportToPDF(conversations: ConversationData[], options: ExportOptions): Promise<ExportResult> {
    // 注意：这里是一个简化的PDF导出实现
    // 在实际项目中，你可能需要使用 jsPDF 或 Puppeteer 等库来生成真正的PDF

    const htmlContent = await this.exportToHTML(conversations, options);

    // 这里应该使用PDF生成库，暂时返回HTML内容
    const filename = options.filename || `ai-conversations-${Date.now()}.pdf`;

    // 模拟PDF生成
    const pdfNote = `
PDF导出功能需要额外的库支持。
当前返回的是HTML格式的内容，可以通过浏览器打印功能转换为PDF。

要实现真正的PDF导出，建议使用以下方案之一：
1. jsPDF + html2canvas
2. Puppeteer (需要服务端支持)
3. 浏览器原生打印API

HTML内容:
${htmlContent.content}
`;

    const blob = new Blob([pdfNote], { type: 'text/plain;charset=utf-8' });

    return {
      success: true,
      filename: filename.replace('.pdf', '.txt'),
      content: pdfNote,
      blob,
      size: blob.size,
      conversationCount: conversations.length,
      messageCount: htmlContent.messageCount
    };
  }

  /**
   * 清理锚点文本
   */
  private sanitizeAnchor(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\u4e00-\u9fff\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim();
  }

  /**
   * HTML转义
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 下载文件
   */
  downloadFile(result: ExportResult): void {
    if (!result.success || !result.blob) {
      throw new Error('导出结果无效，无法下载');
    }

    const url = URL.createObjectURL(result.blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = result.filename;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理URL对象
    setTimeout(() => URL.revokeObjectURL(url), 100);

    this.logger.info('文件下载完成', { filename: result.filename });
  }

  /**
   * 获取支持的导出格式
   */
  getSupportedFormats(): { format: ExportFormat; name: string; description: string }[] {
    return [
      {
        format: 'markdown',
        name: 'Markdown',
        description: '适合技术文档和版本控制，支持格式化文本'
      },
      {
        format: 'json',
        name: 'JSON',
        description: '结构化数据格式，便于程序处理和数据交换'
      },
      {
        format: 'html',
        name: 'HTML',
        description: '网页格式，支持样式和交互，可在浏览器中查看'
      },
      {
        format: 'txt',
        name: '纯文本',
        description: '简单的文本格式，兼容性最好'
      },
      {
        format: 'csv',
        name: 'CSV',
        description: '表格格式，可在Excel等软件中打开'
      },
      {
        format: 'pdf',
        name: 'PDF',
        description: '便携式文档格式，适合打印和分享（需要额外库支持）'
      }
    ];
  }

  /**
   * 预估导出文件大小
   */
  estimateFileSize(conversations: ConversationData[], format: ExportFormat): number {
    const totalChars = conversations.reduce((sum, conv) => {
      return sum + conv.title.length +
             conv.messages.reduce((msgSum, msg) => msgSum + msg.content.length, 0);
    }, 0);

    // 根据格式估算文件大小（字节）
    switch (format) {
      case 'json':
        return totalChars * 2; // JSON有额外的结构开销
      case 'html':
        return totalChars * 3; // HTML有标签开销
      case 'markdown':
        return totalChars * 1.2; // Markdown有少量格式开销
      case 'csv':
        return totalChars * 1.5; // CSV有分隔符开销
      case 'txt':
        return totalChars; // 纯文本最小
      case 'pdf':
        return totalChars * 4; // PDF有较大开销
      default:
        return totalChars;
    }
  }
}
