/**
 * 端到端测试
 * 模拟完整的用户工作流程
 */

import { AdapterManager } from '../../src/content/adapters/adapter-manager';
import { SearchEngine } from '../../src/core/search/search-engine';
import { ExportManager } from '../../src/core/export/export-manager';
import { BackupManager } from '../../src/core/backup/backup-manager';
import { SyncManager } from '../../src/core/sync/sync-manager';
import { ConversationData } from '../../src/types/conversation';
import { createMockElement } from '../setup';

describe('端到端测试', () => {
  let adapterManager: AdapterManager;
  let searchEngine: SearchEngine;
  let exportManager: ExportManager;
  let backupManager: BackupManager;
  let syncManager: SyncManager;

  beforeEach(async () => {
    // 初始化所有组件
    adapterManager = new AdapterManager();
    searchEngine = new SearchEngine();
    exportManager = new ExportManager();
    
    backupManager = new BackupManager({
      autoBackup: false,
      backupInterval: 24,
      maxBackups: 10,
      compression: true,
      encryption: false
    });

    syncManager = new SyncManager({
      provider: 'chrome-storage',
      enabled: true,
      autoSync: false,
      syncInterval: 60,
      settings: {
        conflictResolution: 'local',
        maxRetries: 3,
        timeout: 30000
      }
    });

    await syncManager.initialize();

    // 清理DOM
    document.body.innerHTML = '';
    document.head.innerHTML = '';
  });

  afterEach(() => {
    searchEngine.clearCache();
    syncManager.destroy();
    backupManager.destroy();
    document.body.innerHTML = '';
    document.head.innerHTML = '';
  });

  describe('完整用户工作流程', () => {
    test('场景1: 用户在ChatGPT上进行对话并自动保存', async () => {
      // 1. 模拟用户访问ChatGPT
      Object.defineProperty(window, 'location', {
        value: { 
          hostname: 'chat.openai.com', 
          href: 'https://chat.openai.com/c/javascript-tutorial' 
        },
        writable: true
      });

      // 2. 创建ChatGPT页面结构
      const title = createMockElement('h1');
      title.textContent = 'JavaScript异步编程教程';
      document.head.appendChild(title);

      const container = createMockElement('main');
      
      // 用户问题
      const userMsg1 = createMockElement('div', { 'data-message-author-role': 'user' });
      userMsg1.textContent = '请解释JavaScript中的Promise是什么？';
      container.appendChild(userMsg1);

      // AI回答
      const aiMsg1 = createMockElement('div', { 'data-message-author-role': 'assistant' });
      aiMsg1.textContent = 'Promise是JavaScript中处理异步操作的对象，它代表了一个异步操作的最终完成或失败。Promise有三种状态：pending（等待中）、fulfilled（已完成）、rejected（已拒绝）。';
      container.appendChild(aiMsg1);

      document.body.appendChild(container);

      // 3. 检测平台并提取对话
      const adapter = adapterManager.detectPlatform();
      expect(adapter?.getPlatformName()).toBe('ChatGPT');

      const conversationData = adapterManager.extractCurrentConversation();
      expect(conversationData).not.toBeNull();
      expect(conversationData!.platform).toBe('ChatGPT');
      expect(conversationData!.title).toBe('JavaScript异步编程教程');
      expect(conversationData!.messages).toHaveLength(2);

      // 4. 构建搜索索引
      await searchEngine.buildIndex([conversationData!]);

      // 5. 验证搜索功能
      const searchResult = await searchEngine.search('Promise');
      expect(searchResult.results).toHaveLength(1);
      expect(searchResult.results[0].conversation.id).toBe(conversationData!.id);

      // 6. 同步到云端
      (global as any).chrome.storage.sync.get.mockResolvedValue({ conversations: [] });
      (global as any).chrome.storage.sync.set.mockResolvedValue(undefined);

      const syncResult = await syncManager.sync([conversationData!]);
      expect(syncResult.success).toBe(true);
      expect(syncResult.syncedCount).toBe(1);

      console.log('✅ 场景1完成: ChatGPT对话自动保存');
    });

    test('场景2: 用户搜索历史对话并导出', async () => {
      // 1. 准备历史对话数据
      const conversations: ConversationData[] = [
        {
          id: 'conv_001',
          platform: 'ChatGPT',
          title: 'React性能优化技巧',
          url: 'https://chat.openai.com/c/react-performance',
          timestamp: new Date('2024-01-15T10:00:00Z'),
          messages: [
            {
              id: 'msg_001',
              role: 'user',
              content: '如何优化React应用的性能？',
              timestamp: new Date('2024-01-15T10:00:00Z')
            },
            {
              id: 'msg_002',
              role: 'assistant',
              content: '优化React性能的方法包括：1. 使用React.memo避免不必要的重渲染 2. 使用useMemo和useCallback缓存计算结果 3. 实现代码分割和懒加载 4. 优化状态管理',
              timestamp: new Date('2024-01-15T10:01:00Z')
            }
          ],
          metadata: {
            tags: ['React', '性能优化', '前端开发']
          }
        },
        {
          id: 'conv_002',
          platform: 'Claude',
          title: 'Python数据分析入门',
          url: 'https://claude.ai/chat/python-data',
          timestamp: new Date('2024-01-16T14:00:00Z'),
          messages: [
            {
              id: 'msg_003',
              role: 'user',
              content: '我想学习Python数据分析，应该从哪里开始？',
              timestamp: new Date('2024-01-16T14:00:00Z')
            },
            {
              id: 'msg_004',
              role: 'assistant',
              content: '建议从以下步骤开始：1. 学习Python基础语法 2. 掌握NumPy和Pandas库 3. 学习数据可视化（Matplotlib、Seaborn） 4. 实践真实数据集分析',
              timestamp: new Date('2024-01-16T14:01:00Z')
            }
          ],
          metadata: {
            tags: ['Python', '数据分析', '机器学习']
          }
        }
      ];

      // 2. 构建搜索索引
      await searchEngine.buildIndex(conversations);

      // 3. 用户搜索"性能优化"
      const searchResult = await searchEngine.search('性能优化');
      expect(searchResult.results).toHaveLength(1);
      expect(searchResult.results[0].conversation.title).toBe('React性能优化技巧');

      // 4. 用户选择导出搜索结果为Markdown
      const exportResult = await exportManager.exportConversations({
        format: 'markdown',
        conversations: searchResult.results.map(r => r.conversation),
        includeMetadata: true,
        includeTags: true,
        includeTimestamps: true
      });

      expect(exportResult.success).toBe(true);
      expect(exportResult.content).toContain('React性能优化技巧');
      expect(exportResult.content).toContain('如何优化React应用的性能？');
      expect(exportResult.content).toContain('React');
      expect(exportResult.content).toContain('性能优化');

      console.log('✅ 场景2完成: 搜索历史对话并导出');
    });

    test('场景3: 用户创建备份并恢复数据', async () => {
      // 1. 准备测试数据
      const conversations: ConversationData[] = [
        {
          id: 'conv_backup_001',
          platform: 'Gemini',
          title: '机器学习基础概念',
          url: 'https://gemini.google.com/chat/ml-basics',
          timestamp: new Date('2024-01-17T09:00:00Z'),
          messages: [
            {
              id: 'msg_backup_001',
              role: 'user',
              content: '什么是机器学习？',
              timestamp: new Date('2024-01-17T09:00:00Z')
            },
            {
              id: 'msg_backup_002',
              role: 'assistant',
              content: '机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进。主要包括监督学习、无监督学习和强化学习三种类型。',
              timestamp: new Date('2024-01-17T09:01:00Z')
            }
          ],
          metadata: {
            tags: ['机器学习', 'AI', '人工智能']
          }
        }
      ];

      // 2. 创建备份
      const backupMetadata = await backupManager.createBackup(
        conversations,
        '机器学习对话备份'
      );

      expect(backupMetadata.conversationCount).toBe(1);
      expect(backupMetadata.messageCount).toBe(2);
      expect(backupMetadata.description).toBe('机器学习对话备份');

      // 3. 验证备份列表
      const backupList = await backupManager.getBackupList();
      expect(backupList).toHaveLength(1);
      expect(backupList[0].id).toBe(backupMetadata.id);

      // 4. 恢复备份
      const restoredData = await backupManager.restoreBackup(backupMetadata.id);
      expect(restoredData.conversations).toHaveLength(1);
      expect(restoredData.conversations[0].title).toBe('机器学习基础概念');

      // 5. 导出备份文件
      const exportResult = await backupManager.exportBackup(backupMetadata.id);
      expect(exportResult.filename).toMatch(/\.backup$/);
      expect(exportResult.size).toBeGreaterThan(0);

      // 6. 清理测试备份
      await backupManager.deleteBackup(backupMetadata.id);
      const finalBackupList = await backupManager.getBackupList();
      expect(finalBackupList).toHaveLength(0);

      console.log('✅ 场景3完成: 创建备份并恢复数据');
    });

    test('场景4: 多平台对话统一管理', async () => {
      // 1. 模拟用户在不同平台的对话
      const multiPlatformConversations: ConversationData[] = [
        {
          id: 'multi_001',
          platform: 'ChatGPT',
          title: 'JavaScript ES6特性',
          url: 'https://chat.openai.com/c/js-es6',
          timestamp: new Date('2024-01-18T10:00:00Z'),
          messages: [
            {
              id: 'multi_msg_001',
              role: 'user',
              content: 'ES6有哪些新特性？',
              timestamp: new Date('2024-01-18T10:00:00Z')
            },
            {
              id: 'multi_msg_002',
              role: 'assistant',
              content: 'ES6主要新特性包括：let/const、箭头函数、模板字符串、解构赋值、类、模块、Promise等。',
              timestamp: new Date('2024-01-18T10:01:00Z')
            }
          ],
          metadata: {
            tags: ['JavaScript', 'ES6', '前端']
          }
        },
        {
          id: 'multi_002',
          platform: 'Claude',
          title: 'TypeScript类型系统',
          url: 'https://claude.ai/chat/typescript-types',
          timestamp: new Date('2024-01-18T11:00:00Z'),
          messages: [
            {
              id: 'multi_msg_003',
              role: 'user',
              content: 'TypeScript的类型系统有什么优势？',
              timestamp: new Date('2024-01-18T11:00:00Z')
            },
            {
              id: 'multi_msg_004',
              role: 'assistant',
              content: 'TypeScript类型系统的优势：1. 编译时错误检查 2. 更好的IDE支持 3. 代码可读性和维护性提升 4. 重构更安全',
              timestamp: new Date('2024-01-18T11:01:00Z')
            }
          ],
          metadata: {
            tags: ['TypeScript', '类型系统', '前端']
          }
        },
        {
          id: 'multi_003',
          platform: 'Gemini',
          title: 'Node.js后端开发',
          url: 'https://gemini.google.com/chat/nodejs-backend',
          timestamp: new Date('2024-01-18T12:00:00Z'),
          messages: [
            {
              id: 'multi_msg_005',
              role: 'user',
              content: '如何用Node.js构建RESTful API？',
              timestamp: new Date('2024-01-18T12:00:00Z')
            },
            {
              id: 'multi_msg_006',
              role: 'assistant',
              content: '构建Node.js RESTful API的步骤：1. 选择框架（Express、Koa等） 2. 设计路由结构 3. 实现CRUD操作 4. 添加中间件 5. 错误处理和验证',
              timestamp: new Date('2024-01-18T12:01:00Z')
            }
          ],
          metadata: {
            tags: ['Node.js', 'RESTful API', '后端']
          }
        }
      ];

      // 2. 构建统一搜索索引
      await searchEngine.buildIndex(multiPlatformConversations);

      // 3. 跨平台搜索
      const frontendSearch = await searchEngine.search('前端', {
        filters: {
          platforms: ['ChatGPT', 'Claude']
        }
      });
      expect(frontendSearch.results).toHaveLength(2);

      const backendSearch = await searchEngine.search('后端');
      expect(backendSearch.results).toHaveLength(1);
      expect(backendSearch.results[0].conversation.platform).toBe('Gemini');

      // 4. 按平台统计
      const stats = searchEngine.getSearchStats();
      expect(stats.conversationIndices).toBe(3);

      // 5. 导出所有平台的对话
      const allPlatformsExport = await exportManager.exportConversations({
        format: 'json',
        conversations: multiPlatformConversations,
        includeMetadata: true,
        groupByPlatform: true
      });

      expect(allPlatformsExport.success).toBe(true);
      const exportedData = JSON.parse(allPlatformsExport.content!);
      expect(exportedData.platforms).toEqual(['ChatGPT', 'Claude', 'Gemini']);

      // 6. 同步所有平台数据
      const syncResult = await syncManager.sync(multiPlatformConversations);
      expect(syncResult.success).toBe(true);
      expect(syncResult.syncedCount).toBe(3);

      console.log('✅ 场景4完成: 多平台对话统一管理');
    });

    test('场景5: 错误恢复和数据一致性', async () => {
      // 1. 模拟网络错误情况
      const conversations: ConversationData[] = [
        {
          id: 'error_test_001',
          platform: 'ChatGPT',
          title: '错误恢复测试',
          url: 'https://chat.openai.com/c/error-test',
          timestamp: new Date(),
          messages: [
            {
              id: 'error_msg_001',
              role: 'user',
              content: '测试错误恢复',
              timestamp: new Date()
            }
          ],
          metadata: {
            tags: ['测试']
          }
        }
      ];

      // 2. 模拟同步失败
      (global as any).chrome.storage.sync.set.mockRejectedValueOnce(new Error('Network error'));

      const failedSyncResult = await syncManager.sync(conversations);
      expect(failedSyncResult.success).toBe(false);
      expect(failedSyncResult.error).toContain('Network error');

      // 3. 恢复网络并重试同步
      (global as any).chrome.storage.sync.set.mockResolvedValue(undefined);
      const retryResult = await syncManager.sync(conversations);
      expect(retryResult.success).toBe(true);

      // 4. 验证数据一致性
      await searchEngine.buildIndex(conversations);
      const searchResult = await searchEngine.search('错误恢复');
      expect(searchResult.results).toHaveLength(1);

      // 5. 创建备份以确保数据安全
      const backupMetadata = await backupManager.createBackup(
        conversations,
        '错误恢复测试备份'
      );
      expect(backupMetadata.conversationCount).toBe(1);

      console.log('✅ 场景5完成: 错误恢复和数据一致性');
    });
  });

  describe('性能和稳定性测试', () => {
    test('应该能够处理大量数据', async () => {
      // 生成大量测试数据
      const largeDataset: ConversationData[] = [];
      for (let i = 0; i < 500; i++) {
        largeDataset.push({
          id: `large_${i}`,
          platform: ['ChatGPT', 'Claude', 'Gemini'][i % 3] as any,
          title: `大数据测试对话 ${i + 1}`,
          url: `https://example.com/chat/${i}`,
          timestamp: new Date(Date.now() - i * 60000),
          messages: [
            {
              id: `large_msg_${i}`,
              role: 'user',
              content: `这是第${i + 1}个测试消息`,
              timestamp: new Date(Date.now() - i * 60000)
            }
          ],
          metadata: {
            tags: [`标签${i % 10}`, '大数据测试']
          }
        });
      }

      // 测试搜索性能
      const startTime = performance.now();
      await searchEngine.buildIndex(largeDataset);
      const indexTime = performance.now() - startTime;

      expect(indexTime).toBeLessThan(5000); // 索引构建应在5秒内完成

      // 测试搜索响应时间
      const searchStartTime = performance.now();
      const searchResult = await searchEngine.search('测试');
      const searchTime = performance.now() - searchStartTime;

      expect(searchTime).toBeLessThan(1000); // 搜索应在1秒内完成
      expect(searchResult.results.length).toBeGreaterThan(0);

      console.log(`✅ 大数据测试完成: 索引${indexTime.toFixed(2)}ms, 搜索${searchTime.toFixed(2)}ms`);
    });

    test('应该能够处理并发操作', async () => {
      const conversations: ConversationData[] = [
        {
          id: 'concurrent_001',
          platform: 'ChatGPT',
          title: '并发测试1',
          url: 'https://example.com/1',
          timestamp: new Date(),
          messages: [
            {
              id: 'concurrent_msg_001',
              role: 'user',
              content: '并发测试消息1',
              timestamp: new Date()
            }
          ],
          metadata: { tags: ['并发', '测试'] }
        },
        {
          id: 'concurrent_002',
          platform: 'Claude',
          title: '并发测试2',
          url: 'https://example.com/2',
          timestamp: new Date(),
          messages: [
            {
              id: 'concurrent_msg_002',
              role: 'user',
              content: '并发测试消息2',
              timestamp: new Date()
            }
          ],
          metadata: { tags: ['并发', '测试'] }
        }
      ];

      // 并发执行多个操作
      const operations = await Promise.all([
        searchEngine.buildIndex(conversations),
        exportManager.exportConversations({
          format: 'markdown',
          conversations: conversations
        }),
        backupManager.createBackup(conversations, '并发测试备份'),
        syncManager.sync(conversations)
      ]);

      // 验证所有操作都成功完成
      expect(operations).toHaveLength(4);

      // 验证搜索功能正常
      const searchResult = await searchEngine.search('并发');
      expect(searchResult.results).toHaveLength(2);

      console.log('✅ 并发操作测试完成');
    });
  });
});
