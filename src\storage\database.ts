/**
 * IndexedDB 数据库管理器
 * 负责数据库的创建、升级和基础操作
 */

import { ConversationData, MessageData } from '@types/conversation';
import { Logger } from '@shared/logger';

export interface DatabaseSchema {
  conversations: ConversationData;
  messages: MessageData;
  tags: {
    id: string;
    name: string;
    color: string;
    count: number;
    createdAt: Date;
  };
  searchIndex: {
    id: string;
    conversationId: string;
    content: string;
    type: 'title' | 'message' | 'tag';
    platform: string;
    createdAt: Date;
  };
  settings: {
    key: string;
    value: any;
    updatedAt: Date;
  };
}

export class DatabaseManager {
  private db: IDBDatabase | null = null;
  private logger: Logger;
  private readonly dbName = 'AIChatMemo';
  private readonly version = 1;

  constructor() {
    this.logger = new Logger('DatabaseManager');
  }

  /**
   * 初始化数据库连接
   */
  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        this.logger.error('数据库打开失败:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        this.logger.info('数据库连接成功');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        this.logger.info('数据库升级中...');
        this.handleUpgrade(event);
      };
    });
  }

  /**
   * 处理数据库升级
   */
  private handleUpgrade(event: IDBVersionChangeEvent): void {
    const db = (event.target as IDBOpenDBRequest).result;
    const transaction = (event.target as IDBOpenDBRequest).transaction!;

    // 创建会话表
    if (!db.objectStoreNames.contains('conversations')) {
      const conversationStore = db.createObjectStore('conversations', { keyPath: 'id' });
      conversationStore.createIndex('platform', 'platform', { unique: false });
      conversationStore.createIndex('createdAt', 'createdAt', { unique: false });
      conversationStore.createIndex('updatedAt', 'updatedAt', { unique: false });
      conversationStore.createIndex('title', 'title', { unique: false });
      conversationStore.createIndex('url', 'url', { unique: false });
      this.logger.debug('创建 conversations 表');
    }

    // 创建消息表
    if (!db.objectStoreNames.contains('messages')) {
      const messageStore = db.createObjectStore('messages', { keyPath: 'id' });
      messageStore.createIndex('conversationId', 'conversationId', { unique: false });
      messageStore.createIndex('type', 'type', { unique: false });
      messageStore.createIndex('timestamp', 'timestamp', { unique: false });
      messageStore.createIndex('platform', 'metadata.platform', { unique: false });
      this.logger.debug('创建 messages 表');
    }

    // 创建标签表
    if (!db.objectStoreNames.contains('tags')) {
      const tagStore = db.createObjectStore('tags', { keyPath: 'id' });
      tagStore.createIndex('name', 'name', { unique: true });
      tagStore.createIndex('count', 'count', { unique: false });
      this.logger.debug('创建 tags 表');
    }

    // 创建搜索索引表
    if (!db.objectStoreNames.contains('searchIndex')) {
      const searchStore = db.createObjectStore('searchIndex', { keyPath: 'id' });
      searchStore.createIndex('conversationId', 'conversationId', { unique: false });
      searchStore.createIndex('content', 'content', { unique: false });
      searchStore.createIndex('type', 'type', { unique: false });
      searchStore.createIndex('platform', 'platform', { unique: false });
      this.logger.debug('创建 searchIndex 表');
    }

    // 创建设置表
    if (!db.objectStoreNames.contains('settings')) {
      const settingsStore = db.createObjectStore('settings', { keyPath: 'key' });
      this.logger.debug('创建 settings 表');
    }

    transaction.oncomplete = () => {
      this.logger.info('数据库升级完成');
    };
  }

  /**
   * 获取对象存储
   */
  private getStore<T extends keyof DatabaseSchema>(
    storeName: T, 
    mode: IDBTransactionMode = 'readonly'
  ): IDBObjectStore {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    const transaction = this.db.transaction([storeName], mode);
    return transaction.objectStore(storeName);
  }

  /**
   * 获取事务
   */
  private getTransaction(
    storeNames: (keyof DatabaseSchema)[], 
    mode: IDBTransactionMode = 'readonly'
  ): IDBTransaction {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    return this.db.transaction(storeNames, mode);
  }

  /**
   * 执行异步操作
   */
  private async executeRequest<T>(request: IDBRequest<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * 添加记录
   */
  async add<T extends keyof DatabaseSchema>(
    storeName: T, 
    data: DatabaseSchema[T]
  ): Promise<void> {
    const store = this.getStore(storeName, 'readwrite');
    const request = store.add(data);
    await this.executeRequest(request);
    this.logger.debug(`添加记录到 ${storeName}:`, data);
  }

  /**
   * 更新记录
   */
  async put<T extends keyof DatabaseSchema>(
    storeName: T, 
    data: DatabaseSchema[T]
  ): Promise<void> {
    const store = this.getStore(storeName, 'readwrite');
    const request = store.put(data);
    await this.executeRequest(request);
    this.logger.debug(`更新记录到 ${storeName}:`, data);
  }

  /**
   * 获取记录
   */
  async get<T extends keyof DatabaseSchema>(
    storeName: T, 
    key: string
  ): Promise<DatabaseSchema[T] | undefined> {
    const store = this.getStore(storeName);
    const request = store.get(key);
    return await this.executeRequest(request);
  }

  /**
   * 删除记录
   */
  async delete<T extends keyof DatabaseSchema>(
    storeName: T, 
    key: string
  ): Promise<void> {
    const store = this.getStore(storeName, 'readwrite');
    const request = store.delete(key);
    await this.executeRequest(request);
    this.logger.debug(`删除记录从 ${storeName}:`, key);
  }

  /**
   * 获取所有记录
   */
  async getAll<T extends keyof DatabaseSchema>(
    storeName: T
  ): Promise<DatabaseSchema[T][]> {
    const store = this.getStore(storeName);
    const request = store.getAll();
    return await this.executeRequest(request);
  }

  /**
   * 按索引查询
   */
  async getByIndex<T extends keyof DatabaseSchema>(
    storeName: T,
    indexName: string,
    value: any
  ): Promise<DatabaseSchema[T][]> {
    const store = this.getStore(storeName);
    const index = store.index(indexName);
    const request = index.getAll(value);
    return await this.executeRequest(request);
  }

  /**
   * 范围查询
   */
  async getByRange<T extends keyof DatabaseSchema>(
    storeName: T,
    indexName: string,
    range: IDBKeyRange
  ): Promise<DatabaseSchema[T][]> {
    const store = this.getStore(storeName);
    const index = store.index(indexName);
    const request = index.getAll(range);
    return await this.executeRequest(request);
  }

  /**
   * 计数
   */
  async count<T extends keyof DatabaseSchema>(
    storeName: T,
    key?: string
  ): Promise<number> {
    const store = this.getStore(storeName);
    const request = key ? store.count(key) : store.count();
    return await this.executeRequest(request);
  }

  /**
   * 清空表
   */
  async clear<T extends keyof DatabaseSchema>(storeName: T): Promise<void> {
    const store = this.getStore(storeName, 'readwrite');
    const request = store.clear();
    await this.executeRequest(request);
    this.logger.info(`清空表 ${storeName}`);
  }

  /**
   * 批量操作
   */
  async batch(operations: Array<{
    type: 'add' | 'put' | 'delete';
    storeName: keyof DatabaseSchema;
    data?: any;
    key?: string;
  }>): Promise<void> {
    const storeNames = [...new Set(operations.map(op => op.storeName))];
    const transaction = this.getTransaction(storeNames, 'readwrite');

    const promises = operations.map(op => {
      const store = transaction.objectStore(op.storeName);
      let request: IDBRequest;

      switch (op.type) {
        case 'add':
          request = store.add(op.data);
          break;
        case 'put':
          request = store.put(op.data);
          break;
        case 'delete':
          request = store.delete(op.key!);
          break;
        default:
          throw new Error(`未知操作类型: ${op.type}`);
      }

      return this.executeRequest(request);
    });

    await Promise.all(promises);
    this.logger.debug(`批量操作完成: ${operations.length} 个操作`);
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
      this.logger.info('数据库连接已关闭');
    }
  }

  /**
   * 优化数据库
   */
  async optimize(): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    try {
      this.logger.info('开始优化数据库...');

      // 清理碎片化数据
      await this.defragment();

      // 重建索引
      await this.rebuildIndexes();

      // 清理过期数据
      await this.cleanupExpiredData();

      this.logger.info('数据库优化完成');
    } catch (error) {
      this.logger.error('数据库优化失败:', error);
      throw error;
    }
  }

  /**
   * 清理碎片化数据
   */
  private async defragment(): Promise<void> {
    // IndexedDB 会自动处理碎片化，这里主要是清理无效引用
    try {
      // 清理无效的消息引用
      const conversations = await this.getAll('conversations');
      const conversationIds = new Set(conversations.map(c => c.id));

      const messages = await this.getAll('messages');
      const invalidMessages = messages.filter(m => !conversationIds.has(m.conversationId));

      if (invalidMessages.length > 0) {
        const deleteOps = invalidMessages.map(m => ({
          type: 'delete' as const,
          storeName: 'messages' as const,
          key: m.id
        }));

        await this.batch(deleteOps);
        this.logger.info(`清理了 ${invalidMessages.length} 条无效消息`);
      }

      // 清理无效的搜索索引
      const searchIndexes = await this.getAll('searchIndex');
      const invalidIndexes = searchIndexes.filter(si => !conversationIds.has(si.conversationId));

      if (invalidIndexes.length > 0) {
        const deleteOps = invalidIndexes.map(si => ({
          type: 'delete' as const,
          storeName: 'searchIndex' as const,
          key: si.id
        }));

        await this.batch(deleteOps);
        this.logger.info(`清理了 ${invalidIndexes.length} 条无效搜索索引`);
      }

      this.logger.debug('数据碎片清理完成');
    } catch (error) {
      this.logger.error('数据碎片清理失败:', error);
    }
  }

  /**
   * 重建索引
   */
  private async rebuildIndexes(): Promise<void> {
    // IndexedDB 的索引是自动维护的，这里主要是验证索引完整性
    try {
      const storeNames: (keyof DatabaseSchema)[] = ['conversations', 'messages', 'tags', 'searchIndex', 'settings'];

      for (const storeName of storeNames) {
        const count = await this.count(storeName);
        this.logger.debug(`${storeName} 表包含 ${count} 条记录`);
      }

      this.logger.debug('索引验证完成');
    } catch (error) {
      this.logger.error('索引重建失败:', error);
    }
  }

  /**
   * 清理过期数据
   */
  private async cleanupExpiredData(): Promise<void> {
    try {
      // 清理超过6个月的已删除会话
      const sixMonthsAgo = new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000);
      const range = IDBKeyRange.upperBound(sixMonthsAgo);

      const conversations = await this.getByRange('conversations', 'updatedAt', range);
      const deletedConversations = conversations.filter(c => c.metadata?.isDeleted);

      if (deletedConversations.length > 0) {
        const deleteOps = deletedConversations.map(c => ({
          type: 'delete' as const,
          storeName: 'conversations' as const,
          key: c.id
        }));

        await this.batch(deleteOps);
        this.logger.info(`清理了 ${deletedConversations.length} 个过期的已删除会话`);
      }

      this.logger.debug('过期数据清理完成');
    } catch (error) {
      this.logger.error('过期数据清理失败:', error);
    }
  }

  /**
   * 检查数据库是否已连接
   */
  isConnected(): boolean {
    return this.db !== null;
  }
}
