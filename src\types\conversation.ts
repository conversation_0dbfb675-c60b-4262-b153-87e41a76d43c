/**
 * 会话数据类型定义
 */

export interface ConversationData {
  id: string;                    // 会话唯一标识符
  platform: string;             // 平台名称 (<PERSON>tGP<PERSON>, <PERSON>, Gemini, etc.)
  url: string;                   // 会话URL
  title: string;                 // 会话标题
  messages: MessageData[];       // 消息列表
  tags: string[];               // 标签列表
  notes: string;                // 用户备注
  createdAt: Date;              // 创建时间
  updatedAt: Date;              // 最后更新时间
  metadata: ConversationMetadata; // 元数据
}

export interface MessageData {
  id: string;                    // 消息唯一标识符
  conversationId: string;        // 所属会话ID
  type: 'user' | 'assistant';    // 消息类型
  content: string;               // 消息内容
  timestamp: Date;               // 消息时间戳
  metadata: MessageMetadata;     // 消息元数据
}

export interface ConversationMetadata {
  messageCount: number;          // 消息数量
  lastActivity: Date;            // 最后活动时间
  isArchived: boolean;           // 是否已归档
  exportCount?: number;          // 导出次数
  viewCount?: number;            // 查看次数
  size?: number;                 // 数据大小(字节)
  language?: string;             // 主要语言
  topics?: string[];             // 主题标签
}

export interface MessageMetadata {
  platform: string;             // 平台名称
  originalElement?: string;      // 原始DOM元素HTML
  wordCount?: number;            // 字数统计
  hasCodeBlocks?: boolean;       // 是否包含代码块
  hasImages?: boolean;           // 是否包含图片
  hasLinks?: boolean;            // 是否包含链接
  language?: string;             // 消息语言
  sentiment?: 'positive' | 'negative' | 'neutral'; // 情感倾向
}

export interface ConversationFilter {
  platform?: string[];          // 平台筛选
  tags?: string[];              // 标签筛选
  dateRange?: {                 // 时间范围
    start: Date;
    end: Date;
  };
  searchQuery?: string;         // 搜索关键词
  isArchived?: boolean;         // 归档状态
  messageCountRange?: {         // 消息数量范围
    min: number;
    max: number;
  };
}

export interface ConversationSortOptions {
  field: 'createdAt' | 'updatedAt' | 'title' | 'messageCount' | 'platform';
  order: 'asc' | 'desc';
}

export interface ExportOptions {
  format: 'markdown' | 'pdf' | 'json' | 'txt';
  includeMetadata: boolean;
  includeTimestamps: boolean;
  template?: string;            // 导出模板
  filename?: string;            // 文件名
}

export interface SearchResult {
  conversation: ConversationData;
  matches: SearchMatch[];
  score: number;
}

export interface SearchMatch {
  messageId: string;
  content: string;
  highlights: string[];
  context: string;
}

export interface TagStatistics {
  tag: string;
  count: number;
  lastUsed: Date;
  platforms: string[];
}

export interface PlatformStatistics {
  platform: string;
  conversationCount: number;
  messageCount: number;
  totalSize: number;
  lastActivity: Date;
  averageMessagesPerConversation: number;
}

export interface ConversationStatistics {
  totalConversations: number;
  totalMessages: number;
  totalSize: number;
  platformStats: PlatformStatistics[];
  tagStats: TagStatistics[];
  activityByDate: { date: string; count: number }[];
  languageDistribution: { language: string; count: number }[];
}
