/**
 * 发布版本构建脚本
 * 打包插件并准备发布材料
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const archiver = require('archiver');

class ReleaseBuildManager {
  constructor() {
    this.projectRoot = process.cwd();
    this.distDir = path.join(this.projectRoot, 'dist');
    this.releaseDir = path.join(this.projectRoot, 'release');
    this.version = this.getVersion();
  }

  /**
   * 获取版本号
   */
  getVersion() {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    return packageJson.version;
  }

  /**
   * 清理目录
   */
  cleanDirectories() {
    console.log('🧹 清理构建目录...');
    
    if (fs.existsSync(this.distDir)) {
      fs.rmSync(this.distDir, { recursive: true, force: true });
    }
    
    if (fs.existsSync(this.releaseDir)) {
      fs.rmSync(this.releaseDir, { recursive: true, force: true });
    }
    
    fs.mkdirSync(this.distDir, { recursive: true });
    fs.mkdirSync(this.releaseDir, { recursive: true });
    
    console.log('✅ 目录清理完成');
  }

  /**
   * 构建项目
   */
  buildProject() {
    console.log('🔨 构建项目...');
    
    try {
      execSync('npm run build', { stdio: 'inherit' });
      console.log('✅ 项目构建完成');
    } catch (error) {
      console.error('❌ 项目构建失败:', error.message);
      process.exit(1);
    }
  }

  /**
   * 验证构建结果
   */
  validateBuild() {
    console.log('🔍 验证构建结果...');
    
    const requiredFiles = [
      'manifest.json',
      'background.js',
      'content.js',
      'popup.html',
      'popup.js',
      'options.html',
      'options.js',
      'assets'
    ];

    for (const file of requiredFiles) {
      const filePath = path.join(this.distDir, file);
      if (!fs.existsSync(filePath)) {
        console.error(`❌ 缺少必要文件: ${file}`);
        process.exit(1);
      }
    }

    // 验证manifest.json
    const manifestPath = path.join(this.distDir, 'manifest.json');
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    if (!manifest.version) {
      console.error('❌ manifest.json缺少版本号');
      process.exit(1);
    }

    if (!manifest.permissions || manifest.permissions.length === 0) {
      console.error('❌ manifest.json缺少权限配置');
      process.exit(1);
    }

    console.log('✅ 构建结果验证通过');
  }

  /**
   * 优化构建文件
   */
  optimizeBuild() {
    console.log('⚡ 优化构建文件...');
    
    // 移除源码映射文件（生产环境不需要）
    const removeSourceMaps = (dir) => {
      const files = fs.readdirSync(dir);
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          removeSourceMaps(filePath);
        } else if (file.endsWith('.map')) {
          fs.unlinkSync(filePath);
          console.log(`  删除源码映射: ${file}`);
        }
      }
    };

    removeSourceMaps(this.distDir);

    // 压缩CSS文件
    const cssFiles = this.findFiles(this.distDir, '.css');
    for (const cssFile of cssFiles) {
      try {
        const content = fs.readFileSync(cssFile, 'utf8');
        const minified = content
          .replace(/\/\*[\s\S]*?\*\//g, '') // 移除注释
          .replace(/\s+/g, ' ') // 压缩空白
          .replace(/;\s*}/g, '}') // 移除最后的分号
          .trim();
        
        fs.writeFileSync(cssFile, minified);
        console.log(`  压缩CSS: ${path.basename(cssFile)}`);
      } catch (error) {
        console.warn(`  CSS压缩失败: ${cssFile}`, error.message);
      }
    }

    console.log('✅ 构建优化完成');
  }

  /**
   * 查找指定扩展名的文件
   */
  findFiles(dir, extension) {
    const files = [];
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const itemPath = path.join(dir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        files.push(...this.findFiles(itemPath, extension));
      } else if (item.endsWith(extension)) {
        files.push(itemPath);
      }
    }
    
    return files;
  }

  /**
   * 创建发布包
   */
  async createReleasePackage() {
    console.log('📦 创建发布包...');
    
    const zipPath = path.join(this.releaseDir, `ai-chat-memo-v${this.version}.zip`);
    
    return new Promise((resolve, reject) => {
      const output = fs.createWriteStream(zipPath);
      const archive = archiver('zip', { zlib: { level: 9 } });

      output.on('close', () => {
        const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
        console.log(`✅ 发布包创建完成: ${path.basename(zipPath)} (${sizeInMB}MB)`);
        resolve(zipPath);
      });

      archive.on('error', (error) => {
        console.error('❌ 创建发布包失败:', error);
        reject(error);
      });

      archive.pipe(output);
      archive.directory(this.distDir, false);
      archive.finalize();
    });
  }

  /**
   * 生成发布说明
   */
  generateReleaseNotes() {
    console.log('📝 生成发布说明...');
    
    const releaseNotes = `# AI Chat Memo v${this.version}

## 🚀 新功能

### 核心功能
- ✅ 自动检测并保存AI对话内容
- ✅ 支持6大主流AI平台（ChatGPT、Claude、Gemini、Aistudio、Monica、Poe）
- ✅ 智能标签系统，自动分类对话内容
- ✅ 强大的全文搜索功能
- ✅ 多格式导出（Markdown、PDF、JSON、HTML、CSV、TXT）
- ✅ 数据备份与恢复
- ✅ 跨设备数据同步

### 用户界面
- ✅ 现代化的Tailwind CSS设计
- ✅ 响应式布局，适配各种屏幕尺寸
- ✅ 直观的操作界面
- ✅ 实时状态指示器
- ✅ 悬浮保存提示

### 技术特性
- ✅ TypeScript + Vite构建
- ✅ Chrome Extension Manifest v3
- ✅ 高性能搜索引擎
- ✅ 智能缓存机制
- ✅ 数据压缩与加密
- ✅ 完整的错误处理

## 🔧 技术规格

- **支持浏览器**: Chrome 88+, Edge 88+, Firefox 89+
- **权限要求**: 
  - activeTab: 访问当前标签页
  - storage: 本地数据存储
  - scripting: 注入内容脚本
- **存储方式**: IndexedDB + Chrome Storage
- **数据格式**: JSON with compression
- **最大存储**: 无限制（受浏览器限制）

## 📋 安装说明

1. 下载 \`ai-chat-memo-v${this.version}.zip\`
2. 解压到本地目录
3. 打开Chrome浏览器，进入扩展程序页面 (chrome://extensions/)
4. 开启"开发者模式"
5. 点击"加载已解压的扩展程序"
6. 选择解压后的目录
7. 插件安装完成，开始使用！

## 🎯 使用指南

### 基本使用
1. 访问支持的AI平台（ChatGPT、Claude等）
2. 进行正常对话
3. 插件会自动检测并保存对话内容
4. 点击插件图标查看保存的对话

### 高级功能
- **搜索对话**: 在弹窗中使用搜索框快速找到历史对话
- **添加标签**: 为对话添加自定义标签，便于分类管理
- **导出数据**: 支持多种格式导出，方便分享和备份
- **数据同步**: 登录账户后可在多设备间同步数据

## 🐛 已知问题

- 部分动态加载的对话内容可能需要刷新页面才能检测
- 在某些网络环境下同步功能可能较慢
- PDF导出功能在某些复杂格式下可能出现样式问题

## 🔄 更新日志

### v${this.version} (${new Date().toISOString().split('T')[0]})
- 🎉 首次发布
- ✨ 实现所有核心功能
- 🔧 完成6大平台适配
- 📱 优化用户界面体验
- 🚀 性能优化和稳定性提升

## 📞 支持与反馈

- **GitHub**: https://github.com/your-username/ai-chat-memo
- **邮箱**: <EMAIL>
- **问题反馈**: 请在GitHub Issues中提交

## 📄 许可证

MIT License - 详见 LICENSE 文件

---

感谢使用 AI Chat Memo！🎉
`;

    const notesPath = path.join(this.releaseDir, 'RELEASE_NOTES.md');
    fs.writeFileSync(notesPath, releaseNotes);
    
    console.log(`✅ 发布说明已生成: ${notesPath}`);
  }

  /**
   * 生成商店提交材料
   */
  generateStoreAssets() {
    console.log('🏪 生成商店提交材料...');
    
    const storeDir = path.join(this.releaseDir, 'store-assets');
    fs.mkdirSync(storeDir, { recursive: true });

    // Chrome Web Store描述
    const chromeStoreDescription = `AI Chat Memo - 智能AI对话管理助手

🤖 自动保存和管理您的AI对话
支持ChatGPT、Claude、Gemini等主流AI平台，自动检测并保存对话内容，让您的AI学习历程井井有条。

✨ 主要功能：
• 自动检测AI对话并保存
• 智能标签分类系统
• 强大的全文搜索功能
• 多格式导出（Markdown、PDF等）
• 数据备份与跨设备同步
• 现代化用户界面

🎯 支持平台：
• ChatGPT (OpenAI)
• Claude (Anthropic)
• Gemini (Google)
• Aistudio (百度)
• Monica
• Poe

🔒 隐私安全：
• 所有数据本地存储
• 可选云端同步
• 完全开源透明

立即安装，让您的AI对话管理更加高效！`;

    fs.writeFileSync(
      path.join(storeDir, 'chrome-store-description.txt'),
      chromeStoreDescription
    );

    // Firefox Add-ons描述
    const firefoxDescription = `AI Chat Memo - 智能AI对话管理助手

自动保存和管理您在各大AI平台的对话记录，支持搜索、标签、导出等功能。

主要特性：
- 支持ChatGPT、Claude、Gemini等主流AI平台
- 自动检测并保存对话内容
- 智能标签和全文搜索
- 多格式导出功能
- 数据备份与同步
- 隐私安全，本地存储

开源项目，完全免费使用。`;

    fs.writeFileSync(
      path.join(storeDir, 'firefox-description.txt'),
      firefoxDescription
    );

    // 权限说明
    const permissionsExplanation = `权限说明：

1. activeTab - 访问当前活动标签页
   用途：检测AI对话页面并提取对话内容

2. storage - 本地存储
   用途：保存对话数据、用户设置和缓存

3. scripting - 脚本注入
   用途：在AI平台页面注入内容检测脚本

所有权限仅用于插件核心功能，不会收集或上传任何个人信息。`;

    fs.writeFileSync(
      path.join(storeDir, 'permissions-explanation.txt'),
      permissionsExplanation
    );

    console.log(`✅ 商店提交材料已生成: ${storeDir}`);
  }

  /**
   * 执行完整的发布构建流程
   */
  async buildRelease() {
    console.log(`🚀 开始构建 AI Chat Memo v${this.version} 发布版本`);
    console.log('='.repeat(60));

    try {
      // 1. 清理目录
      this.cleanDirectories();

      // 2. 构建项目
      this.buildProject();

      // 3. 验证构建结果
      this.validateBuild();

      // 4. 优化构建文件
      this.optimizeBuild();

      // 5. 创建发布包
      const zipPath = await this.createReleasePackage();

      // 6. 生成发布说明
      this.generateReleaseNotes();

      // 7. 生成商店提交材料
      this.generateStoreAssets();

      console.log('\n🎉 发布构建完成！');
      console.log('='.repeat(60));
      console.log(`📦 发布包: ${zipPath}`);
      console.log(`📁 发布目录: ${this.releaseDir}`);
      console.log(`🔢 版本号: v${this.version}`);
      console.log('\n📋 下一步：');
      console.log('1. 测试发布包功能');
      console.log('2. 提交到Chrome Web Store');
      console.log('3. 提交到Firefox Add-ons');
      console.log('4. 更新GitHub Release');

    } catch (error) {
      console.error('\n❌ 发布构建失败:', error.message);
      process.exit(1);
    }
  }
}

// 主函数
async function main() {
  const builder = new ReleaseBuildManager();
  await builder.buildRelease();
}

// 运行构建
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 构建失败:', error);
    process.exit(1);
  });
}

module.exports = ReleaseBuildManager;
