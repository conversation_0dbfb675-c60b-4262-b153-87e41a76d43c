/* AI Chat Memo Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  width: 380px;
  min-height: 500px;
  background: #ffffff;
  color: #374151;
}

.container {
  padding: 20px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  width: 24px;
  height: 24px;
  background: #3b82f6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.status-section {
  margin-bottom: 20px;
}

.status-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
}

.status-indicator.error {
  background: #ef4444;
}

.status-indicator.warning {
  background: #f59e0b;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

.platform-info {
  font-size: 12px;
  color: #6b7280;
}

.stats-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #1f2937;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  text-align: center;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  display: block;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.actions-section {
  margin-bottom: 20px;
}

.action-button {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #ffffff;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.action-button:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.action-button.primary {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.action-button.primary:hover {
  background: #2563eb;
}

.action-icon {
  font-size: 16px;
}

.recent-section {
  margin-bottom: 20px;
}

.conversation-item {
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.conversation-item:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.conversation-title {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.conversation-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #6b7280;
}

.platform-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

.platform-badge.chatgpt {
  background: #d1fae5;
  color: #065f46;
}

.platform-badge.claude {
  background: #fef3c7;
  color: #92400e;
}

.platform-badge.gemini {
  background: #dbeafe;
  color: #1e40af;
}

.footer {
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.footer-link {
  font-size: 12px;
  color: #6b7280;
  text-decoration: none;
  cursor: pointer;
}

.footer-link:hover {
  color: #374151;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6b7280;
}

.error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 16px;
}

.hidden {
  display: none;
}

/* 动画 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.spinning {
  animation: spin 1s linear infinite;
}

/* 响应式 */
@media (max-width: 400px) {
  body {
    width: 320px;
  }
  
  .container {
    padding: 16px;
  }
}
