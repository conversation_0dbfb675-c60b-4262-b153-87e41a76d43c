const fs = require('fs');
const path = require('path');

console.log('Starting import fix process for unminified code...');

// 读取chunk文件内容
function readChunkFiles() {
  const chunksDir = path.join(__dirname, '../dist/chunks');
  const chunks = {};

  if (fs.existsSync(chunksDir)) {
    const chunkFiles = fs.readdirSync(chunksDir).filter(f => f.endsWith('.js') && !f.endsWith('.map'));
    chunkFiles.forEach(file => {
      const content = fs.readFileSync(path.join(chunksDir, file), 'utf8');
      chunks[file] = content;
      console.log(`Read chunk file: ${file}`);
    });
  }

  return chunks;
}

// 提取chunk中的类和常量定义
function extractDefinitions(chunkContent) {
  const definitions = [];

  // 提取类定义
  const classMatches = chunkContent.match(/class\s+\w+\s*\{[\s\S]*?\n\}/g) || [];
  definitions.push(...classMatches);

  // 提取常量定义
  const constMatches = chunkContent.match(/const\s+\w+\s*=\s*\{[\s\S]*?\};/g) || [];
  definitions.push(...constMatches);

  // 提取函数定义
  const functionMatches = chunkContent.match(/function\s+\w+\s*\([^)]*\)\s*\{[\s\S]*?\n\}/g) || [];
  definitions.push(...functionMatches);

  return definitions.join('\n\n');
}

// 处理单个文件
function fixFile(filePath, fileName, chunks) {
  console.log(`Processing ${fileName}...`);

  if (!fs.existsSync(filePath)) {
    console.error(`${fileName} not found`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  console.log(`Original ${fileName} starts with:`, content.substring(0, 150));

  // 收集所有chunk中的定义
  let allDefinitions = '';
  Object.values(chunks).forEach(chunkContent => {
    const definitions = extractDefinitions(chunkContent);
    if (definitions) {
      allDefinitions += definitions + '\n\n';
    }
  });

  // 移除所有类型的import语句 (适用于未混淆代码)
  const importPatterns = [
    /import\s*\{[^}]*\}\s*from\s*["'][^"']*["']\s*;?\s*/g,
    /import\s+\*\s+as\s+\w+\s+from\s*["'][^"']*["']\s*;?\s*/g,
    /import\s+\w+\s+from\s*["'][^"']*["']\s*;?\s*/g,
    /import\s*["'][^"']*["']\s*;?\s*/g
  ];

  let totalImports = 0;
  importPatterns.forEach(pattern => {
    const matches = content.match(pattern) || [];
    if (matches.length > 0) {
      console.log(`Found ${matches.length} import statements with pattern:`, matches);
      content = content.replace(pattern, '');
      totalImports += matches.length;
    }
  });

  if (totalImports > 0) {
    console.log(`Total ${totalImports} import statements removed from ${fileName}`);
    console.log(`After removing imports, ${fileName} starts with:`, content.substring(0, 150));
  } else {
    console.log(`No import statements found in ${fileName}`);
  }

  // 检查是否已经被IIFE包装
  const isAlreadyWrapped = content.trim().startsWith('(function()') || content.trim().startsWith('(function ()');

  if (!isAlreadyWrapped) {
    // 将依赖定义添加到文件开头，然后包装在IIFE中
    const wrappedContent = `(function() {
  'use strict';

  // === 依赖定义 ===
  ${allDefinitions}

  // === 主要代码 ===
  ${content}
})();`;

    fs.writeFileSync(filePath, wrappedContent);
    console.log(`${fileName} wrapped in IIFE with dependencies successfully`);
  } else {
    console.log(`${fileName} is already wrapped in IIFE, skipping wrapper`);
    fs.writeFileSync(filePath, content);
  }

  return true;
}

// 读取chunk文件
const chunks = readChunkFiles();

// 处理所有需要的文件
const distDir = path.join(__dirname, '../dist');
const files = [
  { path: path.join(distDir, 'background.js'), name: 'background.js' },
  { path: path.join(distDir, 'content.js'), name: 'content.js' },
  { path: path.join(distDir, 'popup.js'), name: 'popup.js' },
  { path: path.join(distDir, 'options.js'), name: 'options.js' },
  { path: path.join(distDir, 'debug.js'), name: 'debug.js' }
];

files.forEach(file => {
  fixFile(file.path, file.name, chunks);
});

console.log('Import fix process completed');
