/**
 * 数据备份恢复管理器
 * 实现数据的备份和恢复功能，包括自动备份、手动备份和数据迁移
 */

import { ConversationData } from '@types/conversation';
import { Logger } from '@utils/logger';
import { EventEmitter } from '@utils/event-emitter';

export interface BackupConfig {
  autoBackup: boolean;
  backupInterval: number; // 小时
  maxBackups: number;
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
  encryptionKey?: string;
  backupLocation: 'local' | 'download' | 'cloud';
}

export interface BackupMetadata {
  id: string;
  timestamp: Date;
  version: string;
  conversationCount: number;
  messageCount: number;
  size: number;
  compressed: boolean;
  encrypted: boolean;
  checksum: string;
  description?: string;
}

export interface BackupData {
  metadata: BackupMetadata;
  conversations: ConversationData[];
  settings?: any;
  tags?: string[];
  statistics?: any;
}

export interface RestoreOptions {
  backupId: string;
  mergeMode: 'replace' | 'merge' | 'skip-existing';
  restoreSettings: boolean;
  restoreTags: boolean;
  restoreStatistics: boolean;
}

export interface RestoreResult {
  success: boolean;
  restoredConversations: number;
  skippedConversations: number;
  errors: string[];
  duration: number;
}

export class BackupManager extends EventEmitter {
  private logger: Logger;
  private config: BackupConfig;
  private backupTimer?: NodeJS.Timeout;
  private backups: Map<string, BackupMetadata> = new Map();

  constructor(config: BackupConfig) {
    super();
    this.logger = new Logger('BackupManager');
    this.config = config;

    if (config.autoBackup) {
      this.startAutoBackup();
    }

    this.loadBackupList();
  }

  /**
   * 创建备份
   */
  async createBackup(
    conversations: ConversationData[],
    description?: string,
    includeSettings: boolean = true
  ): Promise<BackupMetadata> {
    this.logger.info('开始创建备份', {
      conversationCount: conversations.length,
      description
    });

    const startTime = Date.now();

    try {
      // 生成备份ID
      const backupId = this.generateBackupId();
      
      // 计算统计信息
      const messageCount = conversations.reduce((sum, conv) => sum + conv.messages.length, 0);

      // 准备备份数据
      const backupData: BackupData = {
        metadata: {
          id: backupId,
          timestamp: new Date(),
          version: '1.0',
          conversationCount: conversations.length,
          messageCount,
          size: 0, // 将在序列化后计算
          compressed: this.config.compressionEnabled,
          encrypted: this.config.encryptionEnabled,
          checksum: '',
          description
        },
        conversations,
        settings: includeSettings ? await this.getSettings() : undefined,
        tags: await this.getAllTags(conversations),
        statistics: await this.getStatistics(conversations)
      };

      // 序列化数据
      let serializedData = JSON.stringify(backupData);

      // 压缩数据
      if (this.config.compressionEnabled) {
        serializedData = await this.compressData(serializedData);
      }

      // 加密数据
      if (this.config.encryptionEnabled && this.config.encryptionKey) {
        serializedData = await this.encryptData(serializedData, this.config.encryptionKey);
      }

      // 计算校验和
      const checksum = await this.calculateChecksum(serializedData);
      backupData.metadata.checksum = checksum;
      backupData.metadata.size = serializedData.length;

      // 保存备份
      await this.saveBackup(backupId, serializedData, backupData.metadata);

      // 更新备份列表
      this.backups.set(backupId, backupData.metadata);

      // 清理旧备份
      await this.cleanupOldBackups();

      const duration = Date.now() - startTime;
      this.logger.info('备份创建成功', {
        backupId,
        size: backupData.metadata.size,
        duration
      });

      this.emit('backupCreated', backupData.metadata);
      return backupData.metadata;

    } catch (error) {
      this.logger.error('备份创建失败:', error);
      this.emit('backupError', error);
      throw error;
    }
  }

  /**
   * 恢复备份
   */
  async restoreBackup(options: RestoreOptions): Promise<RestoreResult> {
    this.logger.info('开始恢复备份', options);

    const startTime = Date.now();
    let restoredConversations = 0;
    let skippedConversations = 0;
    const errors: string[] = [];

    try {
      // 加载备份数据
      const backupData = await this.loadBackup(options.backupId);
      if (!backupData) {
        throw new Error(`备份不存在: ${options.backupId}`);
      }

      // 验证备份完整性
      const isValid = await this.validateBackup(backupData);
      if (!isValid) {
        throw new Error('备份数据损坏或校验失败');
      }

      // 恢复对话数据
      for (const conversation of backupData.conversations) {
        try {
          const shouldRestore = await this.shouldRestoreConversation(conversation, options.mergeMode);
          
          if (shouldRestore) {
            await this.restoreConversation(conversation, options.mergeMode);
            restoredConversations++;
          } else {
            skippedConversations++;
          }
        } catch (error) {
          const errorMsg = `恢复对话失败 ${conversation.id}: ${error}`;
          errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      // 恢复设置
      if (options.restoreSettings && backupData.settings) {
        try {
          await this.restoreSettings(backupData.settings);
        } catch (error) {
          errors.push(`恢复设置失败: ${error}`);
        }
      }

      // 恢复标签
      if (options.restoreTags && backupData.tags) {
        try {
          await this.restoreTags(backupData.tags);
        } catch (error) {
          errors.push(`恢复标签失败: ${error}`);
        }
      }

      // 恢复统计数据
      if (options.restoreStatistics && backupData.statistics) {
        try {
          await this.restoreStatistics(backupData.statistics);
        } catch (error) {
          errors.push(`恢复统计数据失败: ${error}`);
        }
      }

      const result: RestoreResult = {
        success: errors.length === 0,
        restoredConversations,
        skippedConversations,
        errors,
        duration: Date.now() - startTime
      };

      this.logger.info('备份恢复完成', result);
      this.emit('backupRestored', result);

      return result;

    } catch (error) {
      const result: RestoreResult = {
        success: false,
        restoredConversations,
        skippedConversations,
        errors: [error instanceof Error ? error.message : '恢复失败'],
        duration: Date.now() - startTime
      };

      this.logger.error('备份恢复失败:', error);
      this.emit('restoreError', error);
      return result;
    }
  }

  /**
   * 获取备份列表
   */
  getBackupList(): BackupMetadata[] {
    return Array.from(this.backups.values())
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * 删除备份
   */
  async deleteBackup(backupId: string): Promise<void> {
    this.logger.info('删除备份', { backupId });

    try {
      await this.removeBackupFile(backupId);
      this.backups.delete(backupId);
      await this.saveBackupList();

      this.emit('backupDeleted', backupId);
      this.logger.info('备份删除成功', { backupId });

    } catch (error) {
      this.logger.error('备份删除失败:', error);
      throw error;
    }
  }

  /**
   * 导出备份文件
   */
  async exportBackup(backupId: string): Promise<Blob> {
    this.logger.info('导出备份文件', { backupId });

    try {
      const backupData = await this.loadBackupRaw(backupId);
      if (!backupData) {
        throw new Error(`备份不存在: ${backupId}`);
      }

      const metadata = this.backups.get(backupId);
      const filename = `ai-chat-backup-${backupId}-${Date.now()}.json`;
      
      const blob = new Blob([backupData], { type: 'application/json' });
      
      this.logger.info('备份导出成功', { backupId, size: blob.size });
      return blob;

    } catch (error) {
      this.logger.error('备份导出失败:', error);
      throw error;
    }
  }

  /**
   * 导入备份文件
   */
  async importBackup(file: File): Promise<BackupMetadata> {
    this.logger.info('导入备份文件', { filename: file.name, size: file.size });

    try {
      const content = await this.readFileContent(file);
      const backupData = JSON.parse(content) as BackupData;

      // 验证备份格式
      if (!this.isValidBackupFormat(backupData)) {
        throw new Error('无效的备份文件格式');
      }

      // 生成新的备份ID（避免冲突）
      const newBackupId = this.generateBackupId();
      backupData.metadata.id = newBackupId;

      // 保存导入的备份
      await this.saveBackup(newBackupId, content, backupData.metadata);
      this.backups.set(newBackupId, backupData.metadata);

      this.logger.info('备份导入成功', { backupId: newBackupId });
      this.emit('backupImported', backupData.metadata);

      return backupData.metadata;

    } catch (error) {
      this.logger.error('备份导入失败:', error);
      throw error;
    }
  }

  /**
   * 开始自动备份
   */
  startAutoBackup(): void {
    if (this.backupTimer) {
      clearInterval(this.backupTimer);
    }

    const intervalMs = this.config.backupInterval * 60 * 60 * 1000; // 转换为毫秒
    this.backupTimer = setInterval(() => {
      this.emit('autoBackupTriggered');
    }, intervalMs);

    this.logger.info('自动备份已启动', { intervalHours: this.config.backupInterval });
  }

  /**
   * 停止自动备份
   */
  stopAutoBackup(): void {
    if (this.backupTimer) {
      clearInterval(this.backupTimer);
      this.backupTimer = undefined;
    }
    this.logger.info('自动备份已停止');
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<BackupConfig>): void {
    this.config = { ...this.config, ...newConfig };

    if (newConfig.autoBackup !== undefined) {
      if (newConfig.autoBackup) {
        this.startAutoBackup();
      } else {
        this.stopAutoBackup();
      }
    }

    if (newConfig.backupInterval !== undefined && this.config.autoBackup) {
      this.startAutoBackup(); // 重启定时器
    }

    this.emit('configUpdated', this.config);
  }

  /**
   * 获取配置
   */
  getConfig(): BackupConfig {
    return { ...this.config };
  }

  /**
   * 生成备份ID
   */
  private generateBackupId(): string {
    return `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取设置
   */
  private async getSettings(): Promise<any> {
    // 这里应该从实际的设置存储中获取
    return {
      theme: 'light',
      language: 'zh-CN',
      autoSave: true
    };
  }

  /**
   * 获取所有标签
   */
  private async getAllTags(conversations: ConversationData[]): Promise<string[]> {
    const tagSet = new Set<string>();

    conversations.forEach(conv => {
      if (conv.metadata?.tags) {
        conv.metadata.tags.forEach(tag => tagSet.add(tag));
      }
    });

    return Array.from(tagSet);
  }

  /**
   * 获取统计数据
   */
  private async getStatistics(conversations: ConversationData[]): Promise<any> {
    const totalMessages = conversations.reduce((sum, conv) => sum + conv.messages.length, 0);
    const platforms = new Set(conversations.map(conv => conv.platform));

    return {
      totalConversations: conversations.length,
      totalMessages,
      platforms: Array.from(platforms),
      createdAt: new Date().toISOString()
    };
  }

  /**
   * 压缩数据
   */
  private async compressData(data: string): Promise<string> {
    // 简化的压缩实现，实际项目中可以使用 pako 或其他压缩库
    try {
      const encoder = new TextEncoder();
      const decoder = new TextDecoder();
      const compressed = encoder.encode(data);
      return btoa(String.fromCharCode(...compressed));
    } catch (error) {
      this.logger.warn('数据压缩失败，使用原始数据', error);
      return data;
    }
  }

  /**
   * 解压数据
   */
  private async decompressData(compressedData: string): Promise<string> {
    try {
      const binaryString = atob(compressedData);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      const decoder = new TextDecoder();
      return decoder.decode(bytes);
    } catch (error) {
      this.logger.warn('数据解压失败，尝试使用原始数据', error);
      return compressedData;
    }
  }

  /**
   * 加密数据
   */
  private async encryptData(data: string, key: string): Promise<string> {
    // 简化的加密实现，实际项目中应该使用更安全的加密算法
    try {
      const encoder = new TextEncoder();
      const keyBytes = encoder.encode(key);
      const dataBytes = encoder.encode(data);

      // 简单的XOR加密（仅用于演示）
      const encrypted = new Uint8Array(dataBytes.length);
      for (let i = 0; i < dataBytes.length; i++) {
        encrypted[i] = dataBytes[i] ^ keyBytes[i % keyBytes.length];
      }

      return btoa(String.fromCharCode(...encrypted));
    } catch (error) {
      this.logger.warn('数据加密失败，使用原始数据', error);
      return data;
    }
  }

  /**
   * 解密数据
   */
  private async decryptData(encryptedData: string, key: string): Promise<string> {
    try {
      const binaryString = atob(encryptedData);
      const encrypted = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        encrypted[i] = binaryString.charCodeAt(i);
      }

      const encoder = new TextEncoder();
      const decoder = new TextDecoder();
      const keyBytes = encoder.encode(key);

      // 简单的XOR解密
      const decrypted = new Uint8Array(encrypted.length);
      for (let i = 0; i < encrypted.length; i++) {
        decrypted[i] = encrypted[i] ^ keyBytes[i % keyBytes.length];
      }

      return decoder.decode(decrypted);
    } catch (error) {
      this.logger.warn('数据解密失败，尝试使用原始数据', error);
      return encryptedData;
    }
  }

  /**
   * 计算校验和
   */
  private async calculateChecksum(data: string): Promise<string> {
    try {
      const encoder = new TextEncoder();
      const dataBytes = encoder.encode(data);
      const hashBuffer = await crypto.subtle.digest('SHA-256', dataBytes);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    } catch (error) {
      this.logger.warn('校验和计算失败', error);
      return '';
    }
  }

  /**
   * 保存备份
   */
  private async saveBackup(backupId: string, data: string, metadata: BackupMetadata): Promise<void> {
    try {
      // 保存到IndexedDB或其他存储
      const key = `backup_${backupId}`;
      await this.saveToStorage(key, data);
      await this.saveBackupList();

      this.logger.debug('备份保存成功', { backupId, size: data.length });
    } catch (error) {
      this.logger.error('备份保存失败:', error);
      throw error;
    }
  }

  /**
   * 加载备份
   */
  private async loadBackup(backupId: string): Promise<BackupData | null> {
    try {
      const rawData = await this.loadBackupRaw(backupId);
      if (!rawData) return null;

      const metadata = this.backups.get(backupId);
      if (!metadata) return null;

      let data = rawData;

      // 解密数据
      if (metadata.encrypted && this.config.encryptionKey) {
        data = await this.decryptData(data, this.config.encryptionKey);
      }

      // 解压数据
      if (metadata.compressed) {
        data = await this.decompressData(data);
      }

      return JSON.parse(data) as BackupData;

    } catch (error) {
      this.logger.error('备份加载失败:', error);
      return null;
    }
  }

  /**
   * 加载原始备份数据
   */
  private async loadBackupRaw(backupId: string): Promise<string | null> {
    try {
      const key = `backup_${backupId}`;
      return await this.loadFromStorage(key);
    } catch (error) {
      this.logger.error('原始备份数据加载失败:', error);
      return null;
    }
  }

  /**
   * 验证备份
   */
  private async validateBackup(backupData: BackupData): Promise<boolean> {
    try {
      // 验证基本结构
      if (!backupData.metadata || !backupData.conversations) {
        return false;
      }

      // 验证校验和
      if (backupData.metadata.checksum) {
        const currentChecksum = await this.calculateChecksum(JSON.stringify(backupData));
        if (currentChecksum !== backupData.metadata.checksum) {
          this.logger.warn('备份校验和不匹配');
          return false;
        }
      }

      return true;
    } catch (error) {
      this.logger.error('备份验证失败:', error);
      return false;
    }
  }

  /**
   * 判断是否应该恢复对话
   */
  private async shouldRestoreConversation(
    conversation: ConversationData,
    mergeMode: 'replace' | 'merge' | 'skip-existing'
  ): Promise<boolean> {
    // 这里应该检查本地是否已存在该对话
    const exists = await this.conversationExists(conversation.id);

    switch (mergeMode) {
      case 'replace':
        return true;
      case 'skip-existing':
        return !exists;
      case 'merge':
        return true;
      default:
        return true;
    }
  }

  /**
   * 恢复对话
   */
  private async restoreConversation(
    conversation: ConversationData,
    mergeMode: 'replace' | 'merge' | 'skip-existing'
  ): Promise<void> {
    // 这里应该调用实际的数据存储服务
    this.emit('conversationRestored', conversation);
  }

  /**
   * 恢复设置
   */
  private async restoreSettings(settings: any): Promise<void> {
    // 这里应该调用实际的设置服务
    this.emit('settingsRestored', settings);
  }

  /**
   * 恢复标签
   */
  private async restoreTags(tags: string[]): Promise<void> {
    // 这里应该调用实际的标签服务
    this.emit('tagsRestored', tags);
  }

  /**
   * 恢复统计数据
   */
  private async restoreStatistics(statistics: any): Promise<void> {
    // 这里应该调用实际的统计服务
    this.emit('statisticsRestored', statistics);
  }

  /**
   * 检查对话是否存在
   */
  private async conversationExists(conversationId: string): Promise<boolean> {
    // 这里应该检查实际的数据存储
    // 暂时返回false，表示不存在
    return false;
  }

  /**
   * 清理旧备份
   */
  private async cleanupOldBackups(): Promise<void> {
    const backupList = this.getBackupList();

    if (backupList.length <= this.config.maxBackups) {
      return;
    }

    // 删除超出限制的旧备份
    const toDelete = backupList.slice(this.config.maxBackups);

    for (const backup of toDelete) {
      try {
        await this.deleteBackup(backup.id);
        this.logger.debug('清理旧备份', { backupId: backup.id });
      } catch (error) {
        this.logger.error('清理旧备份失败:', error);
      }
    }
  }

  /**
   * 加载备份列表
   */
  private async loadBackupList(): Promise<void> {
    try {
      const listData = await this.loadFromStorage('backup_list');
      if (listData) {
        const backupArray = JSON.parse(listData) as BackupMetadata[];
        this.backups.clear();

        backupArray.forEach(backup => {
          // 确保日期对象正确
          backup.timestamp = new Date(backup.timestamp);
          this.backups.set(backup.id, backup);
        });

        this.logger.debug('备份列表加载成功', { count: this.backups.size });
      }
    } catch (error) {
      this.logger.error('备份列表加载失败:', error);
    }
  }

  /**
   * 保存备份列表
   */
  private async saveBackupList(): Promise<void> {
    try {
      const backupArray = Array.from(this.backups.values());
      const listData = JSON.stringify(backupArray);
      await this.saveToStorage('backup_list', listData);

      this.logger.debug('备份列表保存成功', { count: backupArray.length });
    } catch (error) {
      this.logger.error('备份列表保存失败:', error);
    }
  }

  /**
   * 删除备份文件
   */
  private async removeBackupFile(backupId: string): Promise<void> {
    try {
      const key = `backup_${backupId}`;
      await this.removeFromStorage(key);
      this.logger.debug('备份文件删除成功', { backupId });
    } catch (error) {
      this.logger.error('备份文件删除失败:', error);
      throw error;
    }
  }

  /**
   * 读取文件内容
   */
  private async readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (event) => {
        if (event.target?.result) {
          resolve(event.target.result as string);
        } else {
          reject(new Error('文件读取失败'));
        }
      };

      reader.onerror = () => {
        reject(new Error('文件读取错误'));
      };

      reader.readAsText(file);
    });
  }

  /**
   * 验证备份格式
   */
  private isValidBackupFormat(data: any): data is BackupData {
    return (
      data &&
      typeof data === 'object' &&
      data.metadata &&
      typeof data.metadata === 'object' &&
      data.conversations &&
      Array.isArray(data.conversations) &&
      typeof data.metadata.id === 'string' &&
      typeof data.metadata.version === 'string' &&
      typeof data.metadata.conversationCount === 'number'
    );
  }

  /**
   * 保存到存储
   */
  private async saveToStorage(key: string, data: string): Promise<void> {
    try {
      // 使用IndexedDB或Chrome存储
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.set({ [key]: data });
      } else {
        // 降级到localStorage
        localStorage.setItem(key, data);
      }
    } catch (error) {
      this.logger.error('存储保存失败:', error);
      throw error;
    }
  }

  /**
   * 从存储加载
   */
  private async loadFromStorage(key: string): Promise<string | null> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get([key]);
        return result[key] || null;
      } else {
        // 降级到localStorage
        return localStorage.getItem(key);
      }
    } catch (error) {
      this.logger.error('存储加载失败:', error);
      return null;
    }
  }

  /**
   * 从存储删除
   */
  private async removeFromStorage(key: string): Promise<void> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.remove([key]);
      } else {
        // 降级到localStorage
        localStorage.removeItem(key);
      }
    } catch (error) {
      this.logger.error('存储删除失败:', error);
      throw error;
    }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.stopAutoBackup();
    this.removeAllListeners();
    this.backups.clear();
    this.logger.info('备份管理器已销毁');
  }

  /**
   * 获取备份统计信息
   */
  getBackupStats(): {
    totalBackups: number;
    totalSize: number;
    oldestBackup?: Date;
    newestBackup?: Date;
    averageSize: number;
  } {
    const backupList = this.getBackupList();

    if (backupList.length === 0) {
      return {
        totalBackups: 0,
        totalSize: 0,
        averageSize: 0
      };
    }

    const totalSize = backupList.reduce((sum, backup) => sum + backup.size, 0);
    const timestamps = backupList.map(backup => backup.timestamp);

    return {
      totalBackups: backupList.length,
      totalSize,
      oldestBackup: new Date(Math.min(...timestamps.map(t => t.getTime()))),
      newestBackup: new Date(Math.max(...timestamps.map(t => t.getTime()))),
      averageSize: Math.round(totalSize / backupList.length)
    };
  }
}
