/**
 * 简单的消息总线实现
 */

export class MessageBus {
  private source: string;
  private listeners: Map<string, Function[]> = new Map();

  constructor(source: string) {
    this.source = source;
  }

  on(event: string, listener: Function): () => void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);

    // 返回取消订阅函数
    return () => {
      this.off(event, listener);
    };
  }

  off(event: string, listener: Function): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event: string, data?: any): Promise<void> {
    const listeners = this.listeners.get(event);
    if (listeners) {
      const promises = listeners.map(listener => {
        try {
          return Promise.resolve(listener(data));
        } catch (error) {
          return Promise.reject(error);
        }
      });
      return Promise.all(promises).then(() => {});
    }
    return Promise.resolve();
  }

  async send(event: string, data?: any): Promise<any> {
    // 简化实现，实际应该通过Chrome API发送消息
    return Promise.resolve({ success: true, data: null });
  }

  /**
   * 发送消息到background script
   */
  async sendMessage(message: { type: string; data?: any }, retries: number = 3): Promise<any> {
    return new Promise((resolve, reject) => {
      if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
        const attemptSend = (attempt: number) => {
          chrome.runtime.sendMessage(message, (response) => {
            if (chrome.runtime.lastError) {
              const error = chrome.runtime.lastError.message;
              console.warn(`[MessageBus] 发送消息失败 (尝试 ${attempt}/${retries + 1}):`, error);

              // 如果是连接错误且还有重试次数，则重试
              if (error.includes('Could not establish connection') && attempt < retries) {
                setTimeout(() => attemptSend(attempt + 1), 100 * attempt); // 递增延迟
              } else {
                reject(new Error(error));
              }
            } else {
              resolve(response || { success: false, error: 'No response' });
            }
          });
        };

        attemptSend(1);
      } else {
        // 如果不在Chrome扩展环境中，返回模拟响应
        resolve({ success: false, error: 'Chrome runtime not available' });
      }
    });
  }

  destroy(): void {
    this.listeners.clear();
  }
}
