import { defineConfig } from 'vite';
import { resolve } from 'path';
import { viteStaticCopy } from 'vite-plugin-static-copy';

export default defineConfig({
  plugins: [
    viteStaticCopy({
      targets: [
        {
          src: 'manifest.json',
          dest: '.'
        },
        {
          src: 'src/popup/popup.html',
          dest: '.'
        },
        {
          src: 'src/options/options.html',
          dest: '.'
        },
        {
          src: 'src/content/content.css',
          dest: '.'
        },
        {
          src: 'src/popup/popup.css',
          dest: '.'
        },
        {
          src: 'src/options/options.css',
          dest: '.'
        },
        {
          src: 'src/debug/debug.html',
          dest: '.'
        },
        {
          src: 'public/icons',
          dest: '.'
        }
      ]
    })
  ],
  
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    sourcemap: true,  // 强制开启源码映射
    minify: false,    // 强制关闭混淆

    rollupOptions: {
      input: {
        background: resolve(__dirname, 'src/background/index.ts'),
        content: resolve(__dirname, 'src/content/index.ts'),
        popup: resolve(__dirname, 'src/popup/index.ts'),
        options: resolve(__dirname, 'src/options/index.ts'),
        debug: resolve(__dirname, 'src/debug/debug.ts')
      },

      output: {
        entryFileNames: '[name].js',
        chunkFileNames: 'chunks/[name]-[hash].js',
        assetFileNames: '[name].[ext]',  // 简化资源文件名
        format: 'es',  // 使用ES模块格式，后续用脚本转换
        globals: {
          chrome: 'chrome'
        }
      },

      // 外部依赖配置
      external: ['chrome']
    },

    // 目标环境
    target: 'es2020',

    // 构建优化
    chunkSizeWarningLimit: 1000
  },
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@types': resolve(__dirname, 'src/types'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@content': resolve(__dirname, 'src/content'),
      '@background': resolve(__dirname, 'src/background'),
      '@popup': resolve(__dirname, 'src/popup'),
      '@options': resolve(__dirname, 'src/options'),
      '@shared': resolve(__dirname, 'src/shared'),
      '@storage': resolve(__dirname, 'src/storage'),
      '@conversation': resolve(__dirname, 'src/conversation'),
      '@tags': resolve(__dirname, 'src/tags')
    }
  },
  
  // 开发服务器配置
  server: {
    port: 3000,
    open: false
  },
  
  // 环境变量
  define: {
    __DEV__: process.env.NODE_ENV === 'development',
    __PROD__: process.env.NODE_ENV === 'production',
    __VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0')
  }
});
