/* AI Chat Memo Content Script Styles */

/* 浮动指示器样式 */
.ai-chat-memo-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  background: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
  max-width: 280px;
  backdrop-filter: blur(10px);
}

.ai-chat-memo-indicator:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  border-color: #3b82f6;
}

.ai-chat-memo-indicator.saving {
  border-color: #f59e0b;
  background: #fef3c7;
}

.ai-chat-memo-indicator.saved {
  border-color: #10b981;
  background: #d1fae5;
}

.ai-chat-memo-indicator.error {
  border-color: #ef4444;
  background: #fee2e2;
}

/* 指示器图标 */
.ai-chat-memo-indicator .icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  vertical-align: middle;
}

.ai-chat-memo-indicator .icon.saving {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 状态文本 */
.ai-chat-memo-indicator .status-text {
  font-weight: 500;
}

.ai-chat-memo-indicator .platform-text {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

/* 工具提示 */
.ai-chat-memo-tooltip {
  position: absolute;
  bottom: 100%;
  right: 0;
  margin-bottom: 8px;
  padding: 8px 12px;
  background: #1f2937;
  color: #ffffff;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
}

.ai-chat-memo-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  right: 12px;
  border: 4px solid transparent;
  border-top-color: #1f2937;
}

.ai-chat-memo-indicator:hover .ai-chat-memo-tooltip {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-chat-memo-indicator {
    top: 10px;
    right: 10px;
    padding: 8px 12px;
    font-size: 12px;
    max-width: 200px;
  }
}

/* 确保不与页面元素冲突 */
.ai-chat-memo-indicator * {
  box-sizing: border-box;
}

/* 隐藏状态 */
.ai-chat-memo-indicator.hidden {
  opacity: 0;
  pointer-events: none;
  transform: translateY(-10px);
}

/* 动画进入 */
.ai-chat-memo-indicator.entering {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 调试模式样式 */
.ai-chat-memo-debug {
  position: fixed;
  bottom: 20px;
  left: 20px;
  z-index: 9999;
  background: #000000;
  color: #00ff00;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 11px;
  max-width: 300px;
  max-height: 200px;
  overflow-y: auto;
  opacity: 0.8;
}

.ai-chat-memo-debug .log-entry {
  margin-bottom: 2px;
  word-break: break-all;
}

.ai-chat-memo-debug .log-entry.error {
  color: #ff4444;
}

.ai-chat-memo-debug .log-entry.warn {
  color: #ffaa00;
}

.ai-chat-memo-debug .log-entry.info {
  color: #00aaff;
}
