/**
 * 数据管理系统集成测试
 */

import { SearchEngine } from '../../src/core/search/search-engine';
import { ExportManager } from '../../src/core/export/export-manager';
import { SyncManager } from '../../src/core/sync/sync-manager';
import { BackupManager } from '../../src/core/backup/backup-manager';
import { ConversationData } from '../../src/types/conversation';

describe('数据管理系统集成测试', () => {
  let searchEngine: SearchEngine;
  let exportManager: ExportManager;
  let syncManager: SyncManager;
  let backupManager: BackupManager;
  let testConversations: ConversationData[];

  beforeEach(async () => {
    // 初始化所有管理器
    searchEngine = new SearchEngine();
    exportManager = new ExportManager();
    
    syncManager = new SyncManager({
      provider: 'chrome-storage',
      enabled: true,
      autoSync: false,
      syncInterval: 60,
      settings: {
        conflictResolution: 'local',
        maxRetries: 3,
        timeout: 30000
      }
    });

    backupManager = new BackupManager({
      autoBackup: false,
      backupInterval: 24,
      maxBackups: 10,
      compression: true,
      encryption: false
    });

    // 创建测试数据
    testConversations = [
      {
        id: 'conv_001',
        platform: 'ChatGPT',
        title: 'JavaScript异步编程深度讨论',
        url: 'https://chat.openai.com/c/123',
        timestamp: new Date('2024-01-15T10:30:00Z'),
        messages: [
          {
            id: 'msg_001',
            role: 'user',
            content: '请详细解释JavaScript中Promise、async/await和Generator的区别和使用场景',
            timestamp: new Date('2024-01-15T10:30:00Z')
          },
          {
            id: 'msg_002',
            role: 'assistant',
            content: 'Promise是ES6引入的异步编程解决方案，提供了更好的错误处理和链式调用。async/await是ES2017引入的语法糖，让异步代码看起来更像同步代码。Generator是ES6引入的函数，可以暂停和恢复执行。',
            timestamp: new Date('2024-01-15T10:31:00Z')
          },
          {
            id: 'msg_003',
            role: 'user',
            content: '能给个具体的代码示例吗？',
            timestamp: new Date('2024-01-15T10:32:00Z')
          },
          {
            id: 'msg_004',
            role: 'assistant',
            content: '当然可以！这里是一些示例代码：\n\n```javascript\n// Promise示例\nfetch("/api/data")\n  .then(response => response.json())\n  .then(data => console.log(data))\n  .catch(error => console.error(error));\n\n// async/await示例\nasync function fetchData() {\n  try {\n    const response = await fetch("/api/data");\n    const data = await response.json();\n    console.log(data);\n  } catch (error) {\n    console.error(error);\n  }\n}\n```',
            timestamp: new Date('2024-01-15T10:33:00Z')
          }
        ],
        metadata: {
          tags: ['JavaScript', '异步编程', 'Promise', 'async/await', 'Generator']
        }
      },
      {
        id: 'conv_002',
        platform: 'Claude',
        title: 'React性能优化最佳实践',
        url: 'https://claude.ai/chat/456',
        timestamp: new Date('2024-01-16T14:20:00Z'),
        messages: [
          {
            id: 'msg_005',
            role: 'user',
            content: '我的React应用在处理大量数据时变得很慢，有什么优化建议吗？',
            timestamp: new Date('2024-01-16T14:20:00Z')
          },
          {
            id: 'msg_006',
            role: 'assistant',
            content: 'React性能优化有多种策略：1. 使用React.memo避免不必要的重渲染 2. 使用useMemo和useCallback缓存计算结果和函数 3. 实现虚拟滚动处理大列表 4. 代码分割和懒加载 5. 使用React DevTools Profiler分析性能瓶颈',
            timestamp: new Date('2024-01-16T14:21:00Z')
          },
          {
            id: 'msg_007',
            role: 'user',
            content: '虚拟滚动是怎么实现的？',
            timestamp: new Date('2024-01-16T14:22:00Z')
          },
          {
            id: 'msg_008',
            role: 'assistant',
            content: '虚拟滚动的核心思想是只渲染可见区域的元素。可以使用react-window或react-virtualized库，或者自己实现：计算可见区域、动态渲染元素、维护滚动位置。',
            timestamp: new Date('2024-01-16T14:23:00Z')
          }
        ],
        metadata: {
          tags: ['React', '性能优化', '虚拟滚动', '前端开发']
        }
      },
      {
        id: 'conv_003',
        platform: 'Gemini',
        title: 'Python数据科学工具链',
        url: 'https://gemini.google.com/chat/789',
        timestamp: new Date('2024-01-17T09:15:00Z'),
        messages: [
          {
            id: 'msg_009',
            role: 'user',
            content: '我想学习Python数据科学，应该从哪些库开始？',
            timestamp: new Date('2024-01-17T09:15:00Z')
          },
          {
            id: 'msg_010',
            role: 'assistant',
            content: 'Python数据科学的核心库包括：NumPy（数值计算）、Pandas（数据处理）、Matplotlib/Seaborn（数据可视化）、Scikit-learn（机器学习）、Jupyter（交互式开发环境）。建议按这个顺序学习。',
            timestamp: new Date('2024-01-17T09:16:00Z')
          }
        ],
        metadata: {
          tags: ['Python', '数据科学', 'NumPy', 'Pandas', '机器学习']
        }
      }
    ];

    // 初始化同步管理器
    await syncManager.initialize();
  });

  afterEach(() => {
    searchEngine.clearCache();
    syncManager.destroy();
    backupManager.destroy();
  });

  describe('搜索与导出集成', () => {
    test('应该能够搜索并导出结果', async () => {
      // 构建搜索索引
      await searchEngine.buildIndex(testConversations);
      
      // 搜索JavaScript相关对话
      const searchResult = await searchEngine.search('JavaScript Promise');
      expect(searchResult.results).toHaveLength(1);
      expect(searchResult.results[0].conversation.id).toBe('conv_001');
      
      // 导出搜索结果
      const exportResult = await exportManager.exportConversations({
        format: 'markdown',
        conversations: searchResult.results.map(r => r.conversation),
        includeMetadata: true,
        includeTags: true
      });
      
      expect(exportResult.success).toBe(true);
      expect(exportResult.content).toContain('JavaScript异步编程深度讨论');
      expect(exportResult.content).toContain('Promise');
      expect(exportResult.conversationCount).toBe(1);
      expect(exportResult.messageCount).toBe(4);
    });

    test('应该能够按标签搜索并导出', async () => {
      await searchEngine.buildIndex(testConversations);
      
      // 搜索React相关内容
      const searchResult = await searchEngine.search('React 性能优化');
      expect(searchResult.results).toHaveLength(1);
      
      // 导出为JSON格式
      const exportResult = await exportManager.exportConversations({
        format: 'json',
        conversations: searchResult.results.map(r => r.conversation),
        includeMetadata: true
      });
      
      const jsonData = JSON.parse(exportResult.content!);
      expect(jsonData.conversations[0].metadata.tags).toContain('React');
      expect(jsonData.conversations[0].metadata.tags).toContain('性能优化');
    });
  });

  describe('搜索与备份集成', () => {
    test('应该能够搜索、备份和恢复数据', async () => {
      // 构建搜索索引
      await searchEngine.buildIndex(testConversations);
      
      // 创建备份
      const backupMetadata = await backupManager.createBackup(testConversations, '集成测试备份');
      expect(backupMetadata.conversationCount).toBe(3);
      expect(backupMetadata.messageCount).toBe(6);
      
      // 恢复备份
      const restoredData = await backupManager.restoreBackup(backupMetadata.id);
      expect(restoredData.conversations).toHaveLength(3);
      
      // 在恢复的数据上重建索引并搜索
      await searchEngine.buildIndex(restoredData.conversations);
      const searchResult = await searchEngine.search('Python 数据科学');
      
      expect(searchResult.results).toHaveLength(1);
      expect(searchResult.results[0].conversation.id).toBe('conv_003');
    });

    test('应该能够备份搜索结果', async () => {
      await searchEngine.buildIndex(testConversations);
      
      // 搜索特定内容
      const searchResult = await searchEngine.search('优化');
      const searchedConversations = searchResult.results.map(r => r.conversation);
      
      // 备份搜索结果
      const backupMetadata = await backupManager.createBackup(
        searchedConversations, 
        '优化相关对话备份'
      );
      
      expect(backupMetadata.conversationCount).toBe(searchedConversations.length);
      
      // 验证备份内容
      const restoredData = await backupManager.restoreBackup(backupMetadata.id);
      expect(restoredData.conversations.every(conv => 
        conv.title.includes('优化') || 
        conv.messages.some(msg => msg.content.includes('优化'))
      )).toBe(true);
    });
  });

  describe('同步与备份集成', () => {
    test('应该能够同步数据并创建备份', async () => {
      // 模拟Chrome存储返回空数据
      (global as any).chrome.storage.sync.get.mockResolvedValue({ conversations: [] });
      (global as any).chrome.storage.sync.set.mockResolvedValue(undefined);
      
      // 执行同步
      const syncResult = await syncManager.sync(testConversations);
      expect(syncResult.success).toBe(true);
      expect(syncResult.syncedCount).toBe(3);
      
      // 同步成功后创建备份
      const backupMetadata = await backupManager.createBackup(
        testConversations, 
        '同步后自动备份'
      );
      
      expect(backupMetadata.conversationCount).toBe(3);
      expect(backupMetadata.description).toBe('同步后自动备份');
    });

    test('应该能够从备份恢复并同步', async () => {
      // 创建备份
      const backupMetadata = await backupManager.createBackup(testConversations);
      
      // 恢复备份
      const restoredData = await backupManager.restoreBackup(backupMetadata.id);
      
      // 同步恢复的数据
      (global as any).chrome.storage.sync.get.mockResolvedValue({ conversations: [] });
      const syncResult = await syncManager.sync(restoredData.conversations);
      
      expect(syncResult.success).toBe(true);
      expect(syncResult.syncedCount).toBe(3);
    });
  });

  describe('导出与备份集成', () => {
    test('应该能够导出备份文件', async () => {
      // 创建备份
      const backupMetadata = await backupManager.createBackup(
        testConversations, 
        '导出测试备份'
      );
      
      // 导出备份文件
      const exportResult = await backupManager.exportBackup(backupMetadata.id);
      expect(exportResult.filename).toMatch(/\.backup$/);
      expect(exportResult.size).toBeGreaterThan(0);
      
      // 同时导出对话为Markdown
      const conversationExport = await exportManager.exportConversations({
        format: 'markdown',
        conversations: testConversations,
        includeMetadata: true
      });
      
      expect(conversationExport.success).toBe(true);
      expect(conversationExport.conversationCount).toBe(3);
    });

    test('应该能够导入备份并导出对话', async () => {
      // 创建并导出备份
      const backupMetadata = await backupManager.createBackup(testConversations);
      const exportResult = await backupManager.exportBackup(backupMetadata.id);
      
      // 删除原备份
      await backupManager.deleteBackup(backupMetadata.id);
      
      // 导入备份
      const mockFile = new File([exportResult.blob], exportResult.filename);
      const importedMetadata = await backupManager.importBackup(mockFile);
      
      // 恢复并导出对话
      const restoredData = await backupManager.restoreBackup(importedMetadata.id);
      const conversationExport = await exportManager.exportConversations({
        format: 'json',
        conversations: restoredData.conversations
      });
      
      const jsonData = JSON.parse(conversationExport.content!);
      expect(jsonData.conversations).toHaveLength(3);
    });
  });

  describe('完整工作流集成', () => {
    test('应该支持完整的数据管理工作流', async () => {
      // 1. 构建搜索索引
      await searchEngine.buildIndex(testConversations);
      
      // 2. 搜索特定内容
      const searchResult = await searchEngine.search('JavaScript React');
      expect(searchResult.results).toHaveLength(2);
      
      // 3. 导出搜索结果
      const exportResult = await exportManager.exportConversations({
        format: 'html',
        conversations: searchResult.results.map(r => r.conversation),
        includeMetadata: true,
        includeTags: true
      });
      expect(exportResult.success).toBe(true);
      
      // 4. 创建备份
      const backupMetadata = await backupManager.createBackup(
        testConversations,
        '完整工作流测试备份'
      );
      expect(backupMetadata.conversationCount).toBe(3);
      
      // 5. 同步数据
      (global as any).chrome.storage.sync.get.mockResolvedValue({ conversations: [] });
      const syncResult = await syncManager.sync(testConversations);
      expect(syncResult.success).toBe(true);
      
      // 6. 验证所有组件状态
      const searchStats = searchEngine.getSearchStats();
      expect(searchStats.conversationIndices).toBe(3);
      
      const backupList = await backupManager.getBackupList();
      expect(backupList).toHaveLength(1);
      
      const syncStatus = syncManager.getStatus();
      expect(syncStatus.isConnected).toBe(true);
    });

    test('应该处理大量数据的工作流', async () => {
      // 生成大量测试数据
      const largeDataset: ConversationData[] = [];
      for (let i = 0; i < 100; i++) {
        largeDataset.push({
          id: `conv_${i.toString().padStart(3, '0')}`,
          platform: ['ChatGPT', 'Claude', 'Gemini'][i % 3] as any,
          title: `测试对话 ${i + 1}`,
          url: `https://example.com/chat/${i}`,
          timestamp: new Date(Date.now() - i * 60000),
          messages: [
            {
              id: `msg_${i}_1`,
              role: 'user',
              content: `这是第${i + 1}个测试问题`,
              timestamp: new Date(Date.now() - i * 60000)
            },
            {
              id: `msg_${i}_2`,
              role: 'assistant',
              content: `这是第${i + 1}个测试回答`,
              timestamp: new Date(Date.now() - i * 60000 + 30000)
            }
          ],
          metadata: {
            tags: [`标签${i % 10}`, '测试']
          }
        });
      }
      
      // 构建索引
      const startTime = Date.now();
      await searchEngine.buildIndex(largeDataset);
      const indexTime = Date.now() - startTime;
      
      // 搜索性能测试
      const searchStartTime = Date.now();
      const searchResult = await searchEngine.search('测试');
      const searchTime = Date.now() - searchStartTime;
      
      expect(searchResult.results.length).toBeGreaterThan(0);
      expect(indexTime).toBeLessThan(5000); // 索引构建应在5秒内完成
      expect(searchTime).toBeLessThan(1000); // 搜索应在1秒内完成
      
      // 备份性能测试
      const backupStartTime = Date.now();
      const backupMetadata = await backupManager.createBackup(largeDataset);
      const backupTime = Date.now() - backupStartTime;
      
      expect(backupMetadata.conversationCount).toBe(100);
      expect(backupTime).toBeLessThan(10000); // 备份应在10秒内完成
    });
  });
});
