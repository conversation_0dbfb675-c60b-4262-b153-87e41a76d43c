<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Memo - 调试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 AI Chat Memo 调试工具</h1>
            <p>这个页面运行在扩展上下文中，可以访问Chrome扩展API</p>
            <div id="extension-status">
                <span class="status-indicator status-offline"></span>
                <span>检查中...</span>
            </div>
        </div>

        <div class="section">
            <h2>📋 系统信息</h2>
            <div id="system-info">加载中...</div>
        </div>

        <div class="section">
            <h2>🔍 连接测试</h2>
            <button class="button" onclick="testBackgroundConnection()">测试Background连接</button>
            <button class="button" onclick="testContentScriptConnection()">测试Content Script连接</button>
            <button class="button" onclick="testStorageAccess()">测试存储访问</button>
            <button class="button" onclick="testPermissions()">检查权限</button>
            <button class="button" onclick="clearLog()">清除日志</button>
            <div id="test-result" class="result"></div>
        </div>

        <div class="section">
            <h2>🛠️ 快速操作</h2>
            <button class="button" onclick="reloadExtension()">重新加载扩展</button>
            <button class="button" onclick="openOptionsPage()">打开设置页面</button>
            <button class="button" onclick="getCurrentTabInfo()">获取当前标签页信息</button>
            <button class="button" onclick="testSaveConversation()">测试保存会话</button>
        </div>

        <div class="section">
            <h2>📊 扩展状态</h2>
            <div id="extension-stats">加载中...</div>
        </div>

        <div class="section">
            <h2>🚨 常见问题解决</h2>
            <div class="warning">
                <h3>如果遇到连接错误：</h3>
                <ol>
                    <li><strong>重新加载扩展</strong>：点击上方"重新加载扩展"按钮</li>
                    <li><strong>检查当前页面</strong>：确保在支持的AI平台上</li>
                    <li><strong>等待页面加载</strong>：确保页面完全加载后再测试</li>
                    <li><strong>检查权限</strong>：点击"检查权限"确认扩展权限正常</li>
                </ol>
            </div>
        </div>
    </div>

    <script src="debug.js"></script>
</body>
</html>
