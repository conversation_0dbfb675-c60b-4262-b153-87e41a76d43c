const fs = require('fs');
const path = require('path');

console.log('Starting simple import fix...');

// 简单的IIFE包装，不处理依赖
function wrapInIIFE(filePath, fileName) {
  console.log(`Processing ${fileName}...`);
  
  if (!fs.existsSync(filePath)) {
    console.error(`${fileName} not found`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');

  // 检查是否已经存在EVENTS定义（在移除import之前检查）
  const hasEventsDefinition = content.includes('const EVENTS') || content.includes('export const EVENTS');

  // 移除import语句（更全面的正则表达式）
  content = content.replace(/import\s*\{[^}]*\}\s*from\s*["'][^"']*["']\s*;?\s*/g, '');
  content = content.replace(/import\s+\w+\s+from\s*["'][^"']*["']\s*;?\s*/g, '');
  content = content.replace(/import\s*["'][^"']*["']\s*;?\s*/g, '');
  content = content.replace(/import\s+\*\s+as\s+\w+\s+from\s*["'][^"']*["']\s*;?\s*/g, '');

  // 移除所有以import开头的行
  content = content.replace(/^import\s+.*$/gm, '');
  
  // 简单的依赖定义（手动添加需要的类）
  let dependencies = `
// Logger类定义
class Logger {
  constructor(name) {
    this.name = name;
  }
  
  debug(...args) {
    console.log(\`[DEBUG \${this.name}]\`, ...args);
  }
  
  info(...args) {
    console.log(\`[INFO \${this.name}]\`, ...args);
  }
  
  warn(...args) {
    console.warn(\`[WARN \${this.name}]\`, ...args);
  }
  
  error(...args) {
    console.error(\`[ERROR \${this.name}]\`, ...args);
  }
}

// MessageBus类定义
class MessageBus {
  constructor(source) {
    this.listeners = new Map();
    this.source = source;
  }
  
  on(event, listener) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(listener);
    return () => this.off(event, listener);
  }
  
  off(event, listener) {
    const listeners = this.listeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }
  
  emit(event, data) {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error('MessageBus listener error:', error);
        }
      });
    }
  }
  
  send(event, data) {
    return this.emit(event, data);
  }

  async sendMessage(message) {
    return new Promise((resolve, reject) => {
      if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
        chrome.runtime.sendMessage(message, (response) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response || { success: false, error: 'No response' });
          }
        });
      } else {
        // 如果不在Chrome扩展环境中，返回模拟响应
        resolve({ success: false, error: 'Chrome runtime not available' });
      }
    });
  }
}

`;

  // 只有在没有EVENTS定义时才添加
  if (!hasEventsDefinition) {
    dependencies += `
// EVENTS常量定义
const EVENTS = {
  CONVERSATION_CREATED: "conversation:created",
  CONVERSATION_UPDATED: "conversation:updated",
  CONVERSATION_DELETED: "conversation:deleted",
  CONVERSATION_ARCHIVED: "conversation:archived",
  CONVERSATION_UNARCHIVED: "conversation:unarchived",
  CONVERSATIONS_MERGED: "conversations:merged",
  MESSAGE_ADDED: "message:added",
  MESSAGE_UPDATED: "message:updated",
  MESSAGE_DELETED: "message:deleted",
  PLATFORM_DETECTED: "platform:detected",
  PLATFORM_CHANGED: "platform:changed",
  STORAGE_READY: "storage:ready",
  STORAGE_ERROR: "storage:error",
  SETTINGS_UPDATED: "settings:updated",
  SETTINGS_RESET: "settings:reset"
};
`;
  }

  // 包装在IIFE中
  const wrappedContent = `(function() {
  'use strict';
  
  ${dependencies}
  
  ${content}
})();`;
  
  fs.writeFileSync(filePath, wrappedContent);
  console.log(`${fileName} processed successfully`);
  return true;
}

// 处理文件
const distDir = path.join(__dirname, '../dist');
const files = [
  'background.js',
  'content.js',
  'popup.js',
  'options.js'
];

files.forEach(fileName => {
  const filePath = path.join(distDir, fileName);
  wrapInIIFE(filePath, fileName);
});

console.log('Simple import fix completed');
