const fs = require('fs');
const path = require('path');

// 处理文件的函数
function processFile(filePath, fileName) {
  if (!fs.existsSync(filePath)) {
    console.error(`${fileName} not found`);
    return false;
  }

  let code = fs.readFileSync(filePath, 'utf8');
  console.log(`Original ${fileName} first 200 chars:`, code.substring(0, 200));

  // 移除import语句
  const originalLength = code.length;
  code = code.replace(/import\s*\{[^}]*\}\s*from\s*["'][^"']+["']\s*;?/g, '');
  console.log(`${fileName}: Removed imports, length changed from ${originalLength} to ${code.length}`);

  // 包装在IIFE中
  const iifeCode = `(function() {
    'use strict';

    ${code}
  })();`;

  // 写回文件
  fs.writeFileSync(filePath, iifeCode);
  console.log(`${fileName} converted to IIFE format, final size:`, iifeCode.length);
  return true;
}

// 处理content.js和background.js
const contentPath = path.join(__dirname, '../dist/content.js');
const backgroundPath = path.join(__dirname, '../dist/background.js');
const chunksDir = path.join(__dirname, '../dist/chunks');

// 读取所有chunk文件并内联它们
let chunkCode = '';
if (fs.existsSync(chunksDir)) {
  const chunkFiles = fs.readdirSync(chunksDir);
  console.log('Found chunk files:', chunkFiles);

  chunkFiles.forEach(chunkFile => {
    if (chunkFile.endsWith('.js')) {
      const chunkPath = path.join(chunksDir, chunkFile);
      const code = fs.readFileSync(chunkPath, 'utf8');
      console.log(`Reading chunk ${chunkFile}, size: ${code.length}`);

      // 移除chunk文件中的export语句
      const cleanChunkCode = code
        .replace(/export\s*\{[^}]*\}\s*;?/g, '')
        .replace(/export\s+/g, '');

      chunkCode += cleanChunkCode + '\n';
    }
  });
} else {
  console.log('No chunks directory found');
}

// 处理content.js
if (fs.existsSync(contentPath)) {
  let contentCode = fs.readFileSync(contentPath, 'utf8');

  // 添加chunk代码到content代码前面
  if (chunkCode) {
    contentCode = chunkCode + contentCode;
  }

  // 移除import语句 - 更强的正则表达式
  const originalLength = contentCode.length;
  contentCode = contentCode.replace(/import\s*\{[^}]*\}\s*from\s*["'][^"']*["']\s*;?/g, '');
  contentCode = contentCode.replace(/import\s+[^;]+;?/g, '');
  console.log(`content.js: Removed imports, length changed from ${originalLength} to ${contentCode.length}`);

  // 包装在IIFE中
  const iifeCode = `(function() {
    'use strict';

    ${contentCode}
  })();`;

  fs.writeFileSync(contentPath, iifeCode);
  console.log('content.js converted to IIFE format, final size:', iifeCode.length);
}

// 处理background.js
if (fs.existsSync(backgroundPath)) {
  let backgroundCode = fs.readFileSync(backgroundPath, 'utf8');

  // 添加chunk代码到background代码前面
  if (chunkCode) {
    backgroundCode = chunkCode + backgroundCode;
  }

  // 移除import语句 - 更强的正则表达式
  const originalLength = backgroundCode.length;
  backgroundCode = backgroundCode.replace(/import\s*\{[^}]*\}\s*from\s*["'][^"']*["']\s*;?/g, '');
  backgroundCode = backgroundCode.replace(/import\s+[^;]+;?/g, '');
  console.log(`background.js: Removed imports, length changed from ${originalLength} to ${backgroundCode.length}`);

  // 包装在IIFE中
  const iifeCode = `(function() {
    'use strict';

    ${backgroundCode}
  })();`;

  fs.writeFileSync(backgroundPath, iifeCode);
  console.log('background.js converted to IIFE format, final size:', iifeCode.length);
}

// 删除chunks目录
if (fs.existsSync(chunksDir)) {
  fs.rmSync(chunksDir, { recursive: true, force: true });
  console.log('Chunks directory removed');
}

console.log('Script conversion completed');
