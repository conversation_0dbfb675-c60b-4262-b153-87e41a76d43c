/**
 * Popup 入口文件
 * 插件弹窗界面的主要逻辑
 */

import { MessageBus } from '@shared/message-bus';
import { Logger } from '@shared/logger';
import { EVENTS } from '@types/index';
import type { Conversation, ConversationMetadata } from '@types/conversation';
import type { Platform } from '@types/platform';

interface PopupState {
  currentTab: string;
  conversations: Conversation[];
  searchResults: Conversation[];
  isLoading: boolean;
  searchQuery: string;
  stats: {
    totalConversations: number;
    totalMessages: number;
    platformStats: Record<Platform, number>;
    recentActivity: Array<{ date: string; count: number }>;
    popularTags: Array<{ tag: string; count: number }>;
  };
}

class PopupService {
  private messageBus: MessageBus;
  private logger: Logger;
  private state: PopupState;

  constructor() {
    this.logger = new Logger('Popup');
    this.messageBus = new MessageBus('popup');
    this.state = {
      currentTab: 'recent',
      conversations: [],
      searchResults: [],
      isLoading: false,
      searchQuery: '',
      stats: {
        totalConversations: 0,
        totalMessages: 0,
        platformStats: {} as Record<Platform, number>,
        recentActivity: [],
        popularTags: []
      }
    };
    this.init();
  }

  private async init(): Promise<void> {
    this.logger.info('Popup service initialized');
    this.setupEventListeners();
    await this.loadInitialData();
  }

  private setupEventListeners(): void {
    document.addEventListener('DOMContentLoaded', () => {
      this.logger.info('Popup DOM loaded');
      this.bindUIEvents();
    });
  }

  private bindUIEvents(): void {
    // 标签页切换
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        const tab = target.dataset.tab;
        if (tab) {
          this.switchTab(tab);
        }
      });
    });

    // 刷新按钮
    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        this.refreshData();
      });
    }

    // 设置按钮
    const settingsBtn = document.getElementById('settingsBtn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => {
        this.openSettings();
      });
    }

    // 保存当前会话按钮
    const saveCurrentBtn = document.getElementById('saveCurrentBtn');
    if (saveCurrentBtn) {
      saveCurrentBtn.addEventListener('click', () => {
        this.saveCurrentConversation();
      });
    }

    // 查看所有会话按钮
    const viewAllBtn = document.getElementById('viewAllBtn');
    if (viewAllBtn) {
      viewAllBtn.addEventListener('click', () => {
        this.viewAllConversations();
      });
    }

    // 导出数据按钮
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        this.exportData();
      });
    }

    // 搜索输入
    const searchInput = document.getElementById('search-input') as HTMLInputElement;
    if (searchInput) {
      let searchTimeout: number;
      searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        const query = (e.target as HTMLInputElement).value;
        searchTimeout = window.setTimeout(() => {
          this.performSearch(query);
        }, 300);
      });
    }

    // 快速过滤按钮
    document.querySelectorAll('[data-filter]').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const filter = (e.target as HTMLElement).dataset.filter;
        if (filter) {
          this.applyQuickFilter(filter);
        }
      });
    });

    // 视图切换按钮
    const viewToggle = document.getElementById('view-toggle');
    if (viewToggle) {
      viewToggle.addEventListener('click', () => {
        this.toggleView();
      });
    }
  }

  private async loadInitialData(): Promise<void> {
    this.setLoading(true);
    try {
      // 首先检查当前标签页是否支持
      await this.checkCurrentTabSupport();

      // 加载最近会话
      await this.loadRecentConversations();
      // 加载统计数据
      await this.loadStats();
    } catch (error) {
      this.logger.error('Failed to load initial data:', error);
      this.showError('加载数据失败，请稍后重试');
    } finally {
      this.setLoading(false);
    }
  }

  private async loadRecentConversations(): Promise<void> {
    try {
      const response = await this.messageBus.sendMessage({
        type: 'get-recent-conversations',
        data: { limit: 50 }
      });

      if (response && response.success) {
        this.state.conversations = response.conversations || [];
        this.renderConversations();
        this.updateTotalCount();
      } else {
        throw new Error(response?.error || 'Failed to load conversations');
      }
    } catch (error) {
      this.logger.error('Failed to load recent conversations:', error);
      // 如果是连接错误，显示更友好的错误信息
      if (error instanceof Error && error.message.includes('Could not establish connection')) {
        this.showError('无法连接到扩展服务，请刷新页面后重试');
      } else {
        throw error;
      }
    }
  }

  private async loadStats(): Promise<void> {
    try {
      const response = await this.messageBus.sendMessage({
        type: 'get-stats',
        data: {}
      });

      if (response && response.success) {
        this.state.stats = response.stats || this.state.stats;
        this.renderStats();
      } else {
        this.logger.warn('Failed to load stats:', response?.error);
      }
    } catch (error) {
      this.logger.error('Failed to load stats:', error);
      // 如果是连接错误，不显示错误，只记录日志
      if (error instanceof Error && error.message.includes('Could not establish connection')) {
        this.logger.warn('无法连接到扩展服务获取统计信息');
      }
    }
  }

  private switchTab(tab: string): void {
    // 更新状态
    this.state.currentTab = tab;

    // 更新UI
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.classList.remove('text-blue-600', 'border-blue-600', 'bg-blue-50');
      btn.classList.add('text-gray-500', 'border-transparent');
    });

    const activeBtn = document.querySelector(`[data-tab="${tab}"]`);
    if (activeBtn) {
      activeBtn.classList.remove('text-gray-500', 'border-transparent');
      activeBtn.classList.add('text-blue-600', 'border-blue-600', 'bg-blue-50');
    }

    // 显示对应页面
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.add('hidden');
    });

    const activePage = document.getElementById(`${tab}-page`);
    if (activePage) {
      activePage.classList.remove('hidden');
    }

    // 根据标签页加载数据
    if (tab === 'search' && this.state.searchQuery) {
      this.performSearch(this.state.searchQuery);
    } else if (tab === 'stats') {
      this.loadStats();
    }
  }

  private async refreshData(): Promise<void> {
    this.setLoading(true);
    try {
      if (this.state.currentTab === 'recent') {
        await this.loadRecentConversations();
      } else if (this.state.currentTab === 'search' && this.state.searchQuery) {
        await this.performSearch(this.state.searchQuery);
      } else if (this.state.currentTab === 'stats') {
        await this.loadStats();
      }
    } catch (error) {
      this.logger.error('Failed to refresh data:', error);
      this.showError('刷新失败，请稍后重试');
    } finally {
      this.setLoading(false);
    }
  }

  private openSettings(): void {
    chrome.runtime.openOptionsPage();
  }

  private async performSearch(query: string): Promise<void> {
    this.state.searchQuery = query;

    if (!query.trim()) {
      this.state.searchResults = [];
      this.renderSearchResults();
      return;
    }

    this.setSearchLoading(true);
    try {
      const response = await this.messageBus.sendMessage({
        type: 'search-conversations',
        data: { query: query.trim(), limit: 50 }
      });

      if (response.success) {
        this.state.searchResults = response.conversations || [];
        this.renderSearchResults();
      } else {
        throw new Error(response.error || 'Search failed');
      }
    } catch (error) {
      this.logger.error('Search failed:', error);
      this.showError('搜索失败，请稍后重试');
    } finally {
      this.setSearchLoading(false);
    }
  }

  private applyQuickFilter(filter: string): void {
    const searchInput = document.getElementById('search-input') as HTMLInputElement;
    if (searchInput) {
      searchInput.value = filter;
      this.performSearch(filter);
    }
  }

  private toggleView(): void {
    // 这里可以实现列表视图和卡片视图的切换
    // 暂时保留接口
    this.logger.info('Toggle view clicked');
  }

  private renderConversations(): void {
    const container = document.getElementById('conversations-list');
    const emptyState = document.getElementById('empty-state');

    if (!container || !emptyState) return;

    if (this.state.conversations.length === 0) {
      container.innerHTML = '';
      emptyState.classList.remove('hidden');
      return;
    }

    emptyState.classList.add('hidden');
    container.innerHTML = this.state.conversations.map(conv => this.renderConversationCard(conv)).join('');

    // 绑定会话卡片事件
    container.querySelectorAll('.conversation-card').forEach(card => {
      card.addEventListener('click', (e) => {
        const conversationId = (e.currentTarget as HTMLElement).dataset.id;
        if (conversationId) {
          this.openConversation(conversationId);
        }
      });
    });
  }

  private renderConversationCard(conversation: Conversation): string {
    const platformColors = {
      'chatgpt': 'bg-chatgpt',
      'claude': 'bg-claude',
      'gemini': 'bg-gemini',
      'aistudio': 'bg-aistudio',
      'monica': 'bg-monica',
      'poe': 'bg-poe'
    };

    const platformColor = platformColors[conversation.platform] || 'bg-gray-500';
    const timeAgo = this.formatTimeAgo(new Date(conversation.updatedAt));
    const messageCount = conversation.messages?.length || 0;

    return `
      <div class="conversation-card bg-white border border-gray-200 rounded-lg p-3 hover:border-blue-300 hover:shadow-sm transition-all cursor-pointer" data-id="${conversation.id}">
        <div class="flex items-start justify-between mb-2">
          <h3 class="text-sm font-medium text-gray-900 line-clamp-2 flex-1 mr-2">${this.escapeHtml(conversation.title)}</h3>
          <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium text-white ${platformColor} flex-shrink-0">
            ${conversation.platform.toUpperCase()}
          </span>
        </div>

        <div class="flex items-center justify-between text-xs text-gray-500">
          <div class="flex items-center space-x-2">
            <span>${messageCount} 条消息</span>
            ${conversation.tags && conversation.tags.length > 0 ? `
              <span>•</span>
              <div class="flex space-x-1">
                ${conversation.tags.slice(0, 2).map(tag => `
                  <span class="px-1.5 py-0.5 bg-gray-100 text-gray-600 rounded text-xs">${this.escapeHtml(tag)}</span>
                `).join('')}
                ${conversation.tags.length > 2 ? `<span class="text-gray-400">+${conversation.tags.length - 2}</span>` : ''}
              </div>
            ` : ''}
          </div>
          <span>${timeAgo}</span>
        </div>
      </div>
    `;
  }

  private renderSearchResults(): void {
    const container = document.getElementById('search-results');
    const emptyState = document.getElementById('search-empty');

    if (!container || !emptyState) return;

    if (this.state.searchResults.length === 0) {
      container.innerHTML = '';
      if (this.state.searchQuery.trim()) {
        emptyState.innerHTML = `
          <svg class="mx-auto w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <p class="text-gray-500 text-sm">未找到相关会话</p>
          <p class="text-gray-400 text-xs mt-1">尝试使用其他关键词或调整搜索条件</p>
        `;
      } else {
        emptyState.innerHTML = `
          <svg class="mx-auto w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <p class="text-gray-500 text-sm">输入关键词开始搜索</p>
          <p class="text-gray-400 text-xs mt-1">支持搜索会话标题、内容、标签和备注</p>
        `;
      }
      emptyState.classList.remove('hidden');
      return;
    }

    emptyState.classList.add('hidden');
    container.innerHTML = this.state.searchResults.map(conv => this.renderConversationCard(conv)).join('');

    // 绑定搜索结果事件
    container.querySelectorAll('.conversation-card').forEach(card => {
      card.addEventListener('click', (e) => {
        const conversationId = (e.currentTarget as HTMLElement).dataset.id;
        if (conversationId) {
          this.openConversation(conversationId);
        }
      });
    });
  }

  private renderStats(): void {
    // 更新总体统计
    const totalConversationsEl = document.getElementById('total-conversations');
    const totalMessagesEl = document.getElementById('total-messages');

    if (totalConversationsEl) {
      totalConversationsEl.textContent = this.state.stats.totalConversations.toString();
    }
    if (totalMessagesEl) {
      totalMessagesEl.textContent = this.state.stats.totalMessages.toString();
    }

    // 渲染平台分布
    this.renderPlatformStats();

    // 渲染最近活动
    this.renderRecentActivity();

    // 渲染热门标签
    this.renderPopularTags();
  }

  private renderPlatformStats(): void {
    const container = document.getElementById('platform-stats');
    if (!container) return;

    const platformColors = {
      'chatgpt': 'bg-chatgpt',
      'claude': 'bg-claude',
      'gemini': 'bg-gemini',
      'aistudio': 'bg-aistudio',
      'monica': 'bg-monica',
      'poe': 'bg-poe'
    };

    const platforms = Object.entries(this.state.stats.platformStats);
    if (platforms.length === 0) {
      container.innerHTML = '<p class="text-gray-500 text-sm">暂无数据</p>';
      return;
    }

    container.innerHTML = platforms.map(([platform, count]) => {
      const color = platformColors[platform as Platform] || 'bg-gray-500';
      return `
        <div class="flex items-center justify-between py-1">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 rounded-full ${color}"></div>
            <span class="text-sm text-gray-700">${platform.toUpperCase()}</span>
          </div>
          <span class="text-sm font-medium text-gray-900">${count}</span>
        </div>
      `;
    }).join('');
  }

  private renderRecentActivity(): void {
    const container = document.getElementById('recent-activity');
    if (!container) return;

    if (this.state.stats.recentActivity.length === 0) {
      container.innerHTML = '<p class="text-gray-500 text-sm">暂无数据</p>';
      return;
    }

    container.innerHTML = this.state.stats.recentActivity.map(activity => `
      <div class="flex items-center justify-between py-1">
        <span class="text-sm text-gray-700">${activity.date}</span>
        <span class="text-sm font-medium text-gray-900">${activity.count} 条</span>
      </div>
    `).join('');
  }

  private renderPopularTags(): void {
    const container = document.getElementById('popular-tags');
    if (!container) return;

    if (this.state.stats.popularTags.length === 0) {
      container.innerHTML = '<p class="text-gray-500 text-sm">暂无标签</p>';
      return;
    }

    container.innerHTML = this.state.stats.popularTags.map(tag => `
      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 cursor-pointer hover:bg-blue-200 transition-colors"
            onclick="this.dispatchEvent(new CustomEvent('tag-click', {detail: '${tag.tag}', bubbles: true}))">
        ${this.escapeHtml(tag.tag)} (${tag.count})
      </span>
    `).join('');

    // 绑定标签点击事件
    container.addEventListener('tag-click', (e: any) => {
      const tag = e.detail;
      this.switchTab('search');
      const searchInput = document.getElementById('search-input') as HTMLInputElement;
      if (searchInput) {
        searchInput.value = `tag:${tag}`;
        this.performSearch(`tag:${tag}`);
      }
    });
  }

  private openConversation(conversationId: string): void {
    // 发送消息到background script获取会话详情
    this.messageBus.sendMessage({
      type: EVENTS.CONVERSATION.GET_BY_ID,
      data: { id: conversationId }
    }).then(response => {
      if (response.success && response.conversation) {
        // 打开原始链接
        if (response.conversation.url) {
          chrome.tabs.create({ url: response.conversation.url });
        }
      }
    }).catch(error => {
      this.logger.error('Failed to open conversation:', error);
    });
  }

  private setLoading(loading: boolean): void {
    this.state.isLoading = loading;
    const loadingEl = document.getElementById('loading');
    const conversationsEl = document.getElementById('conversations-list');

    if (loadingEl && conversationsEl) {
      if (loading) {
        loadingEl.classList.remove('hidden');
        conversationsEl.classList.add('hidden');
      } else {
        loadingEl.classList.add('hidden');
        conversationsEl.classList.remove('hidden');
      }
    }
  }

  private setSearchLoading(loading: boolean): void {
    const loadingEl = document.getElementById('search-loading');
    const resultsEl = document.getElementById('search-results');

    if (loadingEl && resultsEl) {
      if (loading) {
        loadingEl.classList.remove('hidden');
        resultsEl.classList.add('hidden');
      } else {
        loadingEl.classList.add('hidden');
        resultsEl.classList.remove('hidden');
      }
    }
  }

  private showError(message: string): void {
    const errorEl = document.getElementById('error');
    if (errorEl) {
      errorEl.textContent = message;
      errorEl.classList.remove('hidden');
      setTimeout(() => {
        errorEl.classList.add('hidden');
      }, 5000);
    }
  }

  private updateTotalCount(): void {
    const countEl = document.getElementById('total-count');
    if (countEl) {
      countEl.textContent = this.state.conversations.length.toString();
    }
  }

  private formatTimeAgo(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;

    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    });
  }

  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 保存当前会话
   */
  private async saveCurrentConversation(): Promise<void> {
    try {
      this.setLoading(true);

      // 获取当前活动标签页
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        this.showError('无法获取当前标签页');
        return;
      }

      const currentTab = tabs[0];

      // 发送消息到background script请求保存当前会话
      const response = await this.messageBus.sendMessage({
        type: 'save-current-conversation'
      });

      if (response && response.success) {
        this.showSuccess('当前会话已保存');
        // 刷新会话列表
        await this.loadRecentConversations();
      } else {
        this.showError(response?.error || '保存失败，请确保当前页面支持会话保存');
      }
    } catch (error) {
      this.logger.error('保存当前会话失败:', error);
      this.showError('保存失败，请稍后重试');
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * 查看所有会话
   */
  private viewAllConversations(): void {
    // 打开选项页面的会话管理标签
    chrome.runtime.openOptionsPage();
  }

  /**
   * 导出数据
   */
  private async exportData(): Promise<void> {
    try {
      this.setLoading(true);

      const response = await this.messageBus.sendMessage({
        type: 'export-conversations',
        data: { format: 'json' }
      });

      if (response.success) {
        // 创建下载链接
        const blob = new Blob([JSON.stringify(response.data, null, 2)], {
          type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ai-chat-memo-export-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showSuccess('数据导出成功');
      } else {
        this.showError(response.error || '导出失败');
      }
    } catch (error) {
      this.logger.error('导出数据失败:', error);
      this.showError('导出失败，请稍后重试');
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * 检查当前标签页是否支持
   */
  private async checkCurrentTabSupport(): Promise<void> {
    try {
      // 获取当前活动标签页
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) return;

      const currentTab = tabs[0];
      const url = currentTab.url || '';

      // 检查是否是支持的域名
      const supportedDomains = [
        'chatgpt.com',
        'chat.openai.com',
        'claude.ai',
        'gemini.google.com',
        'aistudio.google.com',
        'monica.im',
        'poe.com'
      ];

      const isSupported = supportedDomains.some(domain => url.includes(domain));

      if (!isSupported) {
        this.showUnsupportedPageWarning();
      } else {
        // 尝试ping content script
        try {
          if (currentTab.id) {
            await chrome.tabs.sendMessage(currentTab.id, { type: 'ping' });
          }
        } catch (error) {
          this.logger.warn('Content script not ready:', error);
          this.showContentScriptNotReadyWarning();
        }
      }
    } catch (error) {
      this.logger.error('Failed to check tab support:', error);
    }
  }

  /**
   * 显示不支持页面的警告
   */
  private showUnsupportedPageWarning(): void {
    const warningEl = document.getElementById('page-warning');
    if (warningEl) {
      warningEl.innerHTML = `
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-sm text-yellow-800">当前页面不支持AI对话保存功能</span>
          </div>
          <p class="text-xs text-yellow-700 mt-1">请访问支持的AI平台（ChatGPT、Claude、Gemini等）</p>
        </div>
      `;
      warningEl.classList.remove('hidden');
    }
  }

  /**
   * 显示Content Script未就绪的警告
   */
  private showContentScriptNotReadyWarning(): void {
    const warningEl = document.getElementById('page-warning');
    if (warningEl) {
      warningEl.innerHTML = `
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-sm text-blue-800">页面正在加载中...</span>
          </div>
          <p class="text-xs text-blue-700 mt-1">请等待页面完全加载后再使用保存功能</p>
        </div>
      `;
      warningEl.classList.remove('hidden');
    }
  }

  /**
   * 显示成功消息
   */
  private showSuccess(message: string): void {
    // 简单的成功提示实现
    console.log('Success:', message);
    // TODO: 实现更好的UI提示
  }


}

// 初始化服务
const popupService = new PopupService();

// 导出服务实例
export default popupService;








