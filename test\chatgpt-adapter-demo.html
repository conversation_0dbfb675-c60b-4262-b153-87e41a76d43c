<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGPT 适配器演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f7f7f8;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .conversation-turn {
            margin-bottom: 20px;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid #e5e5e5;
        }
        .user-message {
            background-color: #f0f9ff;
            border-left: 4px solid #0ea5e9;
        }
        .assistant-message {
            background-color: #f8fafc;
            border-left: 4px solid #64748b;
        }
        .message-header {
            font-weight: 600;
            margin-bottom: 8px;
            color: #374151;
        }
        .message-content {
            line-height: 1.6;
            color: #1f2937;
        }
        pre {
            background: #f1f5f9;
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 12px 0;
        }
        code {
            background: #f1f5f9;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        .controls {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
        }
        button {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #f9fafb;
        }
        button.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        button.primary:hover {
            background: #2563eb;
        }
        .status {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .status.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        .status.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        .status.info {
            background: #eff6ff;
            color: #1d4ed8;
            border: 1px solid #dbeafe;
        }
        .extracted-data {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 16px;
            margin-top: 16px;
        }
        .extracted-data pre {
            background: #1e293b;
            color: #e2e8f0;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ChatGPT 适配器演示</h1>
        <p>这个页面模拟了ChatGPT的对话界面，用于测试我们的平台适配器功能。</p>
        
        <div class="controls">
            <button class="primary" onclick="testAdapter()">测试适配器</button>
            <button onclick="addMessage('user')">添加用户消息</button>
            <button onclick="addMessage('assistant')">添加助手消息</button>
            <button onclick="clearMessages()">清空消息</button>
        </div>

        <div id="status"></div>
    </div>

    <!-- 模拟ChatGPT的对话容器 -->
    <div class="container">
        <h2>模拟对话</h2>
        <div role="main" id="conversation-container">
            <!-- 初始消息 -->
            <div class="conversation-turn user-message" data-message-author-role="user">
                <div class="message-header">用户</div>
                <div class="message-content">
                    <div class="markdown">你好！请帮我写一个简单的JavaScript函数来计算斐波那契数列。</div>
                </div>
            </div>

            <div class="conversation-turn assistant-message" data-message-author-role="assistant">
                <div class="message-header">ChatGPT</div>
                <div class="message-content">
                    <div class="markdown">
                        <p>当然！我来为你写一个计算斐波那契数列的JavaScript函数。这里提供两种实现方式：</p>
                        
                        <h3>1. 递归实现（简单但效率较低）</h3>
                        <pre><code class="language-javascript">function fibonacci(n) {
    if (n <= 1) {
        return n;
    }
    return fibonacci(n - 1) + fibonacci(n - 2);
}

// 使用示例
console.log(fibonacci(10)); // 输出: 55</code></pre>

                        <h3>2. 迭代实现（推荐，效率更高）</h3>
                        <pre><code class="language-javascript">function fibonacciIterative(n) {
    if (n <= 1) return n;
    
    let a = 0, b = 1;
    for (let i = 2; i <= n; i++) {
        let temp = a + b;
        a = b;
        b = temp;
    }
    return b;
}

// 使用示例
console.log(fibonacciIterative(10)); // 输出: 55</code></pre>

                        <p>第二种迭代方法的时间复杂度是 O(n)，而递归方法的时间复杂度是 O(2^n)，所以在处理较大数字时，迭代方法会快很多。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 提取的数据显示区域 -->
    <div class="container">
        <h2>提取的会话数据</h2>
        <div class="extracted-data">
            <pre id="extracted-output">点击"测试适配器"按钮来提取会话数据...</pre>
        </div>
    </div>

    <script>
        // 模拟ChatGPT适配器的核心逻辑
        class MockChatGPTAdapter {
            constructor() {
                this.platform = 'ChatGPT';
                this.selectors = {
                    conversationContainer: '[role="main"]',
                    messageElements: '[data-message-author-role]',
                    userMessage: '[data-message-author-role="user"]',
                    assistantMessage: '[data-message-author-role="assistant"]',
                    messageContent: '.markdown, .whitespace-pre-wrap, .prose'
                };
            }

            isCurrentPlatform() {
                return window.location.hostname.includes('localhost') || 
                       window.location.hostname.includes('127.0.0.1') ||
                       window.location.hostname.includes('chat.openai.com');
            }

            isPageReady() {
                const hasContainer = document.querySelector(this.selectors.conversationContainer) !== null;
                const hasMessages = document.querySelector(this.selectors.messageElements) !== null;
                return hasContainer && hasMessages;
            }

            extractConversation() {
                if (!this.isPageReady()) {
                    return null;
                }

                const messages = this.extractMessages();
                const title = this.extractTitle();
                const url = window.location.href;

                return {
                    id: `demo_${Date.now()}`,
                    platform: this.platform,
                    url,
                    title,
                    messages,
                    tags: [this.platform.toLowerCase()],
                    notes: '',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    metadata: {
                        messageCount: messages.length,
                        lastActivity: new Date(),
                        isArchived: false,
                        platformSpecific: {
                            hasCodeBlocks: messages.some(m => m.content.includes('```')),
                            hasImages: false,
                            conversationType: 'coding'
                        }
                    }
                };
            }

            extractMessages() {
                const messages = [];
                const messageElements = document.querySelectorAll(this.selectors.messageElements);

                messageElements.forEach((element, index) => {
                    const role = element.getAttribute('data-message-author-role');
                    if (!role || (role !== 'user' && role !== 'assistant')) {
                        return;
                    }

                    const content = this.extractMessageContent(element);
                    if (!content.trim()) {
                        return;
                    }

                    messages.push({
                        id: `${role}_${index}`,
                        conversationId: '',
                        type: role,
                        content,
                        timestamp: new Date(),
                        metadata: {
                            platform: this.platform,
                            elementIndex: index,
                            hasCodeBlocks: content.includes('```'),
                            hasImages: false,
                            wordCount: content.split(/\s+/).length,
                            characterCount: content.length
                        }
                    });
                });

                return messages;
            }

            extractMessageContent(element) {
                const contentElement = element.querySelector(this.selectors.messageContent) || element;
                return contentElement.textContent?.trim() || '';
            }

            extractTitle() {
                const firstUserMessage = document.querySelector(this.selectors.userMessage);
                if (firstUserMessage) {
                    const content = this.extractMessageContent(firstUserMessage);
                    if (content) {
                        return content.slice(0, 50) + (content.length > 50 ? '...' : '');
                    }
                }
                return `ChatGPT 会话 - ${new Date().toLocaleDateString()}`;
            }
        }

        // 全局适配器实例
        const adapter = new MockChatGPTAdapter();

        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
        }

        function testAdapter() {
            try {
                showStatus('正在测试适配器...', 'info');
                
                // 检查平台检测
                const isPlatform = adapter.isCurrentPlatform();
                const isReady = adapter.isPageReady();
                
                if (!isPlatform) {
                    showStatus('平台检测失败：当前页面不是ChatGPT平台', 'error');
                    return;
                }
                
                if (!isReady) {
                    showStatus('页面检测失败：页面未准备就绪', 'error');
                    return;
                }

                // 提取会话数据
                const conversation = adapter.extractConversation();
                
                if (conversation) {
                    showStatus(`成功提取会话数据！标题: "${conversation.title}"，消息数: ${conversation.messages.length}`, 'success');
                    
                    // 显示提取的数据
                    document.getElementById('extracted-output').textContent = JSON.stringify(conversation, null, 2);
                } else {
                    showStatus('提取会话数据失败：未找到有效数据', 'error');
                }
                
            } catch (error) {
                showStatus(`测试失败：${error.message}`, 'error');
                console.error('适配器测试错误:', error);
            }
        }

        function addMessage(type) {
            const container = document.getElementById('conversation-container');
            const messageCount = container.children.length;
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `conversation-turn ${type}-message`;
            messageDiv.setAttribute('data-message-author-role', type);
            
            const sampleContent = type === 'user' 
                ? `这是第 ${messageCount + 1} 条用户消息。请帮我解决一个问题。`
                : `这是第 ${messageCount + 1} 条助手回复。我很乐意帮助您解决问题！\n\n这里是一些示例代码：\n\`\`\`javascript\nconsole.log("Hello World!");\n\`\`\``;
            
            messageDiv.innerHTML = `
                <div class="message-header">${type === 'user' ? '用户' : 'ChatGPT'}</div>
                <div class="message-content">
                    <div class="markdown">${sampleContent.replace(/\n/g, '<br>')}</div>
                </div>
            `;
            
            container.appendChild(messageDiv);
            showStatus(`已添加${type === 'user' ? '用户' : '助手'}消息`, 'success');
        }

        function clearMessages() {
            const container = document.getElementById('conversation-container');
            container.innerHTML = '';
            document.getElementById('extracted-output').textContent = '消息已清空，点击"测试适配器"按钮来提取会话数据...';
            showStatus('已清空所有消息', 'info');
        }

        // 页面加载完成后自动测试
        window.addEventListener('load', () => {
            showStatus('页面已加载，可以开始测试适配器功能', 'info');
        });
    </script>
</body>
</html>
