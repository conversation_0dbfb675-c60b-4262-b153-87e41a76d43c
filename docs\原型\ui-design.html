<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI会话管理插件</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'chatgpt': '#10b981',
                        'claude': '#f59e0b',
                        'gemini': '#3b82f6',
                        'aistudio': '#8b5cf6',
                        'monica': '#ec4899',
                        'poe': '#6366f1'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-8">
                    <h1 class="text-xl font-semibold text-gray-900">AI会话管理</h1>
                    <div class="hidden md:flex space-x-6">
                        <a href="#" onclick="showPage('conversations')" class="text-blue-600 border-b-2 border-blue-600 px-1 pb-4 text-sm font-medium">会话列表</a>
                        <a href="#" onclick="showPage('settings')" class="text-gray-500 hover:text-gray-700 px-1 pb-4 text-sm font-medium">设置</a>
                        <a href="#" onclick="showPage('export')" class="text-gray-500 hover:text-gray-700 px-1 pb-4 text-sm font-medium">导出</a>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        批量管理
                    </button>
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        导出选中
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div id="conversationsPage" class="page-content">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <!-- 搜索和过滤区域 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                <div class="flex flex-col lg:flex-row gap-4 mb-4">
                    <div class="flex-1">
                        <input type="text"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="搜索会话内容、标签或备注..."
                               id="searchInput">
                    </div>
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        搜索
                    </button>
                </div>
                <div class="flex flex-wrap gap-2">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 cursor-pointer hover:bg-blue-200 transition-colors" data-platform="all">
                        全部
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700 cursor-pointer hover:bg-gray-200 transition-colors" data-platform="chatgpt">
                        ChatGPT
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700 cursor-pointer hover:bg-gray-200 transition-colors" data-platform="claude">
                        Claude
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700 cursor-pointer hover:bg-gray-200 transition-colors" data-platform="gemini">
                        Gemini
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700 cursor-pointer hover:bg-gray-200 transition-colors" data-platform="aistudio">
                        Aistudio
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700 cursor-pointer hover:bg-gray-200 transition-colors" data-platform="monica">
                        Monica
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700 cursor-pointer hover:bg-gray-200 transition-colors" data-platform="poe">
                        Poe
                    </span>
                </div>
            </div>

            <!-- 视图控制 -->
            <div class="flex justify-between items-center mb-6">
                <div class="flex bg-white rounded-lg shadow-sm border border-gray-200 p-1">
                    <button class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md transition-colors" onclick="switchView('grid')">
                        卡片视图
                    </button>
                    <button class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 rounded-md transition-colors" onclick="switchView('list')">
                        列表视图
                    </button>
                </div>
                <div class="text-sm text-gray-500">
                    共 <span id="totalCount" class="font-medium text-gray-900">156</span> 条会话
                </div>
            </div>

            <!-- 会话列表 - 卡片视图 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="gridView">
                <!-- 会话卡片 1 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer" onclick="openConversation('1')">
                    <div class="flex justify-between items-start mb-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-chatgpt text-white">
                            ChatGPT
                        </span>
                        <div class="flex space-x-1">
                            <button class="p-1 text-gray-400 hover:text-gray-600 rounded" onclick="event.stopPropagation(); editConversation('1')" title="编辑">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-gray-400 hover:text-red-600 rounded" onclick="event.stopPropagation(); deleteConversation('1')" title="删除">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="mb-4">
                        <h3 class="font-medium text-gray-900 mb-2 line-clamp-2">如何设计一个用户友好的界面？</h3>
                        <p class="text-sm text-gray-600 line-clamp-3">设计用户友好的界面需要考虑以下几个方面：1. 简洁明了的布局 2. 直观的导航结构 3. 一致的视觉风格 4. 良好的响应式设计...</p>
                    </div>
                    <div class="flex justify-between items-center pt-4 border-t border-gray-100">
                        <div class="flex flex-wrap gap-1">
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">UI设计</span>
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">用户体验</span>
                        </div>
                        <span class="text-xs text-gray-500">2小时前</span>
                    </div>
                </div>

                <!-- 会话卡片 2 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer" onclick="openConversation('2')">
                    <div class="flex justify-between items-start mb-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-claude text-white">
                            Claude
                        </span>
                        <div class="flex space-x-1">
                            <button class="p-1 text-gray-400 hover:text-gray-600 rounded" onclick="event.stopPropagation(); editConversation('2')" title="编辑">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-gray-400 hover:text-red-600 rounded" onclick="event.stopPropagation(); deleteConversation('2')" title="删除">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="mb-4">
                        <h3 class="font-medium text-gray-900 mb-2 line-clamp-2">JavaScript异步编程最佳实践</h3>
                        <p class="text-sm text-gray-600 line-clamp-3">JavaScript异步编程有几种主要方式：Promise、async/await、回调函数。推荐使用async/await语法，它让异步代码看起来更像同步代码...</p>
                    </div>
                    <div class="flex justify-between items-center pt-4 border-t border-gray-100">
                        <div class="flex flex-wrap gap-1">
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800">JavaScript</span>
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800">编程</span>
                        </div>
                        <span class="text-xs text-gray-500">1天前</span>
                    </div>
                </div>

                <!-- 会话卡片 3 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer" onclick="openConversation('3')">
                    <div class="flex justify-between items-start mb-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gemini text-white">
                            Gemini
                        </span>
                        <div class="flex space-x-1">
                            <button class="p-1 text-gray-400 hover:text-gray-600 rounded" onclick="event.stopPropagation(); editConversation('3')" title="编辑">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-gray-400 hover:text-red-600 rounded" onclick="event.stopPropagation(); deleteConversation('3')" title="删除">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="mb-4">
                        <h3 class="font-medium text-gray-900 mb-2 line-clamp-2">机器学习模型部署策略</h3>
                        <p class="text-sm text-gray-600 line-clamp-3">机器学习模型部署需要考虑多个因素：模型大小、推理速度、资源消耗、可扩展性等。常见的部署方式包括云端API、边缘计算、移动端部署...</p>
                    </div>
                    <div class="flex justify-between items-center pt-4 border-t border-gray-100">
                        <div class="flex flex-wrap gap-1">
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">机器学习</span>
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-indigo-100 text-indigo-800">部署</span>
                        </div>
                        <span class="text-xs text-gray-500">3天前</span>
                    </div>
                </div>
            </div>

            <!-- 会话列表 - 列表视图 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 hidden" id="listView">
                <div class="divide-y divide-gray-200">
                    <div class="p-4 hover:bg-gray-50 cursor-pointer" onclick="openConversation('1')">
                        <div class="flex items-center space-x-4">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-sm font-medium text-gray-900 truncate">如何设计一个用户友好的界面？</h3>
                                    <div class="flex items-center space-x-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-chatgpt text-white">ChatGPT</span>
                                        <span class="text-xs text-gray-500">2小时前</span>
                                    </div>
                                </div>
                                <div class="mt-1 flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">UI设计</span>
                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">用户体验</span>
                                </div>
                            </div>
                            <div class="flex space-x-1">
                                <button class="p-1 text-gray-400 hover:text-gray-600 rounded" onclick="event.stopPropagation(); editConversation('1')" title="编辑">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </button>
                                <button class="p-1 text-gray-400 hover:text-red-600 rounded" onclick="event.stopPropagation(); deleteConversation('1')" title="删除">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="p-4 hover:bg-gray-50 cursor-pointer" onclick="openConversation('2')">
                        <div class="flex items-center space-x-4">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-sm font-medium text-gray-900 truncate">JavaScript异步编程最佳实践</h3>
                                    <div class="flex items-center space-x-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-claude text-white">Claude</span>
                                        <span class="text-xs text-gray-500">1天前</span>
                                    </div>
                                </div>
                                <div class="mt-1 flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800">JavaScript</span>
                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800">编程</span>
                                </div>
                            </div>
                            <div class="flex space-x-1">
                                <button class="p-1 text-gray-400 hover:text-gray-600 rounded" onclick="event.stopPropagation(); editConversation('2')" title="编辑">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </button>
                                <button class="p-1 text-gray-400 hover:text-red-600 rounded" onclick="event.stopPropagation(); deleteConversation('2')" title="删除">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置页面 -->
    <div id="settingsPage" class="page-content hidden">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">插件设置</h2>
                </div>
                <div class="p-6 space-y-6">
                    <!-- 自动保存设置 -->
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-gray-900">自动保存会话</h3>
                            <p class="text-sm text-gray-500">自动保存新的AI会话内容到本地</p>
                        </div>
                        <button type="button" class="bg-blue-600 relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" role="switch" aria-checked="true">
                            <span class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
                        </button>
                    </div>

                    <!-- 支持的平台 -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-900 mb-3">支持的AI平台</h3>
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                            <div class="flex items-center space-x-3">
                                <input type="checkbox" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="text-sm text-gray-700">ChatGPT</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <input type="checkbox" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="text-sm text-gray-700">Claude</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <input type="checkbox" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="text-sm text-gray-700">Gemini</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <input type="checkbox" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="text-sm text-gray-700">Aistudio</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <input type="checkbox" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="text-sm text-gray-700">Monica</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <input type="checkbox" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="text-sm text-gray-700">Poe</span>
                            </div>
                        </div>
                    </div>

                    <!-- 数据管理 -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-900 mb-3">数据管理</h3>
                        <div class="space-y-3">
                            <button class="w-full sm:w-auto bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium transition-colors">
                                导出所有数据
                            </button>
                            <button class="w-full sm:w-auto bg-red-100 hover:bg-red-200 text-red-700 px-4 py-2 rounded-md text-sm font-medium transition-colors ml-0 sm:ml-3">
                                清空所有数据
                            </button>
                        </div>
                    </div>

                    <!-- 错误日志 -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-900 mb-3">错误日志</h3>
                        <div class="bg-gray-50 rounded-md p-4">
                            <div class="text-sm text-gray-600 mb-2">最近错误记录：</div>
                            <div class="text-xs text-gray-500 font-mono">
                                2024-01-15 14:30:25 - 保存会话失败: 网络连接超时<br>
                                2024-01-15 12:15:10 - ChatGPT页面结构变化，需要更新适配器
                            </div>
                            <button class="mt-3 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors">
                                导出日志
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 导出页面 -->
    <div id="exportPage" class="page-content hidden">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">导出会话</h2>
                </div>
                <div class="p-6">
                    <!-- 导出选项 -->
                    <div class="space-y-6">
                        <div>
                            <h3 class="text-sm font-medium text-gray-900 mb-3">选择导出范围</h3>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" name="exportRange" value="selected" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">仅导出选中的会话 (3条)</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="exportRange" value="filtered" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">导出当前筛选结果 (156条)</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="exportRange" value="all" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">导出所有会话 (1,234条)</span>
                                </label>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-sm font-medium text-gray-900 mb-3">导出格式</h3>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <label class="relative flex cursor-pointer rounded-lg border border-gray-300 bg-white p-4 shadow-sm focus:outline-none">
                                    <input type="radio" name="exportFormat" value="markdown" checked class="sr-only">
                                    <span class="flex flex-1">
                                        <span class="flex flex-col">
                                            <span class="block text-sm font-medium text-gray-900">Markdown</span>
                                            <span class="mt-1 flex items-center text-sm text-gray-500">适合文档编辑和版本控制</span>
                                        </span>
                                    </span>
                                    <svg class="h-5 w-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                </label>
                                <label class="relative flex cursor-pointer rounded-lg border border-gray-300 bg-white p-4 shadow-sm focus:outline-none">
                                    <input type="radio" name="exportFormat" value="pdf" class="sr-only">
                                    <span class="flex flex-1">
                                        <span class="flex flex-col">
                                            <span class="block text-sm font-medium text-gray-900">PDF</span>
                                            <span class="mt-1 flex items-center text-sm text-gray-500">适合打印和分享</span>
                                        </span>
                                    </span>
                                </label>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-sm font-medium text-gray-900 mb-3">导出内容</h3>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">问答内容</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">标签信息</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">备注内容</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">原始链接</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">时间戳</span>
                                </label>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                            <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium transition-colors">
                                预览
                            </button>
                            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                                开始导出
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 会话详情侧边栏 -->
    <div class="fixed inset-y-0 right-0 w-96 bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out z-50" id="conversationSidebar">
        <div class="flex flex-col h-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-medium text-gray-900">编辑会话</h2>
                    <button class="text-gray-400 hover:text-gray-600" onclick="closeSidebar()">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="flex-1 overflow-y-auto p-6">
                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">标签</label>
                        <input type="text"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="添加标签，用逗号分隔"
                               value="UI设计, 用户体验">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
                        <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  rows="4"
                                  placeholder="添加备注信息...">这是一个关于界面设计的重要讨论，包含了很多实用的建议。</textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">原始链接</label>
                        <div class="flex">
                            <input type="text"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50"
                                   value="https://chat.openai.com/c/abc123"
                                   readonly>
                            <button class="px-3 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-200 transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex space-x-3">
                    <button class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium transition-colors">
                        取消
                    </button>
                    <button class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                        保存更改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 遮罩层 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 hidden z-40" id="overlay" onclick="closeSidebar()"></div>

    <script>
        // 页面切换
        function showPage(pageName) {
            // 隐藏所有页面
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.add('hidden');
            });

            // 显示指定页面
            document.getElementById(pageName + 'Page').classList.remove('hidden');

            // 更新导航状态
            document.querySelectorAll('nav a').forEach(link => {
                link.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600');
                link.classList.add('text-gray-500', 'hover:text-gray-700');
            });

            event.target.classList.remove('text-gray-500', 'hover:text-gray-700');
            event.target.classList.add('text-blue-600', 'border-b-2', 'border-blue-600');
        }

        // 视图切换
        function switchView(viewType) {
            const gridView = document.getElementById('gridView');
            const listView = document.getElementById('listView');
            const buttons = document.querySelectorAll('[onclick*="switchView"]');

            if (viewType === 'grid') {
                gridView.classList.remove('hidden');
                listView.classList.add('hidden');
                buttons[0].classList.add('text-white', 'bg-blue-600');
                buttons[0].classList.remove('text-gray-700', 'hover:text-gray-900');
                buttons[1].classList.remove('text-white', 'bg-blue-600');
                buttons[1].classList.add('text-gray-700', 'hover:text-gray-900');
            } else {
                gridView.classList.add('hidden');
                listView.classList.remove('hidden');
                buttons[1].classList.add('text-white', 'bg-blue-600');
                buttons[1].classList.remove('text-gray-700', 'hover:text-gray-900');
                buttons[0].classList.remove('text-white', 'bg-blue-600');
                buttons[0].classList.add('text-gray-700', 'hover:text-gray-900');
            }
        }

        // 打开会话详情
        function openConversation(id) {
            console.log('打开会话:', id);
            // 这里可以跳转到会话详情页面或打开模态框
        }

        // 编辑会话
        function editConversation(id) {
            document.getElementById('conversationSidebar').classList.remove('translate-x-full');
            document.getElementById('overlay').classList.remove('hidden');
        }

        // 关闭侧边栏
        function closeSidebar() {
            document.getElementById('conversationSidebar').classList.add('translate-x-full');
            document.getElementById('overlay').classList.add('hidden');
        }

        // 删除会话
        function deleteConversation(id) {
            if (confirm('确定要删除这条会话吗？此操作无法撤销。')) {
                console.log('删除会话:', id);
                // 这里执行删除操作
            }
        }

        // 平台过滤
        document.addEventListener('DOMContentLoaded', function() {
            const filterTags = document.querySelectorAll('[data-platform]');
            filterTags.forEach(tag => {
                tag.addEventListener('click', function() {
                    // 移除所有活跃状态
                    filterTags.forEach(t => {
                        t.classList.remove('bg-blue-100', 'text-blue-800');
                        t.classList.add('bg-gray-100', 'text-gray-700');
                    });

                    // 添加当前活跃状态
                    this.classList.remove('bg-gray-100', 'text-gray-700');
                    this.classList.add('bg-blue-100', 'text-blue-800');

                    const platform = this.dataset.platform;
                    console.log('过滤平台:', platform);
                });
            });
        });
    </script>
</body>
</html>
