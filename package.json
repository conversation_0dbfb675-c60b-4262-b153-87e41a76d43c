{"name": "ai-chat-memo", "version": "1.0.0", "description": "AI会话管理浏览器插件 - 自动保存、管理和导出AI对话", "main": "dist/background.js", "scripts": {"dev": "vite build --mode development --watch", "build": "vite build && node scripts/simple-fix.js", "build:prod": "vite build --mode production", "preview": "vite preview", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,css,md}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cd scripts && npm run analyze", "analyze:single": "cd scripts && npm run analyze:single", "clean": "<PERSON><PERSON><PERSON> dist", "zip": "node scripts/zip-extension.js"}, "keywords": ["browser-extension", "ai-chat", "conversation-manager", "chatgpt", "claude", "gemini", "chrome-extension", "firefox-addon"], "author": "AI Chat Memo Team", "license": "MIT", "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@types/chrome": "^0.0.254", "@types/jest": "^29.5.14", "@types/node": "^20.19.9", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.2", "eslint-plugin-prettier": "^5.5.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.6.2", "rimraf": "^5.0.10", "ts-jest": "^29.4.0", "typescript": "^5.3.2", "vite": "^5.0.0", "vite-plugin-static-copy": "^0.17.0"}, "dependencies": {"dexie": "^3.2.4", "fuse.js": "^7.0.0", "marked": "^9.1.6", "playwright": "^1.54.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/ai-chat-memo/extension.git"}, "bugs": {"url": "https://github.com/ai-chat-memo/extension/issues"}, "homepage": "https://github.com/ai-chat-memo/extension#readme"}