/**
 * 会话存储管理器
 * 负责会话数据的 CRUD 操作和业务逻辑
 */

import { ConversationData, MessageData } from '@types/conversation';
import { DatabaseManager } from './database';
import { SearchIndexManager } from './search-index';
import { Logger } from '@shared/logger';

export interface ConversationFilter {
  platform?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  tags?: string[];
  hasMessages?: boolean;
  isArchived?: boolean;
}

export interface ConversationSortOptions {
  field: 'createdAt' | 'updatedAt' | 'title' | 'messageCount';
  direction: 'asc' | 'desc';
}

export interface PaginationOptions {
  page: number;
  pageSize: number;
}

export interface ConversationListResult {
  conversations: ConversationData[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

export class ConversationStore {
  private db: DatabaseManager;
  private searchIndex: SearchIndexManager;
  private logger: Logger;

  constructor(db: DatabaseManager, searchIndex: SearchIndexManager) {
    this.db = db;
    this.searchIndex = searchIndex;
    this.logger = new Logger('ConversationStore');
  }

  /**
   * 保存会话
   */
  async saveConversation(conversation: ConversationData): Promise<void> {
    try {
      // 检查是否已存在
      const existing = await this.db.get('conversations', conversation.id);
      
      if (existing) {
        // 更新现有会话
        conversation.updatedAt = new Date();
        await this.updateConversation(conversation);
      } else {
        // 创建新会话
        conversation.createdAt = new Date();
        conversation.updatedAt = new Date();
        await this.createConversation(conversation);
      }

      this.logger.info(`会话已保存: ${conversation.title} (${conversation.messages.length} 条消息)`);
    } catch (error) {
      this.logger.error('保存会话失败:', error);
      throw error;
    }
  }

  /**
   * 创建新会话
   */
  private async createConversation(conversation: ConversationData): Promise<void> {
    const operations = [];

    // 添加会话记录
    operations.push({
      type: 'add' as const,
      storeName: 'conversations' as const,
      data: conversation
    });

    // 添加消息记录
    conversation.messages.forEach(message => {
      message.conversationId = conversation.id;
      operations.push({
        type: 'add' as const,
        storeName: 'messages' as const,
        data: message
      });
    });

    // 批量执行操作
    await this.db.batch(operations);

    // 更新搜索索引
    await this.searchIndex.indexConversation(conversation);

    // 更新标签计数
    await this.updateTagCounts(conversation.tags, 1);
  }

  /**
   * 更新会话
   */
  private async updateConversation(conversation: ConversationData): Promise<void> {
    // 获取现有消息
    const existingMessages = await this.db.getByIndex('messages', 'conversationId', conversation.id);
    const existingMessageIds = new Set(existingMessages.map(m => m.id));
    
    const operations = [];

    // 更新会话记录
    operations.push({
      type: 'put' as const,
      storeName: 'conversations' as const,
      data: conversation
    });

    // 处理消息更新
    conversation.messages.forEach(message => {
      message.conversationId = conversation.id;
      
      if (existingMessageIds.has(message.id)) {
        // 更新现有消息
        operations.push({
          type: 'put' as const,
          storeName: 'messages' as const,
          data: message
        });
      } else {
        // 添加新消息
        operations.push({
          type: 'add' as const,
          storeName: 'messages' as const,
          data: message
        });
      }
    });

    // 删除不再存在的消息
    const currentMessageIds = new Set(conversation.messages.map(m => m.id));
    existingMessages.forEach(message => {
      if (!currentMessageIds.has(message.id)) {
        operations.push({
          type: 'delete' as const,
          storeName: 'messages' as const,
          key: message.id
        });
      }
    });

    // 批量执行操作
    await this.db.batch(operations);

    // 更新搜索索引
    await this.searchIndex.updateConversationIndex(conversation);
  }

  /**
   * 获取会话
   */
  async getConversation(id: string): Promise<ConversationData | null> {
    try {
      const conversation = await this.db.get('conversations', id);
      if (!conversation) {
        return null;
      }

      // 获取关联的消息
      const messages = await this.db.getByIndex('messages', 'conversationId', id);
      
      // 按时间戳排序消息
      messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
      
      conversation.messages = messages;
      return conversation;
    } catch (error) {
      this.logger.error('获取会话失败:', error);
      throw error;
    }
  }

  /**
   * 删除会话
   */
  async deleteConversation(id: string): Promise<void> {
    try {
      const conversation = await this.db.get('conversations', id);
      if (!conversation) {
        throw new Error('会话不存在');
      }

      const operations = [];

      // 删除会话记录
      operations.push({
        type: 'delete' as const,
        storeName: 'conversations' as const,
        key: id
      });

      // 删除关联的消息
      const messages = await this.db.getByIndex('messages', 'conversationId', id);
      messages.forEach(message => {
        operations.push({
          type: 'delete' as const,
          storeName: 'messages' as const,
          key: message.id
        });
      });

      // 批量执行操作
      await this.db.batch(operations);

      // 删除搜索索引
      await this.searchIndex.removeConversationIndex(id);

      // 更新标签计数
      await this.updateTagCounts(conversation.tags, -1);

      this.logger.info(`会话已删除: ${conversation.title}`);
    } catch (error) {
      this.logger.error('删除会话失败:', error);
      throw error;
    }
  }

  /**
   * 获取会话列表
   */
  async getConversations(
    filter?: ConversationFilter,
    sort?: ConversationSortOptions,
    pagination?: PaginationOptions
  ): Promise<ConversationListResult> {
    try {
      let conversations = await this.db.getAll('conversations');

      // 应用过滤器
      if (filter) {
        conversations = await this.applyFilter(conversations, filter);
      }

      // 应用排序
      if (sort) {
        conversations = this.applySorting(conversations, sort);
      }

      // 计算总数
      const total = conversations.length;

      // 应用分页
      if (pagination) {
        const start = (pagination.page - 1) * pagination.pageSize;
        const end = start + pagination.pageSize;
        conversations = conversations.slice(start, end);
      }

      // 加载消息计数（不加载完整消息内容以提高性能）
      for (const conversation of conversations) {
        const messageCount = await this.db.count('messages', conversation.id);
        conversation.metadata.messageCount = messageCount;
        conversation.messages = []; // 清空消息内容以节省内存
      }

      return {
        conversations,
        total,
        page: pagination?.page || 1,
        pageSize: pagination?.pageSize || total,
        hasMore: pagination ? (pagination.page * pagination.pageSize) < total : false
      };
    } catch (error) {
      this.logger.error('获取会话列表失败:', error);
      throw error;
    }
  }

  /**
   * 应用过滤器
   */
  private async applyFilter(
    conversations: ConversationData[], 
    filter: ConversationFilter
  ): Promise<ConversationData[]> {
    let filtered = conversations;

    // 平台过滤
    if (filter.platform) {
      filtered = filtered.filter(c => c.platform === filter.platform);
    }

    // 日期范围过滤
    if (filter.dateRange) {
      filtered = filtered.filter(c => 
        c.createdAt >= filter.dateRange!.start && 
        c.createdAt <= filter.dateRange!.end
      );
    }

    // 标签过滤
    if (filter.tags && filter.tags.length > 0) {
      filtered = filtered.filter(c => 
        filter.tags!.some(tag => c.tags.includes(tag))
      );
    }

    // 消息存在过滤
    if (filter.hasMessages !== undefined) {
      if (filter.hasMessages) {
        filtered = filtered.filter(c => c.metadata.messageCount > 0);
      } else {
        filtered = filtered.filter(c => c.metadata.messageCount === 0);
      }
    }

    // 归档状态过滤
    if (filter.isArchived !== undefined) {
      filtered = filtered.filter(c => c.metadata.isArchived === filter.isArchived);
    }

    return filtered;
  }

  /**
   * 应用排序
   */
  private applySorting(
    conversations: ConversationData[], 
    sort: ConversationSortOptions
  ): ConversationData[] {
    return conversations.sort((a, b) => {
      let valueA: any, valueB: any;

      switch (sort.field) {
        case 'createdAt':
          valueA = a.createdAt.getTime();
          valueB = b.createdAt.getTime();
          break;
        case 'updatedAt':
          valueA = a.updatedAt.getTime();
          valueB = b.updatedAt.getTime();
          break;
        case 'title':
          valueA = a.title.toLowerCase();
          valueB = b.title.toLowerCase();
          break;
        case 'messageCount':
          valueA = a.metadata.messageCount;
          valueB = b.metadata.messageCount;
          break;
        default:
          return 0;
      }

      if (sort.direction === 'asc') {
        return valueA < valueB ? -1 : valueA > valueB ? 1 : 0;
      } else {
        return valueA > valueB ? -1 : valueA < valueB ? 1 : 0;
      }
    });
  }

  /**
   * 更新标签计数
   */
  private async updateTagCounts(tags: string[], delta: number): Promise<void> {
    for (const tagName of tags) {
      try {
        const existing = await this.db.get('tags', tagName);
        
        if (existing) {
          existing.count += delta;
          if (existing.count <= 0) {
            await this.db.delete('tags', tagName);
          } else {
            await this.db.put('tags', existing);
          }
        } else if (delta > 0) {
          await this.db.add('tags', {
            id: tagName,
            name: tagName,
            color: this.generateTagColor(tagName),
            count: delta,
            createdAt: new Date()
          });
        }
      } catch (error) {
        this.logger.warn(`更新标签计数失败: ${tagName}`, error);
      }
    }
  }

  /**
   * 生成标签颜色
   */
  private generateTagColor(tagName: string): string {
    const colors = [
      '#3B82F6', '#EF4444', '#10B981', '#F59E0B', 
      '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'
    ];
    
    let hash = 0;
    for (let i = 0; i < tagName.length; i++) {
      hash = tagName.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  }

  /**
   * 获取统计信息
   */
  async getStatistics(): Promise<{
    totalConversations: number;
    totalMessages: number;
    platformStats: Record<string, number>;
    recentActivity: { date: string; count: number }[];
  }> {
    try {
      const totalConversations = await this.db.count('conversations');
      const totalMessages = await this.db.count('messages');
      
      const conversations = await this.db.getAll('conversations');
      
      // 平台统计
      const platformStats: Record<string, number> = {};
      conversations.forEach(c => {
        platformStats[c.platform] = (platformStats[c.platform] || 0) + 1;
      });

      // 最近活动统计（最近7天）
      const recentActivity = [];
      const now = new Date();
      for (let i = 6; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];
        
        const count = conversations.filter(c => 
          c.createdAt.toISOString().split('T')[0] === dateStr
        ).length;
        
        recentActivity.push({ date: dateStr, count });
      }

      return {
        totalConversations,
        totalMessages,
        platformStats,
        recentActivity
      };
    } catch (error) {
      this.logger.error('获取统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 永久删除指定日期之前的会话
   */
  async permanentDeleteBefore(beforeDate: Date): Promise<number> {
    try {
      // 获取需要删除的会话
      const range = IDBKeyRange.upperBound(beforeDate);
      const conversations = await this.db.getByRange('conversations', 'updatedAt', range);

      // 只删除已标记为删除的会话
      const deletedConversations = conversations.filter(c => c.metadata?.isDeleted);

      if (deletedConversations.length === 0) {
        return 0;
      }

      const operations = [];

      // 删除会话和相关数据
      for (const conversation of deletedConversations) {
        // 删除会话记录
        operations.push({
          type: 'delete' as const,
          storeName: 'conversations' as const,
          key: conversation.id
        });

        // 删除关联的消息
        const messages = await this.db.getByIndex('messages', 'conversationId', conversation.id);
        messages.forEach(message => {
          operations.push({
            type: 'delete' as const,
            storeName: 'messages' as const,
            key: message.id
          });
        });

        // 删除搜索索引
        await this.searchIndex.removeConversationIndex(conversation.id);

        // 更新标签计数
        await this.updateTagCounts(conversation.tags, -1);
      }

      // 批量执行删除操作
      if (operations.length > 0) {
        await this.db.batch(operations);
      }

      this.logger.info(`永久删除了 ${deletedConversations.length} 个会话`);
      return deletedConversations.length;
    } catch (error) {
      this.logger.error('永久删除会话失败:', error);
      throw error;
    }
  }
}
