# AI 会话管理插件需求文档

## 1. 项目目标

开发一款浏览器插件，实现对主流 AI 聊天网站（如 ChatGPT、<PERSON>、Gemini、<PERSON><PERSON><PERSON><PERSON>、Monica、Poe）的会话内容自动归档、标签管理、检索与导出等功能，提升多平台会话的管理效率与体验。

---

## 2. 支持平台

- ChatGPT
- Claude
- Gemini
- Aistudio
- Monica
- Poe

> 说明：后续适配新平台由开发端维护，无需用户自定义规则。

---

## 3. 会话(thread)识别与增量合并

- **唯一标识**：以会话页面 URL 作为唯一标识（thread）。
- **内容合并**：同一 URL 下的新消息增量合并到同一条本地记录。
- **内容去重**：合并时对问答内容去重，防止重复保存。
- **保存内容**：
  - 仅保存“问答对”文本内容。
  - 图片、附件等非文本内容以 placeholder 形式保存（如“[图片]”“[附件]”）。
  - 每条记录附带原会话链接，支持一键跳转至原页面。

---

## 4. 自动保存机制

- **自动保存选项**：全局设置，用户可在设置页开启或关闭。
- **保存时机**：每次有新消息即自动保存，无频率限制。

---

## 5. 标签与备注管理

- **标签**：
  - 自动+手动混合。
  - 自动标签需包含平台简称。
  - 支持用户自定义标签颜色、分组。
  - 支持批量编辑标签。
- **备注**：每条会话记录可随时编辑备注信息。

---

## 6. 检索与高亮

- **检索方式**：
  - 支持全文检索和标签检索。
  - 检索结果支持关键词高亮显示。

---

## 7. 导出功能

- **批量导出**：
  - 支持多条记录批量导出。
  - 支持 Markdown 和 PDF 两种格式。
  - 导出内容采用固定模板格式。

---

## 8. 本地数据管理

- **数据存储**：所有数据本地存储，暂不加密。
- **备份策略**：初版不做本地备份，后续支持云端同步。
- **插件权限**：仅声明实际开发所需权限。

---

## 9. 错误处理与日志

- **异常提示**：保存失败等异常情况需前端即时弹窗/提示用户。
- **错误日志**：记录错误日志，便于后续排查和用户反馈，建议支持导出。

---

## 10. 会话列表与性能优化

- **会话列表展示**：
  - 支持卡片式和列表式两种视图，用户可切换。
  - 支持侧边栏查找、批量管理（如批量删除、加标签等）。
- **性能优化**：
  - 会话数量大时支持分页或懒加载，保证流畅体验。

---

## 11. 云端同步与账号体系（后续版本）

- **数据结构**：本地与云端数据结构保持一致。
- **同步机制**：初版不考虑数据冲突处理，后续如有需求再扩展。
- **账号体系**：后续支持微信、飞书、Google 等第三方一键登录。

---

## 12. 平台适配与通知

- **AI 网站改版**：如遇平台结构变化导致插件失效，需通过插件更新修复，并以消息通知用户。

---

## 13. 非支持范围

- **API 开放**：暂不开放 API。
- **用户自定义会话保存规则**：暂不支持。
- **本地数据加密与备份**：初版不支持，后续云同步时再考虑。

---

## 14. 其他建议

- 优先设计好数据结构和增量合并伪代码，保证核心功能先行落地。
- 分页/懒加载建议提前预留接口，便于后续会话量大时直接扩展。

---

## 15. 数据结构建议（示例）

```json
{
  "thread_id": "唯一URL或自定义ID",
  "platform": "ChatGPT",
  "url": "https://chat.openai.com/c/xxx",
  "qa_pairs": [
    {
      "question": "用户提问内容",
      "answer": "AI回复内容"
    }
  ],
  "placeholders": [
    "[图片]",
    "[附件]"
  ],
  "tags": ["ChatGPT", "自定义标签"],
  "remark": "备注内容",
  "created_at": "时间戳",
  "updated_at": "时间戳"
}
```
