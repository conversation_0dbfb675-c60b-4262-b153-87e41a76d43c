/**
 * Options 入口文件
 * 插件设置页面的主要逻辑
 */

import { MessageBus } from '@shared/message-bus';
import { Logger } from '@shared/logger';
import { SettingsManager } from '@shared/settings';
import { EVENTS } from '@types/index';
import type { Platform } from '@types/platform';

interface OptionsState {
  currentSection: string;
  settings: any;
  isLoading: boolean;
  isDirty: boolean;
}

interface SettingsSection {
  id: string;
  title: string;
  description: string;
  render: () => string;
}

class OptionsService {
  private messageBus: MessageBus;
  private logger: Logger;
  private settingsManager: SettingsManager;
  private state: OptionsState;
  private sections: Map<string, SettingsSection>;

  constructor() {
    this.logger = new Logger('Options');
    this.messageBus = new MessageBus('options');
    this.settingsManager = new SettingsManager();
    this.state = {
      currentSection: 'general',
      settings: {},
      isLoading: false,
      isDirty: false
    };
    this.sections = new Map();

    this.logger.info('Options service initializing...');
    this.init();
  }

  private async init(): Promise<void> {
    this.logger.info('Options service initialized');
    this.initializeSections();
    this.setupEventListeners();
    await this.loadSettings();
  }

  private initializeSections(): void {
    this.sections.set('general', {
      id: 'general',
      title: '基本设置',
      description: '配置插件的基本功能和行为',
      render: () => this.renderGeneralSettings()
    });

    this.sections.set('platforms', {
      id: 'platforms',
      title: '平台设置',
      description: '管理支持的AI平台和检测规则',
      render: () => this.renderPlatformSettings()
    });

    this.sections.set('tags', {
      id: 'tags',
      title: '标签管理',
      description: '配置自动标签生成和标签规则',
      render: () => this.renderTagSettings()
    });

    this.sections.set('storage', {
      id: 'storage',
      title: '存储管理',
      description: '管理本地数据存储和清理策略',
      render: () => this.renderStorageSettings()
    });

    this.sections.set('export', {
      id: 'export',
      title: '导出设置',
      description: '配置数据导出格式和选项',
      render: () => this.renderExportSettings()
    });

    this.sections.set('privacy', {
      id: 'privacy',
      title: '隐私设置',
      description: '管理数据隐私和安全选项',
      render: () => this.renderPrivacySettings()
    });

    this.sections.set('advanced', {
      id: 'advanced',
      title: '高级设置',
      description: '高级功能和开发者选项',
      render: () => this.renderAdvancedSettings()
    });
  }

  private setupEventListeners(): void {
    document.addEventListener('DOMContentLoaded', () => {
      this.logger.info('Options DOM loaded');
      this.bindUIEvents();
      this.showSection(this.state.currentSection);
    });
  }

  private bindUIEvents(): void {
    // 侧边栏导航
    document.querySelectorAll('.sidebar-item').forEach(item => {
      item.addEventListener('click', (e) => {
        const section = (e.currentTarget as HTMLElement).dataset.section;
        if (section) {
          this.showSection(section);
        }
      });
    });

    // 保存按钮
    const saveBtn = document.getElementById('save-btn');
    if (saveBtn) {
      saveBtn.addEventListener('click', () => {
        this.saveSettings();
      });
    }

    // 重置按钮
    const resetBtn = document.getElementById('reset-btn');
    if (resetBtn) {
      resetBtn.addEventListener('click', () => {
        this.resetSettings();
      });
    }

    // 监听表单变化
    document.addEventListener('input', (e) => {
      if ((e.target as HTMLElement).closest('#content')) {
        this.state.isDirty = true;
        this.updateSaveButtonState();
      }
    });

    document.addEventListener('change', (e) => {
      if ((e.target as HTMLElement).closest('#content')) {
        this.state.isDirty = true;
        this.updateSaveButtonState();
      }
    });
  }

  private async loadSettings(): Promise<void> {
    this.setLoading(true);
    try {
      const response = await this.messageBus.sendMessage({
        type: EVENTS.SETTINGS.GET_ALL,
        data: {}
      });

      if (response.success) {
        this.state.settings = response.settings || {};
        this.state.isDirty = false;
        this.updateSaveButtonState();
      } else {
        throw new Error(response.error || 'Failed to load settings');
      }
    } catch (error) {
      this.logger.error('Failed to load settings:', error);
      this.showError('加载设置失败，请刷新页面重试');
    } finally {
      this.setLoading(false);
    }
  }

  private async saveSettings(): Promise<void> {
    if (!this.state.isDirty) return;

    this.setLoading(true);
    try {
      // 收集表单数据
      const formData = this.collectFormData();

      const response = await this.messageBus.sendMessage({
        type: EVENTS.SETTINGS.UPDATE,
        data: { settings: formData }
      });

      if (response.success) {
        this.state.settings = { ...this.state.settings, ...formData };
        this.state.isDirty = false;
        this.updateSaveButtonState();
        this.showSuccess('设置已保存');
      } else {
        throw new Error(response.error || 'Failed to save settings');
      }
    } catch (error) {
      this.logger.error('Failed to save settings:', error);
      this.showError('保存设置失败，请重试');
    } finally {
      this.setLoading(false);
    }
  }

  private async resetSettings(): Promise<void> {
    if (!confirm('确定要重置所有设置吗？此操作无法撤销。')) {
      return;
    }

    this.setLoading(true);
    try {
      const response = await this.messageBus.sendMessage({
        type: EVENTS.SETTINGS.RESET,
        data: {}
      });

      if (response.success) {
        await this.loadSettings();
        this.showSection(this.state.currentSection); // 重新渲染当前页面
        this.showSuccess('设置已重置');
      } else {
        throw new Error(response.error || 'Failed to reset settings');
      }
    } catch (error) {
      this.logger.error('Failed to reset settings:', error);
      this.showError('重置设置失败，请重试');
    } finally {
      this.setLoading(false);
    }
  }

  private showSection(sectionId: string): void {
    const section = this.sections.get(sectionId);
    if (!section) return;

    // 更新状态
    this.state.currentSection = sectionId;

    // 更新侧边栏
    document.querySelectorAll('.sidebar-item').forEach(item => {
      item.classList.remove('bg-blue-50', 'text-blue-700', 'border-r-2', 'border-blue-600');
      item.classList.add('text-gray-600', 'hover:bg-gray-50', 'hover:text-gray-900');
    });

    const activeItem = document.querySelector(`[data-section="${sectionId}"]`);
    if (activeItem) {
      activeItem.classList.remove('text-gray-600', 'hover:bg-gray-50', 'hover:text-gray-900');
      activeItem.classList.add('bg-blue-50', 'text-blue-700', 'border-r-2', 'border-blue-600');
    }

    // 更新页面标题
    const titleEl = document.getElementById('page-title');
    const descEl = document.getElementById('page-description');
    if (titleEl) titleEl.textContent = section.title;
    if (descEl) descEl.textContent = section.description;

    // 渲染内容
    this.renderSectionContent(section);
  }

  private renderSectionContent(section: SettingsSection): void {
    const contentEl = document.getElementById('content');
    if (!contentEl) return;

    contentEl.innerHTML = section.render();
    contentEl.classList.remove('hidden');

    // 绑定新渲染内容的事件
    this.bindSectionEvents(section.id);
  }

  private bindSectionEvents(sectionId: string): void {
    // 根据不同的section绑定特定的事件
    switch (sectionId) {
      case 'platforms':
        this.bindPlatformEvents();
        break;
      case 'tags':
        this.bindTagEvents();
        break;
      case 'storage':
        this.bindStorageEvents();
        break;
      case 'export':
        this.bindExportEvents();
        break;
      default:
        break;
    }
  }

  private renderGeneralSettings(): string {
    const settings = this.state.settings;
    return `
      <div class="space-y-6">
        <!-- 自动保存设置 -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">自动保存</h3>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-700">启用自动保存</label>
                <p class="text-sm text-gray-500">自动保存AI对话内容到本地</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" class="sr-only peer" name="autoSave" ${settings.autoSave ? 'checked' : ''}>
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-700">保存频率</label>
                <p class="text-sm text-gray-500">检测到新消息后的保存延迟</p>
              </div>
              <select name="saveDelay" class="block w-32 px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500">
                <option value="0" ${settings.saveDelay === 0 ? 'selected' : ''}>立即</option>
                <option value="1000" ${settings.saveDelay === 1000 ? 'selected' : ''}>1秒</option>
                <option value="3000" ${settings.saveDelay === 3000 ? 'selected' : ''}>3秒</option>
                <option value="5000" ${settings.saveDelay === 5000 ? 'selected' : ''}>5秒</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 界面设置 -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">界面设置</h3>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-700">显示悬浮指示器</label>
                <p class="text-sm text-gray-500">在支持的页面显示保存状态指示器</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" class="sr-only peer" name="showFloatingIndicator" ${settings.showFloatingIndicator ? 'checked' : ''}>
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-700">默认视图模式</label>
                <p class="text-sm text-gray-500">弹窗中会话列表的默认显示方式</p>
              </div>
              <select name="defaultViewMode" class="block w-32 px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500">
                <option value="card" ${settings.defaultViewMode === 'card' ? 'selected' : ''}>卡片</option>
                <option value="list" ${settings.defaultViewMode === 'list' ? 'selected' : ''}>列表</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 通知设置 -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">通知设置</h3>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-700">保存成功通知</label>
                <p class="text-sm text-gray-500">会话保存成功时显示通知</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" class="sr-only peer" name="showSaveNotification" ${settings.showSaveNotification ? 'checked' : ''}>
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-700">错误通知</label>
                <p class="text-sm text-gray-500">发生错误时显示通知</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" class="sr-only peer" name="showErrorNotification" ${settings.showErrorNotification ? 'checked' : ''}>
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderPlatformSettings(): string {
    const settings = this.state.settings;
    const platforms = ['chatgpt', 'claude', 'gemini', 'aistudio', 'monica', 'poe'];
    const platformNames = {
      'chatgpt': 'ChatGPT',
      'claude': 'Claude',
      'gemini': 'Gemini',
      'aistudio': 'AI Studio',
      'monica': 'Monica',
      'poe': 'Poe'
    };
    const platformColors = {
      'chatgpt': 'bg-chatgpt',
      'claude': 'bg-claude',
      'gemini': 'bg-gemini',
      'aistudio': 'bg-aistudio',
      'monica': 'bg-monica',
      'poe': 'bg-poe'
    };

    return `
      <div class="space-y-6">
        <!-- 平台启用状态 -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">支持的平台</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            ${platforms.map(platform => `
              <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div class="flex items-center space-x-3">
                  <div class="w-4 h-4 rounded-full ${platformColors[platform as Platform]}"></div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">${platformNames[platform as Platform]}</div>
                    <div class="text-xs text-gray-500">自动检测和保存对话</div>
                  </div>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" class="sr-only peer" name="platform_${platform}" ${settings.platforms?.[platform]?.enabled !== false ? 'checked' : ''}>
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            `).join('')}
          </div>
        </div>

        <!-- 检测设置 -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">检测设置</h3>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-700">检测间隔</label>
                <p class="text-sm text-gray-500">页面内容变化检测的时间间隔</p>
              </div>
              <select name="detectionInterval" class="block w-32 px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500">
                <option value="1000" ${settings.detectionInterval === 1000 ? 'selected' : ''}>1秒</option>
                <option value="2000" ${settings.detectionInterval === 2000 ? 'selected' : ''}>2秒</option>
                <option value="5000" ${settings.detectionInterval === 5000 ? 'selected' : ''}>5秒</option>
                <option value="10000" ${settings.detectionInterval === 10000 ? 'selected' : ''}>10秒</option>
              </select>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-700">最小消息长度</label>
                <p class="text-sm text-gray-500">保存消息的最小字符数</p>
              </div>
              <input type="number" name="minMessageLength" value="${settings.minMessageLength || 10}" min="1" max="1000"
                     class="block w-24 px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500">
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderTagSettings(): string {
    return `
      <div class="space-y-6">
        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">标签管理</h3>
          <p class="text-gray-600">标签管理功能正在开发中...</p>
        </div>
      </div>
    `;
  }

  private renderStorageSettings(): string {
    return `
      <div class="space-y-6">
        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">存储管理</h3>
          <p class="text-gray-600">存储管理功能正在开发中...</p>
        </div>
      </div>
    `;
  }

  private renderExportSettings(): string {
    return `
      <div class="space-y-6">
        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">导出设置</h3>
          <p class="text-gray-600">导出设置功能正在开发中...</p>
        </div>
      </div>
    `;
  }

  private renderPrivacySettings(): string {
    return `
      <div class="space-y-6">
        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">隐私设置</h3>
          <p class="text-gray-600">隐私设置功能正在开发中...</p>
        </div>
      </div>
    `;
  }

  private renderAdvancedSettings(): string {
    return `
      <div class="space-y-6">
        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">高级设置</h3>
          <p class="text-gray-600">高级设置功能正在开发中...</p>
        </div>
      </div>
    `;
  }

  // 事件绑定方法
  private bindPlatformEvents(): void {
    // 平台相关事件
  }

  private bindTagEvents(): void {
    // 标签相关事件
  }

  private bindStorageEvents(): void {
    // 存储相关事件
  }

  private bindExportEvents(): void {
    // 导出相关事件
  }

  // 工具方法
  private collectFormData(): any {
    const formData: any = {};
    const form = document.getElementById('content');
    if (!form) return formData;

    // 收集所有表单数据
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach((input: any) => {
      if (input.name) {
        if (input.type === 'checkbox') {
          formData[input.name] = input.checked;
        } else {
          formData[input.name] = input.value;
        }
      }
    });

    return formData;
  }

  private setLoading(loading: boolean): void {
    const loadingEl = document.getElementById('loading');
    const contentEl = document.getElementById('content');

    if (loadingEl && contentEl) {
      if (loading) {
        loadingEl.classList.remove('hidden');
        contentEl.classList.add('hidden');
      } else {
        loadingEl.classList.add('hidden');
        contentEl.classList.remove('hidden');
      }
    }
  }

  private showError(message: string): void {
    const errorEl = document.getElementById('error');
    const messageEl = document.getElementById('error-message');

    if (errorEl && messageEl) {
      messageEl.textContent = message;
      errorEl.classList.remove('hidden');
      setTimeout(() => {
        errorEl.classList.add('hidden');
      }, 5000);
    }
  }

  private showSuccess(message: string): void {
    const successEl = document.getElementById('success');
    const messageEl = document.getElementById('success-message');

    if (successEl && messageEl) {
      messageEl.textContent = message;
      successEl.classList.remove('hidden');
      setTimeout(() => {
        successEl.classList.add('hidden');
      }, 3000);
    }
  }

  private updateSaveButtonState(): void {
    const saveBtn = document.getElementById('save-btn');
    if (saveBtn) {
      if (this.state.isDirty) {
        saveBtn.classList.remove('bg-gray-400');
        saveBtn.classList.add('bg-blue-600', 'hover:bg-blue-700');
        (saveBtn as HTMLButtonElement).disabled = false;
      } else {
        saveBtn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
        saveBtn.classList.add('bg-gray-400');
        (saveBtn as HTMLButtonElement).disabled = true;
      }
    }
  }
}

// 初始化服务
const optionsService = new OptionsService();

// 导出服务实例
export default optionsService;
