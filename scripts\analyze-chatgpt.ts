/**
 * ChatGPT 页面结构分析脚本
 * 使用 Playwright 实际访问 ChatGPT 网站并分析页面结构
 */

import { PageStructureAnalyzer } from './page-analyzer/analyzer';

async function main() {
  console.log('🚀 开始分析 ChatGPT 页面结构...\n');

  const analyzer = new PageStructureAnalyzer();

  try {
    await analyzer.initialize();
    
    console.log('📋 即将分析 ChatGPT 平台');
    console.log('⚠️  注意：如果需要登录，请在浏览器中完成登录后按回车继续');
    console.log('💡 建议：准备一个已登录的 ChatGPT 账号以获得最佳分析结果\n');

    const analysis = await analyzer.analyzePlatform('ChatGPT');

    if (analysis) {
      console.log('\n📊 分析结果摘要:');
      console.log(`平台: ${analysis.platform}`);
      console.log(`URL: ${analysis.url}`);
      console.log(`时间: ${analysis.timestamp}`);
      console.log(`消息容器数量: ${analysis.messageContainers.length}`);
      console.log(`用户消息元素: ${analysis.messageStructure.userElements.length}`);
      console.log(`助手消息元素: ${analysis.messageStructure.assistantElements.length}`);
      console.log(`输入框数量: ${analysis.inputElements.inputs.length}`);
      console.log(`按钮数量: ${analysis.inputElements.buttons.length}`);
      console.log(`推荐置信度: ${analysis.recommendations.confidence}%`);

      console.log('\n🎯 推荐的选择器:');
      console.log(`对话容器: ${analysis.recommendations.conversationContainer}`);
      console.log(`用户消息: ${analysis.recommendations.userMessage}`);
      console.log(`助手消息: ${analysis.recommendations.assistantMessage}`);
      console.log(`消息输入: ${analysis.recommendations.messageInput}`);
      console.log(`发送按钮: ${analysis.recommendations.sendButton}`);

      // 显示找到的关键元素
      if (analysis.messageContainers.length > 0) {
        console.log('\n📦 找到的消息容器:');
        analysis.messageContainers.slice(0, 3).forEach((container, index) => {
          console.log(`  ${index + 1}. ${container.selector}`);
          console.log(`     类名: ${container.className}`);
          console.log(`     子元素数: ${container.childCount}`);
          if (container.dataAttributes.length > 0) {
            console.log(`     数据属性: ${container.dataAttributes.map(attr => `${attr.name}="${attr.value}"`).join(', ')}`);
          }
        });
      }

      if (analysis.messageStructure.userElements.length > 0) {
        console.log('\n👤 找到的用户消息元素:');
        analysis.messageStructure.userElements.slice(0, 2).forEach((element, index) => {
          console.log(`  ${index + 1}. ${element.selector}`);
          console.log(`     类名: ${element.className}`);
          console.log(`     内容预览: ${element.textContent.slice(0, 50)}...`);
        });
      }

      if (analysis.messageStructure.assistantElements.length > 0) {
        console.log('\n🤖 找到的助手消息元素:');
        analysis.messageStructure.assistantElements.slice(0, 2).forEach((element, index) => {
          console.log(`  ${index + 1}. ${element.selector}`);
          console.log(`     类名: ${element.className}`);
          console.log(`     内容预览: ${element.textContent.slice(0, 50)}...`);
        });
      }

      if (analysis.inputElements.inputs.length > 0) {
        console.log('\n📝 找到的输入元素:');
        analysis.inputElements.inputs.slice(0, 2).forEach((input, index) => {
          console.log(`  ${index + 1}. ${input.selector}`);
          console.log(`     标签: ${input.tagName}`);
          console.log(`     占位符: ${input.textContent}`);
        });
      }

      console.log('\n✅ 分析完成！');
      console.log(`📁 详细结果已保存到: analysis/results/chatgpt.json`);
      console.log(`📸 页面截图已保存到: analysis/screenshots/chatgpt.png`);

    } else {
      console.log('❌ 分析失败，请检查网络连接和登录状态');
    }

  } catch (error) {
    console.error('❌ 分析过程中出现错误:', error);
  } finally {
    await analyzer.cleanup();
  }
}

// 处理程序退出
process.on('SIGINT', async () => {
  console.log('\n🛑 收到中断信号，正在清理...');
  process.exit(0);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

if (require.main === module) {
  main().catch(console.error);
}
