/**
 * 备份管理器单元测试
 */

import { BackupManager } from '../../../src/core/backup/backup-manager';
import { ConversationData } from '../../../src/types/conversation';

describe('BackupManager', () => {
  let backupManager: BackupManager;
  let mockConversations: ConversationData[];

  beforeEach(() => {
    const config = {
      autoBackup: false, // 测试中禁用自动备份
      backupInterval: 24,
      maxBackups: 10,
      compression: true,
      encryption: false // 测试中禁用加密以简化
    };

    backupManager = new BackupManager(config);
    
    // 创建测试数据
    mockConversations = [
      {
        id: 'conv_001',
        platform: 'ChatGPT',
        title: 'JavaScript异步编程讨论',
        url: 'https://chat.openai.com/c/123',
        timestamp: new Date('2024-01-15T10:30:00Z'),
        messages: [
          {
            id: 'msg_001',
            role: 'user',
            content: '请解释一下JavaScript中的Promise和async/await的区别',
            timestamp: new Date('2024-01-15T10:30:00Z')
          },
          {
            id: 'msg_002',
            role: 'assistant',
            content: 'Promise和async/await都是处理异步操作的方式。',
            timestamp: new Date('2024-01-15T10:31:00Z')
          }
        ],
        metadata: {
          tags: ['JavaScript', '异步编程', 'Promise']
        }
      },
      {
        id: 'conv_002',
        platform: 'Claude',
        title: 'React性能优化技巧',
        url: 'https://claude.ai/chat/456',
        timestamp: new Date('2024-01-16T14:20:00Z'),
        messages: [
          {
            id: 'msg_003',
            role: 'user',
            content: '如何优化React应用的性能？',
            timestamp: new Date('2024-01-16T14:20:00Z')
          }
        ],
        metadata: {
          tags: ['React', '性能优化']
        }
      }
    ];
  });

  afterEach(() => {
    backupManager.destroy();
    jest.clearAllMocks();
  });

  describe('备份创建', () => {
    test('应该能够创建备份', async () => {
      const description = '测试备份';
      const metadata = await backupManager.createBackup(mockConversations, description);
      
      expect(metadata.id).toBeDefined();
      expect(metadata.description).toBe(description);
      expect(metadata.conversationCount).toBe(2);
      expect(metadata.messageCount).toBe(3);
      expect(metadata.size).toBeGreaterThan(0);
      expect(metadata.compressed).toBe(true);
      expect(metadata.encrypted).toBe(false);
      expect(metadata.checksum).toBeDefined();
    });

    test('应该生成唯一的备份ID', async () => {
      const metadata1 = await backupManager.createBackup(mockConversations);
      const metadata2 = await backupManager.createBackup(mockConversations);
      
      expect(metadata1.id).not.toBe(metadata2.id);
    });

    test('应该正确计算备份大小', async () => {
      const metadata = await backupManager.createBackup(mockConversations);
      
      // 备份大小应该大于原始数据大小（包含元数据）
      const originalSize = JSON.stringify(mockConversations).length;
      expect(metadata.size).toBeGreaterThan(originalSize * 0.5); // 考虑压缩
    });

    test('应该生成校验和', async () => {
      const metadata = await backupManager.createBackup(mockConversations);
      
      expect(metadata.checksum).toMatch(/^[a-f0-9]{64}$/); // SHA-256 hex格式
    });

    test('应该处理空对话列表', async () => {
      const metadata = await backupManager.createBackup([]);
      
      expect(metadata.conversationCount).toBe(0);
      expect(metadata.messageCount).toBe(0);
      expect(metadata.size).toBeGreaterThan(0); // 仍有元数据
    });
  });

  describe('备份列表管理', () => {
    test('应该能够获取备份列表', async () => {
      await backupManager.createBackup(mockConversations, '备份1');
      await backupManager.createBackup(mockConversations, '备份2');
      
      const backups = await backupManager.getBackupList();
      
      expect(backups).toHaveLength(2);
      expect(backups[0].description).toBe('备份2'); // 最新的在前
      expect(backups[1].description).toBe('备份1');
    });

    test('应该按时间倒序排列备份', async () => {
      const backup1 = await backupManager.createBackup(mockConversations, '旧备份');
      
      // 等待一小段时间确保时间戳不同
      await new Promise(resolve => setTimeout(resolve, 10));
      
      const backup2 = await backupManager.createBackup(mockConversations, '新备份');
      
      const backups = await backupManager.getBackupList();
      
      expect(backups[0].id).toBe(backup2.id);
      expect(backups[1].id).toBe(backup1.id);
    });

    test('应该能够删除备份', async () => {
      const metadata = await backupManager.createBackup(mockConversations);
      
      let backups = await backupManager.getBackupList();
      expect(backups).toHaveLength(1);
      
      await backupManager.deleteBackup(metadata.id);
      
      backups = await backupManager.getBackupList();
      expect(backups).toHaveLength(0);
    });

    test('应该处理删除不存在的备份', async () => {
      await expect(backupManager.deleteBackup('non-existent-id')).rejects.toThrow('备份不存在');
    });
  });

  describe('备份恢复', () => {
    test('应该能够恢复备份', async () => {
      const metadata = await backupManager.createBackup(mockConversations);
      
      const restoredData = await backupManager.restoreBackup(metadata.id);
      
      expect(restoredData.conversations).toHaveLength(2);
      expect(restoredData.conversations[0].id).toBe('conv_001');
      expect(restoredData.conversations[1].id).toBe('conv_002');
      expect(restoredData.settings).toBeDefined();
    });

    test('应该验证备份完整性', async () => {
      const metadata = await backupManager.createBackup(mockConversations);
      
      // 模拟损坏的备份数据
      const corruptedBackup = {
        ...metadata,
        checksum: 'invalid-checksum'
      };
      
      // 手动更新存储中的备份元数据
      const backupManager_: any = backupManager;
      const backups = await backupManager_.getStoredBackups();
      backups[metadata.id] = corruptedBackup;
      await backupManager_.saveBackups(backups);
      
      await expect(backupManager.restoreBackup(metadata.id)).rejects.toThrow('备份数据已损坏');
    });

    test('应该处理恢复不存在的备份', async () => {
      await expect(backupManager.restoreBackup('non-existent-id')).rejects.toThrow('备份不存在');
    });
  });

  describe('备份导入导出', () => {
    test('应该能够导出备份', async () => {
      const metadata = await backupManager.createBackup(mockConversations, '测试导出');
      
      const exportResult = await backupManager.exportBackup(metadata.id);
      
      expect(exportResult.filename).toMatch(/\.backup$/);
      expect(exportResult.blob).toBeInstanceOf(Blob);
      expect(exportResult.size).toBeGreaterThan(0);
    });

    test('应该能够导入备份', async () => {
      // 先创建并导出一个备份
      const originalMetadata = await backupManager.createBackup(mockConversations, '原始备份');
      const exportResult = await backupManager.exportBackup(originalMetadata.id);
      
      // 删除原备份
      await backupManager.deleteBackup(originalMetadata.id);
      
      // 模拟文件对象
      const mockFile = new File([exportResult.blob], exportResult.filename, {
        type: 'application/octet-stream'
      });
      
      // 导入备份
      const importedMetadata = await backupManager.importBackup(mockFile);
      
      expect(importedMetadata.description).toBe('原始备份');
      expect(importedMetadata.conversationCount).toBe(2);
      
      // 验证可以恢复
      const restoredData = await backupManager.restoreBackup(importedMetadata.id);
      expect(restoredData.conversations).toHaveLength(2);
    });

    test('应该处理无效的备份文件', async () => {
      const invalidFile = new File(['invalid content'], 'invalid.backup', {
        type: 'application/octet-stream'
      });
      
      await expect(backupManager.importBackup(invalidFile)).rejects.toThrow('无效的备份文件格式');
    });
  });

  describe('自动备份', () => {
    test('应该能够启用自动备份', () => {
      const startAutoBackupSpy = jest.spyOn(backupManager as any, 'startAutoBackup');
      
      backupManager.updateConfig({ autoBackup: true });
      
      expect(startAutoBackupSpy).toHaveBeenCalled();
      
      startAutoBackupSpy.mockRestore();
    });

    test('应该能够禁用自动备份', () => {
      const stopAutoBackupSpy = jest.spyOn(backupManager as any, 'stopAutoBackup');
      
      backupManager.updateConfig({ autoBackup: false });
      
      expect(stopAutoBackupSpy).toHaveBeenCalled();
      
      stopAutoBackupSpy.mockRestore();
    });

    test('应该在达到最大备份数时删除旧备份', async () => {
      // 设置最大备份数为2
      backupManager.updateConfig({ maxBackups: 2 });
      
      // 创建3个备份
      await backupManager.createBackup(mockConversations, '备份1');
      await backupManager.createBackup(mockConversations, '备份2');
      await backupManager.createBackup(mockConversations, '备份3');
      
      const backups = await backupManager.getBackupList();
      
      // 应该只保留最新的2个备份
      expect(backups).toHaveLength(2);
      expect(backups[0].description).toBe('备份3');
      expect(backups[1].description).toBe('备份2');
    });
  });

  describe('配置管理', () => {
    test('应该能够更新配置', () => {
      const newConfig = {
        autoBackup: true,
        backupInterval: 48,
        maxBackups: 20,
        compression: false,
        encryption: true
      };
      
      backupManager.updateConfig(newConfig);
      
      const config = backupManager.getConfig();
      expect(config.autoBackup).toBe(true);
      expect(config.backupInterval).toBe(48);
      expect(config.maxBackups).toBe(20);
      expect(config.compression).toBe(false);
      expect(config.encryption).toBe(true);
    });

    test('应该返回当前配置', () => {
      const config = backupManager.getConfig();
      
      expect(config.autoBackup).toBe(false);
      expect(config.backupInterval).toBe(24);
      expect(config.maxBackups).toBe(10);
      expect(config.compression).toBe(true);
      expect(config.encryption).toBe(false);
    });
  });

  describe('事件系统', () => {
    test('应该发出备份创建事件', async () => {
      let createdMetadata: any = null;
      backupManager.on('backupCreated', (metadata) => {
        createdMetadata = metadata;
      });
      
      const metadata = await backupManager.createBackup(mockConversations);
      
      expect(createdMetadata).not.toBeNull();
      expect(createdMetadata.id).toBe(metadata.id);
    });

    test('应该发出备份删除事件', async () => {
      const metadata = await backupManager.createBackup(mockConversations);
      
      let deletedId: string | null = null;
      backupManager.on('backupDeleted', (id) => {
        deletedId = id;
      });
      
      await backupManager.deleteBackup(metadata.id);
      
      expect(deletedId).toBe(metadata.id);
    });

    test('应该发出备份恢复事件', async () => {
      const metadata = await backupManager.createBackup(mockConversations);
      
      let restoredMetadata: any = null;
      backupManager.on('backupRestored', (meta) => {
        restoredMetadata = meta;
      });
      
      await backupManager.restoreBackup(metadata.id);
      
      expect(restoredMetadata).not.toBeNull();
      expect(restoredMetadata.id).toBe(metadata.id);
    });
  });

  describe('工具方法', () => {
    test('应该能够获取存储使用情况', async () => {
      await backupManager.createBackup(mockConversations);
      await backupManager.createBackup(mockConversations);
      
      const usage = await backupManager.getStorageUsage();
      
      expect(usage.totalBackups).toBe(2);
      expect(usage.totalSize).toBeGreaterThan(0);
      expect(usage.averageSize).toBeGreaterThan(0);
    });

    test('应该能够清理所有备份', async () => {
      await backupManager.createBackup(mockConversations);
      await backupManager.createBackup(mockConversations);
      
      let backups = await backupManager.getBackupList();
      expect(backups).toHaveLength(2);
      
      await backupManager.clearAllBackups();
      
      backups = await backupManager.getBackupList();
      expect(backups).toHaveLength(0);
    });

    test('应该能够销毁管理器', () => {
      const stopAutoBackupSpy = jest.spyOn(backupManager as any, 'stopAutoBackup');
      const removeAllListenersSpy = jest.spyOn(backupManager, 'removeAllListeners');
      
      backupManager.destroy();
      
      expect(stopAutoBackupSpy).toHaveBeenCalled();
      expect(removeAllListenersSpy).toHaveBeenCalled();
      
      stopAutoBackupSpy.mockRestore();
      removeAllListenersSpy.mockRestore();
    });
  });
});
