/**
 * 浏览器兼容性测试
 */

describe('浏览器兼容性测试', () => {
  describe('Chrome Extension APIs', () => {
    test('应该支持Chrome Storage API', () => {
      expect(chrome).toBeDefined();
      expect(chrome.storage).toBeDefined();
      expect(chrome.storage.local).toBeDefined();
      expect(chrome.storage.sync).toBeDefined();
      
      expect(typeof chrome.storage.local.get).toBe('function');
      expect(typeof chrome.storage.local.set).toBe('function');
      expect(typeof chrome.storage.sync.get).toBe('function');
      expect(typeof chrome.storage.sync.set).toBe('function');
    });

    test('应该支持Chrome Runtime API', () => {
      expect(chrome.runtime).toBeDefined();
      expect(typeof chrome.runtime.sendMessage).toBe('function');
      expect(chrome.runtime.onMessage).toBeDefined();
      expect(typeof chrome.runtime.onMessage.addListener).toBe('function');
    });

    test('应该支持Chrome Tabs API', () => {
      expect(chrome.tabs).toBeDefined();
      expect(typeof chrome.tabs.query).toBe('function');
      expect(typeof chrome.tabs.sendMessage).toBe('function');
    });

    test('应该支持Chrome Action API', () => {
      expect(chrome.action).toBeDefined();
      expect(typeof chrome.action.setBadgeText).toBe('function');
      expect(typeof chrome.action.setIcon).toBe('function');
    });
  });

  describe('Web APIs兼容性', () => {
    test('应该支持IndexedDB', () => {
      expect(indexedDB).toBeDefined();
      expect(typeof indexedDB.open).toBe('function');
    });

    test('应该支持Blob和URL APIs', () => {
      expect(Blob).toBeDefined();
      expect(URL).toBeDefined();
      expect(typeof URL.createObjectURL).toBe('function');
      expect(typeof URL.revokeObjectURL).toBe('function');
    });

    test('应该支持FileReader', () => {
      expect(FileReader).toBeDefined();
      const reader = new FileReader();
      expect(typeof reader.readAsText).toBe('function');
    });

    test('应该支持MutationObserver', () => {
      expect(MutationObserver).toBeDefined();
      const observer = new MutationObserver(() => {});
      expect(typeof observer.observe).toBe('function');
      expect(typeof observer.disconnect).toBe('function');
    });

    test('应该支持Performance API', () => {
      expect(performance).toBeDefined();
      expect(typeof performance.now).toBe('function');
    });

    test('应该支持TextEncoder/TextDecoder', () => {
      expect(TextEncoder).toBeDefined();
      expect(TextDecoder).toBeDefined();
      
      const encoder = new TextEncoder();
      const decoder = new TextDecoder();
      
      expect(typeof encoder.encode).toBe('function');
      expect(typeof decoder.decode).toBe('function');
    });
  });

  describe('ES6+特性兼容性', () => {
    test('应该支持Promise', () => {
      expect(Promise).toBeDefined();
      expect(typeof Promise.resolve).toBe('function');
      expect(typeof Promise.reject).toBe('function');
      expect(typeof Promise.all).toBe('function');
    });

    test('应该支持async/await', async () => {
      const asyncFunction = async () => {
        return 'test';
      };
      
      const result = await asyncFunction();
      expect(result).toBe('test');
    });

    test('应该支持Map和Set', () => {
      expect(Map).toBeDefined();
      expect(Set).toBeDefined();
      
      const map = new Map();
      const set = new Set();
      
      expect(typeof map.set).toBe('function');
      expect(typeof map.get).toBe('function');
      expect(typeof set.add).toBe('function');
      expect(typeof set.has).toBe('function');
    });

    test('应该支持Symbol', () => {
      expect(Symbol).toBeDefined();
      const sym = Symbol('test');
      expect(typeof sym).toBe('symbol');
    });

    test('应该支持WeakMap和WeakSet', () => {
      expect(WeakMap).toBeDefined();
      expect(WeakSet).toBeDefined();
    });

    test('应该支持Proxy', () => {
      expect(Proxy).toBeDefined();
      
      const target = { test: 'value' };
      const proxy = new Proxy(target, {
        get: (obj, prop) => obj[prop as keyof typeof obj]
      });
      
      expect(proxy.test).toBe('value');
    });

    test('应该支持解构赋值', () => {
      const obj = { a: 1, b: 2 };
      const arr = [1, 2, 3];
      
      const { a, b } = obj;
      const [first, second] = arr;
      
      expect(a).toBe(1);
      expect(b).toBe(2);
      expect(first).toBe(1);
      expect(second).toBe(2);
    });

    test('应该支持模板字符串', () => {
      const name = 'World';
      const greeting = `Hello, ${name}!`;
      expect(greeting).toBe('Hello, World!');
    });

    test('应该支持箭头函数', () => {
      const add = (a: number, b: number) => a + b;
      expect(add(2, 3)).toBe(5);
    });

    test('应该支持类语法', () => {
      class TestClass {
        constructor(public value: number) {}
        
        getValue() {
          return this.value;
        }
      }
      
      const instance = new TestClass(42);
      expect(instance.getValue()).toBe(42);
    });
  });

  describe('DOM API兼容性', () => {
    test('应该支持querySelector', () => {
      expect(typeof document.querySelector).toBe('function');
      expect(typeof document.querySelectorAll).toBe('function');
    });

    test('应该支持addEventListener', () => {
      const element = document.createElement('div');
      expect(typeof element.addEventListener).toBe('function');
      expect(typeof element.removeEventListener).toBe('function');
    });

    test('应该支持classList', () => {
      const element = document.createElement('div');
      expect(element.classList).toBeDefined();
      expect(typeof element.classList.add).toBe('function');
      expect(typeof element.classList.remove).toBe('function');
      expect(typeof element.classList.contains).toBe('function');
    });

    test('应该支持dataset', () => {
      const element = document.createElement('div');
      element.setAttribute('data-test', 'value');
      expect(element.dataset).toBeDefined();
      expect(element.dataset.test).toBe('value');
    });

    test('应该支持CustomEvent', () => {
      expect(CustomEvent).toBeDefined();
      const event = new CustomEvent('test', { detail: { data: 'test' } });
      expect(event.type).toBe('test');
      expect(event.detail.data).toBe('test');
    });
  });

  describe('CSS特性兼容性', () => {
    test('应该支持CSS Grid', () => {
      const element = document.createElement('div');
      element.style.display = 'grid';
      expect(element.style.display).toBe('grid');
    });

    test('应该支持CSS Flexbox', () => {
      const element = document.createElement('div');
      element.style.display = 'flex';
      expect(element.style.display).toBe('flex');
    });

    test('应该支持CSS Variables', () => {
      const element = document.createElement('div');
      element.style.setProperty('--test-var', 'red');
      expect(element.style.getPropertyValue('--test-var')).toBe('red');
    });

    test('应该支持CSS calc()', () => {
      const element = document.createElement('div');
      element.style.width = 'calc(100% - 20px)';
      expect(element.style.width).toBe('calc(100% - 20px)');
    });
  });

  describe('JSON兼容性', () => {
    test('应该支持JSON.parse和JSON.stringify', () => {
      expect(JSON).toBeDefined();
      expect(typeof JSON.parse).toBe('function');
      expect(typeof JSON.stringify).toBe('function');
      
      const obj = { test: 'value', number: 42 };
      const jsonString = JSON.stringify(obj);
      const parsed = JSON.parse(jsonString);
      
      expect(parsed).toEqual(obj);
    });

    test('应该正确处理特殊值', () => {
      expect(JSON.stringify(null)).toBe('null');
      expect(JSON.stringify(undefined)).toBe(undefined);
      expect(JSON.stringify(true)).toBe('true');
      expect(JSON.stringify(false)).toBe('false');
    });
  });

  describe('正则表达式兼容性', () => {
    test('应该支持基本正则表达式', () => {
      const regex = /test/i;
      expect(regex.test('TEST')).toBe(true);
      expect(regex.test('hello')).toBe(false);
    });

    test('应该支持Unicode正则表达式', () => {
      const regex = /\u{1F600}/u;
      expect(regex.test('😀')).toBe(true);
    });

    test('应该支持命名捕获组', () => {
      const regex = /(?<year>\d{4})-(?<month>\d{2})-(?<day>\d{2})/;
      const match = '2024-01-15'.match(regex);
      
      expect(match?.groups?.year).toBe('2024');
      expect(match?.groups?.month).toBe('01');
      expect(match?.groups?.day).toBe('15');
    });
  });

  describe('国际化兼容性', () => {
    test('应该支持Intl.DateTimeFormat', () => {
      expect(Intl).toBeDefined();
      expect(Intl.DateTimeFormat).toBeDefined();
      
      const formatter = new Intl.DateTimeFormat('zh-CN');
      const date = new Date('2024-01-15');
      const formatted = formatter.format(date);
      
      expect(typeof formatted).toBe('string');
    });

    test('应该支持Intl.NumberFormat', () => {
      expect(Intl.NumberFormat).toBeDefined();
      
      const formatter = new Intl.NumberFormat('zh-CN');
      const formatted = formatter.format(1234.56);
      
      expect(typeof formatted).toBe('string');
    });

    test('应该支持Intl.Collator', () => {
      expect(Intl.Collator).toBeDefined();
      
      const collator = new Intl.Collator('zh-CN');
      const result = collator.compare('a', 'b');
      
      expect(typeof result).toBe('number');
    });
  });

  describe('错误处理兼容性', () => {
    test('应该支持Error对象', () => {
      expect(Error).toBeDefined();
      
      const error = new Error('Test error');
      expect(error.message).toBe('Test error');
      expect(error.name).toBe('Error');
    });

    test('应该支持自定义错误类型', () => {
      class CustomError extends Error {
        constructor(message: string) {
          super(message);
          this.name = 'CustomError';
        }
      }
      
      const error = new CustomError('Custom error');
      expect(error.name).toBe('CustomError');
      expect(error.message).toBe('Custom error');
      expect(error instanceof Error).toBe(true);
      expect(error instanceof CustomError).toBe(true);
    });

    test('应该支持try/catch/finally', () => {
      let finallyExecuted = false;
      
      try {
        throw new Error('Test error');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
      } finally {
        finallyExecuted = true;
      }
      
      expect(finallyExecuted).toBe(true);
    });
  });

  describe('模块系统兼容性', () => {
    test('应该支持import/export语法', () => {
      // 这个测试主要验证TypeScript编译是否正确
      // 实际的import/export在运行时由模块加载器处理
      expect(true).toBe(true);
    });

    test('应该支持动态import', async () => {
      // 模拟动态import
      const mockDynamicImport = async () => {
        return { default: 'test module' };
      };
      
      const module = await mockDynamicImport();
      expect(module.default).toBe('test module');
    });
  });
});
