/**
 * Gemini 平台适配器
 * 专门处理 gemini.google.com 的页面结构和内容提取
 */

import { BasePlatformAdapter, PlatformSelectors } from './base';
import { ConversationData, MessageData } from '@types/conversation';

export class GeminiAdapter extends BasePlatformAdapter {
  readonly platform = 'Gemini';
  readonly selectors: PlatformSelectors = {
    // 基于Google Gemini常见结构模式 (2025-08-01)
    conversationContainer: '[role="main"], main, .conversation, [data-testid*="conversation"], .chat-container, .response-container',
    messageElements: '[data-testid*="message"], .message, .conversation-turn, [role="article"], article, .response-item, .prompt-item',
    userMessage: '[data-author="user"], .user-message, .prompt, .human-message, [data-role="user"]',
    assistantMessage: '[data-author="assistant"], .assistant-message, .response, .ai-message, [data-role="assistant"], .model-response',
    messageContent: '.message-content, .prose, .markdown, .text-content, .whitespace-pre-wrap, p, div[class*="content"], .response-text',
    messageInput: 'textarea[placeholder*="Enter"], textarea[placeholder*="Ask"], textarea[data-testid*="input"], .chat-input textarea, textarea, input[type="text"]',
    sendButton: '[data-testid*="send"], .send-button, button[type="submit"], button[aria-label*="Send"], button[title*="Send"]',
    conversationTitle: '.conversation-title, h1, .chat-title, .title',
    conversationList: '.conversation-list li, .chat-list-item, nav li, .sidebar li'
  };

  protected features = {
    hasConversationList: true,
    hasMessageTimestamps: false,
    hasCodeBlocks: true,
    hasImageSupport: true,
    hasFileUpload: true,
    hasConversationExport: false
  };

  isPageReady(): boolean {
    // 检查是否在Gemini域名
    if (!this.isCurrentPlatform()) {
      return false;
    }

    // 检查是否在会话页面
    const hasConversationContainer = document.querySelector(this.selectors.conversationContainer) !== null;
    const hasMessageInput = document.querySelector(this.selectors.messageInput) !== null;
    const isConversationUrl = window.location.pathname.includes('/app') || 
                             window.location.pathname === '/' ||
                             window.location.pathname.startsWith('/chat');
    
    // 等待页面完全加载
    const isPageLoaded = document.readyState === 'complete' || 
                        document.readyState === 'interactive';
    
    this.logger.debug('Gemini页面检查状态:', {
      hasConversationContainer,
      hasMessageInput,
      isConversationUrl,
      isPageLoaded,
      currentUrl: window.location.href
    });
    
    return hasConversationContainer && hasMessageInput && isConversationUrl && isPageLoaded;
  }

  extractConversation(): ConversationData | null {
    try {
      this.logger.debug('开始提取Gemini对话内容');

      // 查找对话容器
      const conversationContainer = document.querySelector(this.selectors.conversationContainer);
      if (!conversationContainer) {
        this.logger.warn('未找到对话容器');
        return null;
      }

      // 提取消息
      const messages = this.extractMessages(conversationContainer);
      if (messages.length === 0) {
        this.logger.warn('未找到任何消息');
        return null;
      }

      // 提取会话标题
      const title = this.extractConversationTitle();

      // 生成会话ID
      const conversationId = this.generateConversationId();

      const conversation: ConversationData = {
        id: conversationId,
        platform: this.platform,
        title: title || '未命名对话',
        messages,
        url: window.location.href,
        timestamp: new Date().toISOString(),
        metadata: {
          userAgent: navigator.userAgent,
          pageTitle: document.title,
          extractedAt: new Date().toISOString(),
          messageCount: messages.length
        }
      };

      this.logger.info('Gemini对话提取成功:', {
        conversationId,
        messageCount: messages.length,
        title
      });

      return conversation;

    } catch (error) {
      this.logger.error('Gemini对话提取失败:', error);
      return null;
    }
  }

  protected extractMessages(container: Element): MessageData[] {
    const messages: MessageData[] = [];
    
    try {
      // 查找所有消息元素
      const messageElements = container.querySelectorAll(this.selectors.messageElements);
      
      this.logger.debug(`找到 ${messageElements.length} 个消息元素`);

      messageElements.forEach((element, index) => {
        try {
          const message = this.extractSingleMessage(element, index);
          if (message) {
            messages.push(message);
          }
        } catch (error) {
          this.logger.warn(`提取第 ${index} 个消息失败:`, error);
        }
      });

      // 如果没有找到消息，尝试其他策略
      if (messages.length === 0) {
        this.logger.debug('尝试备用消息提取策略');
        const fallbackMessages = this.extractMessagesFallback(container);
        messages.push(...fallbackMessages);
      }

    } catch (error) {
      this.logger.error('消息提取过程出错:', error);
    }

    return messages;
  }

  protected extractSingleMessage(element: Element, index: number): MessageData | null {
    try {
      // 确定消息角色
      const role = this.determineMessageRole(element);
      if (!role) {
        this.logger.debug(`无法确定第 ${index} 个消息的角色`);
        return null;
      }

      // 提取消息内容
      const content = this.extractMessageContent(element);
      if (!content.trim()) {
        this.logger.debug(`第 ${index} 个消息内容为空`);
        return null;
      }

      // 生成消息ID
      const messageId = this.generateMessageId(index, role);

      const message: MessageData = {
        id: messageId,
        role,
        content,
        timestamp: new Date().toISOString(),
        metadata: {
          elementIndex: index,
          extractedFrom: element.tagName.toLowerCase(),
          className: element.className,
          textLength: content.length
        }
      };

      this.logger.debug(`提取消息成功: ${role} - ${content.slice(0, 50)}...`);
      return message;

    } catch (error) {
      this.logger.error(`提取单个消息失败 (索引 ${index}):`, error);
      return null;
    }
  }

  protected determineMessageRole(element: Element): 'user' | 'assistant' | null {
    const className = element.className.toLowerCase();
    const textContent = element.textContent?.toLowerCase() || '';
    
    // 检查用户消息特征
    if (element.matches(this.selectors.userMessage)) {
      return 'user';
    }
    
    // 检查助手消息特征
    if (element.matches(this.selectors.assistantMessage)) {
      return 'assistant';
    }

    // 基于类名判断
    if (className.includes('user') || className.includes('human') || className.includes('prompt')) {
      return 'user';
    }
    
    if (className.includes('assistant') || className.includes('ai') || className.includes('model') || className.includes('response')) {
      return 'assistant';
    }

    // 基于数据属性判断
    const dataRole = element.getAttribute('data-role') || element.getAttribute('data-author');
    if (dataRole) {
      if (dataRole.includes('user') || dataRole.includes('human')) {
        return 'user';
      }
      if (dataRole.includes('assistant') || dataRole.includes('ai') || dataRole.includes('model')) {
        return 'assistant';
      }
    }

    // 基于位置和内容特征判断（Gemini特有）
    const previousSibling = element.previousElementSibling;
    const nextSibling = element.nextElementSibling;
    
    // 如果前一个是用户消息，这个可能是助手消息
    if (previousSibling && this.determineMessageRole(previousSibling) === 'user') {
      return 'assistant';
    }

    this.logger.debug('无法确定消息角色:', {
      className,
      tagName: element.tagName,
      textPreview: textContent.slice(0, 50)
    });

    return null;
  }

  protected extractMessagesFallback(container: Element): MessageData[] {
    const messages: MessageData[] = [];
    
    try {
      // 尝试查找所有包含文本的div和p元素
      const textElements = container.querySelectorAll('div, p, article, section');
      
      textElements.forEach((element, index) => {
        const text = element.textContent?.trim();
        if (text && text.length > 10) { // 过滤掉太短的文本
          // 简单的角色判断逻辑
          const role = index % 2 === 0 ? 'user' : 'assistant';
          
          const message: MessageData = {
            id: this.generateMessageId(index, role),
            role,
            content: text,
            timestamp: new Date().toISOString(),
            metadata: {
              elementIndex: index,
              extractedFrom: 'fallback',
              className: element.className,
              textLength: text.length
            }
          };
          
          messages.push(message);
        }
      });
      
    } catch (error) {
      this.logger.error('备用消息提取失败:', error);
    }
    
    return messages;
  }

  protected extractConversationTitle(): string {
    try {
      const titleElement = document.querySelector(this.selectors.conversationTitle);
      if (titleElement) {
        const title = titleElement.textContent?.trim();
        if (title && title !== 'Gemini') {
          return title;
        }
      }

      // 尝试从页面标题提取
      const pageTitle = document.title;
      if (pageTitle && pageTitle !== 'Gemini') {
        return pageTitle.replace(' - Gemini', '').trim();
      }

      // 尝试从第一条用户消息生成标题
      const firstUserMessage = document.querySelector(this.selectors.userMessage);
      if (firstUserMessage) {
        const content = firstUserMessage.textContent?.trim();
        if (content) {
          return content.slice(0, 50) + (content.length > 50 ? '...' : '');
        }
      }

      return '新对话';

    } catch (error) {
      this.logger.error('提取对话标题失败:', error);
      return '未命名对话';
    }
  }
}
