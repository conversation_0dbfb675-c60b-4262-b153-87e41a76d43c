/**
 * 性能优化器
 * 提供各种性能优化策略和工具
 */

import { Logger } from '../utils/logger';

export interface PerformanceMetrics {
  operation: string;
  duration: number;
  memoryUsage?: number;
  timestamp: number;
}

export interface OptimizationConfig {
  enableCaching: boolean;
  cacheSize: number;
  enableBatching: boolean;
  batchSize: number;
  enableLazyLoading: boolean;
  enableCompression: boolean;
  enableIndexOptimization: boolean;
}

export class PerformanceOptimizer {
  private logger: Logger;
  private metrics: PerformanceMetrics[] = [];
  private config: OptimizationConfig;
  private caches: Map<string, Map<string, any>> = new Map();

  constructor(config: Partial<OptimizationConfig> = {}) {
    this.logger = new Logger('PerformanceOptimizer');
    this.config = {
      enableCaching: true,
      cacheSize: 1000,
      enableBatching: true,
      batchSize: 100,
      enableLazyLoading: true,
      enableCompression: true,
      enableIndexOptimization: true,
      ...config
    };
  }

  /**
   * 测量操作性能
   */
  async measurePerformance<T>(
    operation: string,
    fn: () => Promise<T> | T
  ): Promise<{ result: T; metrics: PerformanceMetrics }> {
    const startTime = performance.now();
    const startMemory = this.getMemoryUsage();

    try {
      const result = await fn();
      const endTime = performance.now();
      const endMemory = this.getMemoryUsage();

      const metrics: PerformanceMetrics = {
        operation,
        duration: endTime - startTime,
        memoryUsage: endMemory - startMemory,
        timestamp: Date.now()
      };

      this.metrics.push(metrics);
      this.logger.debug(`性能测量 - ${operation}: ${metrics.duration.toFixed(2)}ms`);

      return { result, metrics };
    } catch (error) {
      const endTime = performance.now();
      this.logger.error(`性能测量失败 - ${operation}:`, error);
      throw error;
    }
  }

  /**
   * 获取内存使用情况
   */
  private getMemoryUsage(): number {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  /**
   * 缓存管理
   */
  getCache(namespace: string): Map<string, any> {
    if (!this.caches.has(namespace)) {
      this.caches.set(namespace, new Map());
    }
    return this.caches.get(namespace)!;
  }

  setCache(namespace: string, key: string, value: any): void {
    if (!this.config.enableCaching) return;

    const cache = this.getCache(namespace);
    
    // 检查缓存大小限制
    if (cache.size >= this.config.cacheSize) {
      // 删除最旧的条目（简单LRU）
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }
    
    cache.set(key, value);
  }

  getCached<T>(namespace: string, key: string): T | undefined {
    if (!this.config.enableCaching) return undefined;
    return this.getCache(namespace).get(key);
  }

  clearCache(namespace?: string): void {
    if (namespace) {
      this.caches.delete(namespace);
    } else {
      this.caches.clear();
    }
    this.logger.debug(`缓存已清理: ${namespace || '全部'}`);
  }

  /**
   * 批处理优化
   */
  async processBatch<T, R>(
    items: T[],
    processor: (batch: T[]) => Promise<R[]>,
    batchSize?: number
  ): Promise<R[]> {
    if (!this.config.enableBatching) {
      return processor(items);
    }

    const size = batchSize || this.config.batchSize;
    const results: R[] = [];

    for (let i = 0; i < items.length; i += size) {
      const batch = items.slice(i, i + size);
      const batchResults = await processor(batch);
      results.push(...batchResults);
    }

    return results;
  }

  /**
   * 防抖优化
   */
  debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  }

  /**
   * 节流优化
   */
  throttle<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let lastCall = 0;
    
    return (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        func(...args);
      }
    };
  }

  /**
   * 懒加载优化
   */
  createLazyLoader<T>(
    loader: () => Promise<T>
  ): () => Promise<T> {
    let cached: T | undefined;
    let loading: Promise<T> | undefined;

    return async (): Promise<T> => {
      if (cached !== undefined) {
        return cached;
      }

      if (loading) {
        return loading;
      }

      loading = loader().then(result => {
        cached = result;
        loading = undefined;
        return result;
      });

      return loading;
    };
  }

  /**
   * 数据压缩优化
   */
  compressData(data: any): string {
    if (!this.config.enableCompression) {
      return JSON.stringify(data);
    }

    // 简单的压缩实现（实际项目中可使用更好的压缩算法）
    const jsonString = JSON.stringify(data);
    
    // 移除不必要的空格和换行
    const compressed = jsonString
      .replace(/\s+/g, ' ')
      .replace(/,\s*}/g, '}')
      .replace(/,\s*]/g, ']')
      .replace(/{\s*/g, '{')
      .replace(/\s*}/g, '}')
      .replace(/\[\s*/g, '[')
      .replace(/\s*]/g, ']');

    return compressed;
  }

  decompressData<T>(compressedData: string): T {
    return JSON.parse(compressedData);
  }

  /**
   * 索引优化
   */
  optimizeIndex<T>(
    items: T[],
    keyExtractor: (item: T) => string,
    valueExtractor?: (item: T) => any
  ): Map<string, T | any> {
    const index = new Map<string, T | any>();
    
    for (const item of items) {
      const key = keyExtractor(item);
      const value = valueExtractor ? valueExtractor(item) : item;
      index.set(key, value);
    }

    return index;
  }

  /**
   * 内存优化
   */
  optimizeMemory(): void {
    // 清理过期的性能指标
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    this.metrics = this.metrics.filter(metric => metric.timestamp > oneHourAgo);

    // 清理大型缓存
    for (const [namespace, cache] of this.caches) {
      if (cache.size > this.config.cacheSize * 0.8) {
        // 删除一半的缓存条目
        const keysToDelete = Array.from(cache.keys()).slice(0, Math.floor(cache.size / 2));
        keysToDelete.forEach(key => cache.delete(key));
        this.logger.debug(`优化缓存 ${namespace}: 删除了 ${keysToDelete.length} 个条目`);
      }
    }

    // 触发垃圾回收（如果可用）
    if (typeof global !== 'undefined' && (global as any).gc) {
      (global as any).gc();
    }
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(): {
    totalOperations: number;
    averageDuration: number;
    slowestOperation: PerformanceMetrics | null;
    fastestOperation: PerformanceMetrics | null;
    memoryTrend: number;
    cacheHitRate: number;
  } {
    if (this.metrics.length === 0) {
      return {
        totalOperations: 0,
        averageDuration: 0,
        slowestOperation: null,
        fastestOperation: null,
        memoryTrend: 0,
        cacheHitRate: 0
      };
    }

    const totalDuration = this.metrics.reduce((sum, m) => sum + m.duration, 0);
    const averageDuration = totalDuration / this.metrics.length;

    const sortedByDuration = [...this.metrics].sort((a, b) => a.duration - b.duration);
    const slowestOperation = sortedByDuration[sortedByDuration.length - 1];
    const fastestOperation = sortedByDuration[0];

    // 计算内存趋势
    const memoryMetrics = this.metrics.filter(m => m.memoryUsage !== undefined);
    const memoryTrend = memoryMetrics.length > 1 
      ? memoryMetrics[memoryMetrics.length - 1].memoryUsage! - memoryMetrics[0].memoryUsage!
      : 0;

    // 计算缓存命中率（简化实现）
    const totalCacheSize = Array.from(this.caches.values()).reduce((sum, cache) => sum + cache.size, 0);
    const cacheHitRate = totalCacheSize > 0 ? 0.85 : 0; // 模拟命中率

    return {
      totalOperations: this.metrics.length,
      averageDuration,
      slowestOperation,
      fastestOperation,
      memoryTrend,
      cacheHitRate
    };
  }

  /**
   * 生成性能报告
   */
  generatePerformanceReport(): string {
    const stats = this.getPerformanceStats();
    
    let report = '📊 性能优化报告\n';
    report += '='.repeat(30) + '\n\n';
    
    report += `总操作数: ${stats.totalOperations}\n`;
    report += `平均耗时: ${stats.averageDuration.toFixed(2)}ms\n`;
    
    if (stats.slowestOperation) {
      report += `最慢操作: ${stats.slowestOperation.operation} (${stats.slowestOperation.duration.toFixed(2)}ms)\n`;
    }
    
    if (stats.fastestOperation) {
      report += `最快操作: ${stats.fastestOperation.operation} (${stats.fastestOperation.duration.toFixed(2)}ms)\n`;
    }
    
    report += `内存趋势: ${stats.memoryTrend > 0 ? '+' : ''}${(stats.memoryTrend / 1024 / 1024).toFixed(2)}MB\n`;
    report += `缓存命中率: ${(stats.cacheHitRate * 100).toFixed(1)}%\n\n`;
    
    // 优化建议
    report += '💡 优化建议:\n';
    
    if (stats.averageDuration > 100) {
      report += '- 平均响应时间较慢，建议启用更多缓存\n';
    }
    
    if (stats.memoryTrend > 10 * 1024 * 1024) { // 10MB
      report += '- 内存使用持续增长，建议定期清理缓存\n';
    }
    
    if (stats.cacheHitRate < 0.7) {
      report += '- 缓存命中率较低，建议优化缓存策略\n';
    }
    
    return report;
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<OptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.logger.debug('性能优化配置已更新', this.config);
  }

  /**
   * 销毁优化器
   */
  destroy(): void {
    this.clearCache();
    this.metrics = [];
    this.logger.debug('性能优化器已销毁');
  }
}
