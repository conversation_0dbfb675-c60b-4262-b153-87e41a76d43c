/**
 * 搜索引擎核心模块
 * 实现全文搜索、索引构建和结果排序功能
 */

import { ConversationData, MessageData } from '@types/conversation';
import { Logger } from '@utils/logger';

export interface SearchResult {
  conversation: ConversationData;
  message?: MessageData;
  score: number;
  highlights: string[];
  matchType: 'title' | 'content' | 'tag' | 'metadata';
}

export interface SearchOptions {
  query: string;
  platform?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  tags?: string[];
  limit?: number;
  offset?: number;
  sortBy?: 'relevance' | 'date' | 'title';
  sortOrder?: 'asc' | 'desc';
}

export interface SearchIndex {
  id: string;
  type: 'conversation' | 'message';
  content: string;
  normalizedContent: string;
  tokens: string[];
  metadata: {
    conversationId: string;
    messageId?: string;
    platform: string;
    timestamp: string;
    title: string;
    tags: string[];
  };
}

export class SearchEngine {
  private logger: Logger;
  private indices: Map<string, SearchIndex> = new Map();
  private stopWords: Set<string>;
  private tokenCache: Map<string, string[]> = new Map();

  constructor() {
    this.logger = new Logger('SearchEngine');
    this.stopWords = this.initializeStopWords();
  }

  /**
   * 初始化停用词列表
   */
  private initializeStopWords(): Set<string> {
    const chineseStopWords = [
      '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
      '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
      '自己', '这', '那', '它', '他', '她', '们', '这个', '那个', '什么', '怎么',
      '为什么', '哪里', '什么时候', '如何', '可以', '能够', '应该', '需要'
    ];

    const englishStopWords = [
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of',
      'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have',
      'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
      'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'i',
      'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
    ];

    return new Set([...chineseStopWords, ...englishStopWords]);
  }

  /**
   * 构建搜索索引
   */
  async buildIndex(conversations: ConversationData[]): Promise<void> {
    this.logger.info('开始构建搜索索引', { conversationCount: conversations.length });

    try {
      this.indices.clear();
      this.tokenCache.clear();

      for (const conversation of conversations) {
        // 为对话创建索引
        await this.indexConversation(conversation);

        // 为每条消息创建索引
        for (const message of conversation.messages) {
          await this.indexMessage(conversation, message);
        }
      }

      this.logger.info('搜索索引构建完成', { 
        indexCount: this.indices.size,
        cacheSize: this.tokenCache.size
      });

    } catch (error) {
      this.logger.error('搜索索引构建失败:', error);
      throw error;
    }
  }

  /**
   * 为对话创建索引
   */
  private async indexConversation(conversation: ConversationData): Promise<void> {
    const content = [
      conversation.title,
      conversation.metadata?.tags?.join(' ') || '',
      conversation.platform
    ].join(' ');

    const normalizedContent = this.normalizeText(content);
    const tokens = this.tokenize(normalizedContent);

    const index: SearchIndex = {
      id: `conv_${conversation.id}`,
      type: 'conversation',
      content,
      normalizedContent,
      tokens,
      metadata: {
        conversationId: conversation.id,
        platform: conversation.platform,
        timestamp: conversation.timestamp,
        title: conversation.title,
        tags: conversation.metadata?.tags || []
      }
    };

    this.indices.set(index.id, index);
  }

  /**
   * 为消息创建索引
   */
  private async indexMessage(conversation: ConversationData, message: MessageData): Promise<void> {
    const content = message.content;
    const normalizedContent = this.normalizeText(content);
    const tokens = this.tokenize(normalizedContent);

    const index: SearchIndex = {
      id: `msg_${message.id}`,
      type: 'message',
      content,
      normalizedContent,
      tokens,
      metadata: {
        conversationId: conversation.id,
        messageId: message.id,
        platform: conversation.platform,
        timestamp: message.timestamp,
        title: conversation.title,
        tags: conversation.metadata?.tags || []
      }
    };

    this.indices.set(index.id, index);
  }

  /**
   * 文本标准化
   */
  private normalizeText(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, ' ') // 保留中文、英文、数字
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 文本分词
   */
  private tokenize(text: string): string[] {
    const cacheKey = text;
    if (this.tokenCache.has(cacheKey)) {
      return this.tokenCache.get(cacheKey)!;
    }

    // 简单的分词策略
    const tokens: string[] = [];

    // 英文分词
    const englishWords = text.match(/[a-zA-Z]+/g) || [];
    tokens.push(...englishWords.filter(word => 
      word.length > 2 && !this.stopWords.has(word.toLowerCase())
    ));

    // 中文分词（简单的字符级分词）
    const chineseChars = text.match(/[\u4e00-\u9fff]/g) || [];
    tokens.push(...chineseChars);

    // 中文词组分词（2-3字词组）
    for (let i = 0; i < chineseChars.length - 1; i++) {
      const bigram = chineseChars.slice(i, i + 2).join('');
      if (!this.stopWords.has(bigram)) {
        tokens.push(bigram);
      }

      if (i < chineseChars.length - 2) {
        const trigram = chineseChars.slice(i, i + 3).join('');
        if (!this.stopWords.has(trigram)) {
          tokens.push(trigram);
        }
      }
    }

    // 数字
    const numbers = text.match(/\d+/g) || [];
    tokens.push(...numbers);

    const uniqueTokens = [...new Set(tokens)];
    this.tokenCache.set(cacheKey, uniqueTokens);

    return uniqueTokens;
  }

  /**
   * 执行搜索
   */
  async search(options: SearchOptions): Promise<SearchResult[]> {
    this.logger.debug('执行搜索', options);

    try {
      const queryTokens = this.tokenize(this.normalizeText(options.query));
      if (queryTokens.length === 0) {
        return [];
      }

      const results: SearchResult[] = [];

      for (const index of this.indices.values()) {
        // 应用过滤条件
        if (!this.matchesFilters(index, options)) {
          continue;
        }

        // 计算相关性得分
        const score = this.calculateRelevanceScore(index, queryTokens, options.query);
        if (score > 0) {
          // 生成高亮片段
          const highlights = this.generateHighlights(index.content, queryTokens);

          // 确定匹配类型
          const matchType = this.determineMatchType(index, queryTokens);

          results.push({
            conversation: this.getConversationFromIndex(index),
            message: index.type === 'message' ? this.getMessageFromIndex(index) : undefined,
            score,
            highlights,
            matchType
          });
        }
      }

      // 排序结果
      const sortedResults = this.sortResults(results, options);

      // 应用分页
      const paginatedResults = this.applyPagination(sortedResults, options);

      this.logger.debug('搜索完成', {
        query: options.query,
        totalResults: results.length,
        returnedResults: paginatedResults.length
      });

      return paginatedResults;

    } catch (error) {
      this.logger.error('搜索执行失败:', error);
      throw error;
    }
  }

  /**
   * 检查索引是否匹配过滤条件
   */
  private matchesFilters(index: SearchIndex, options: SearchOptions): boolean {
    // 平台过滤
    if (options.platform && index.metadata.platform !== options.platform) {
      return false;
    }

    // 日期范围过滤
    if (options.dateRange) {
      const timestamp = new Date(index.metadata.timestamp);
      if (timestamp < options.dateRange.start || timestamp > options.dateRange.end) {
        return false;
      }
    }

    // 标签过滤
    if (options.tags && options.tags.length > 0) {
      const hasMatchingTag = options.tags.some(tag => 
        index.metadata.tags.includes(tag)
      );
      if (!hasMatchingTag) {
        return false;
      }
    }

    return true;
  }

  /**
   * 计算相关性得分
   */
  private calculateRelevanceScore(index: SearchIndex, queryTokens: string[], originalQuery: string): number {
    let score = 0;

    // 精确匹配得分
    if (index.normalizedContent.includes(this.normalizeText(originalQuery))) {
      score += 100;
    }

    // 词汇匹配得分
    const matchedTokens = queryTokens.filter(token => 
      index.tokens.includes(token)
    );

    if (matchedTokens.length === 0) {
      return 0;
    }

    // 基础匹配得分
    const matchRatio = matchedTokens.length / queryTokens.length;
    score += matchRatio * 50;

    // 标题匹配加权
    if (index.type === 'conversation' || 
        index.metadata.title.toLowerCase().includes(this.normalizeText(originalQuery))) {
      score += 30;
    }

    // 标签匹配加权
    const tagMatches = queryTokens.filter(token =>
      index.metadata.tags.some(tag => tag.toLowerCase().includes(token))
    );
    score += tagMatches.length * 20;

    // 时间衰减（越新的内容得分越高）
    const daysSinceCreation = (Date.now() - new Date(index.metadata.timestamp).getTime()) / (1000 * 60 * 60 * 24);
    const timeDecay = Math.max(0, 1 - daysSinceCreation / 365); // 一年内的内容有时间加权
    score += timeDecay * 10;

    return Math.round(score);
  }

  /**
   * 生成高亮片段
   */
  private generateHighlights(content: string, queryTokens: string[]): string[] {
    const highlights: string[] = [];
    const maxHighlights = 3;
    const contextLength = 100;

    for (const token of queryTokens) {
      const regex = new RegExp(`(${token})`, 'gi');
      const matches = [...content.matchAll(regex)];

      for (const match of matches.slice(0, maxHighlights)) {
        const start = Math.max(0, match.index! - contextLength / 2);
        const end = Math.min(content.length, match.index! + token.length + contextLength / 2);

        let highlight = content.slice(start, end);
        if (start > 0) highlight = '...' + highlight;
        if (end < content.length) highlight = highlight + '...';

        // 添加高亮标记
        highlight = highlight.replace(regex, '<mark>$1</mark>');
        highlights.push(highlight);

        if (highlights.length >= maxHighlights) break;
      }

      if (highlights.length >= maxHighlights) break;
    }

    return highlights;
  }

  /**
   * 确定匹配类型
   */
  private determineMatchType(index: SearchIndex, queryTokens: string[]): 'title' | 'content' | 'tag' | 'metadata' {
    const titleTokens = this.tokenize(this.normalizeText(index.metadata.title));
    const hasTitle = queryTokens.some(token => titleTokens.includes(token));

    if (hasTitle) return 'title';

    const hasTags = queryTokens.some(token =>
      index.metadata.tags.some(tag => this.normalizeText(tag).includes(token))
    );

    if (hasTags) return 'tag';

    if (index.metadata.platform.toLowerCase().includes(queryTokens.join(' ').toLowerCase())) {
      return 'metadata';
    }

    return 'content';
  }

  /**
   * 排序搜索结果
   */
  private sortResults(results: SearchResult[], options: SearchOptions): SearchResult[] {
    const sortBy = options.sortBy || 'relevance';
    const sortOrder = options.sortOrder || 'desc';

    return results.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'relevance':
          comparison = a.score - b.score;
          break;
        case 'date':
          comparison = new Date(a.conversation.timestamp).getTime() -
                      new Date(b.conversation.timestamp).getTime();
          break;
        case 'title':
          comparison = a.conversation.title.localeCompare(b.conversation.title);
          break;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });
  }

  /**
   * 应用分页
   */
  private applyPagination(results: SearchResult[], options: SearchOptions): SearchResult[] {
    const limit = options.limit || 20;
    const offset = options.offset || 0;

    return results.slice(offset, offset + limit);
  }

  /**
   * 获取搜索统计信息
   */
  getSearchStats(): {
    totalIndices: number;
    conversationIndices: number;
    messageIndices: number;
    cacheSize: number;
  } {
    const conversationIndices = Array.from(this.indices.values())
      .filter(index => index.type === 'conversation').length;

    const messageIndices = Array.from(this.indices.values())
      .filter(index => index.type === 'message').length;

    return {
      totalIndices: this.indices.size,
      conversationIndices,
      messageIndices,
      cacheSize: this.tokenCache.size
    };
  }

  /**
   * 清理搜索缓存
   */
  clearCache(): void {
    this.tokenCache.clear();
    this.logger.info('搜索缓存已清理');
  }

  /**
   * 增量更新索引
   */
  async updateIndex(conversation: ConversationData): Promise<void> {
    try {
      // 删除旧索引
      const oldIndices = Array.from(this.indices.keys())
        .filter(key => key.includes(conversation.id));

      oldIndices.forEach(key => this.indices.delete(key));

      // 重新创建索引
      await this.indexConversation(conversation);
      for (const message of conversation.messages) {
        await this.indexMessage(conversation, message);
      }

      this.logger.debug('索引更新完成', { conversationId: conversation.id });
    } catch (error) {
      this.logger.error('索引更新失败:', error);
      throw error;
    }
  }

  /**
   * 删除索引
   */
  removeIndex(conversationId: string): void {
    const indicesToRemove = Array.from(this.indices.keys())
      .filter(key => key.includes(conversationId));

    indicesToRemove.forEach(key => this.indices.delete(key));

    this.logger.debug('索引删除完成', {
      conversationId,
      removedCount: indicesToRemove.length
    });
  }
}
