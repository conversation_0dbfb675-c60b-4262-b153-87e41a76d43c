/**
 * 会话管理模块入口
 * 导出会话管理相关的类和接口
 */

import { ConversationManager } from './conversation-manager';

// 创建全局会话管理器实例
export const conversationManager = new ConversationManager();

// 导出类型和接口
export type {
  ConversationMergeOptions,
  ConversationDuplicateOptions,
  ConversationArchiveOptions
} from './conversation-manager';

// 导出主要类
export { ConversationManager } from './conversation-manager';

// 默认导出会话管理器实例
export default conversationManager;
