# AI会话管理插件 - 多平台适配方案

## 🎯 适配策略概览

### 为什么不使用 Playwright MCP？

虽然 Playwright MCP 是一个优秀的页面自动化工具，但在浏览器插件环境中存在以下限制：

1. **运行环境限制**: 浏览器插件运行在沙箱环境中，无法直接使用 Node.js 模块
2. **权限限制**: 插件无法启动外部进程或访问文件系统
3. **性能考虑**: 实时页面监听需要轻量级解决方案
4. **用户体验**: 不能依赖外部工具，需要纯前端解决方案

### 推荐方案：基于 DOM 观察器的适配器模式

采用**适配器模式 + DOM 观察器 + 平台特征识别**的组合方案：

```mermaid
graph TD
    A[页面加载] --> B[平台检测器]
    B --> C{识别平台}
    C -->|ChatGPT| D[ChatGPT适配器]
    C -->|Claude| E[Claude适配器]
    C -->|Gemini| F[Gemini适配器]
    C -->|其他| G[通用适配器]
    
    D --> H[DOM观察器]
    E --> H
    F --> H
    G --> H
    
    H --> I[内容提取]
    I --> J[数据标准化]
    J --> K[保存到存储]
```

## 🔍 平台检测与识别

### 1. 平台检测器

```typescript
// src/content/platform-detector.ts
export class PlatformDetector {
  private static readonly PLATFORM_PATTERNS: Record<string, PlatformConfig> = {
    'chat.openai.com': {
      name: 'ChatGPT',
      adapter: 'ChatGPTAdapter',
      features: ['conversation_threads', 'message_streaming', 'code_blocks']
    },
    
    'claude.ai': {
      name: 'Claude',
      adapter: 'ClaudeAdapter', 
      features: ['conversation_threads', 'message_streaming', 'file_upload']
    },
    
    'gemini.google.com': {
      name: 'Gemini',
      adapter: 'GeminiAdapter',
      features: ['conversation_threads', 'multimodal', 'suggestions']
    },
    
    'aistudio.google.com': {
      name: 'Aistudio',
      adapter: 'AistudioAdapter',
      features: ['conversation_threads', 'code_generation']
    },
    
    'monica.im': {
      name: 'Monica',
      adapter: 'MonicaAdapter',
      features: ['conversation_threads', 'quick_actions']
    },
    
    'poe.com': {
      name: 'Poe',
      adapter: 'PoeAdapter',
      features: ['multiple_models', 'conversation_threads']
    }
  };
  
  static detectPlatform(): PlatformConfig | null {
    const hostname = window.location.hostname;
    
    for (const [pattern, config] of Object.entries(this.PLATFORM_PATTERNS)) {
      if (hostname.includes(pattern)) {
        return config;
      }
    }
    
    return null;
  }
  
  static isSupported(): boolean {
    return this.detectPlatform() !== null;
  }
}

export interface PlatformConfig {
  name: string;
  adapter: string;
  features: string[];
}
```

### 2. 动态适配器加载

```typescript
// src/content/adapter-factory.ts
export class AdapterFactory {
  private static adapters: Map<string, BasePlatformAdapter> = new Map();
  
  static async createAdapter(platform: PlatformConfig): Promise<BasePlatformAdapter> {
    if (this.adapters.has(platform.name)) {
      return this.adapters.get(platform.name)!;
    }
    
    let adapter: BasePlatformAdapter;
    
    switch (platform.adapter) {
      case 'ChatGPTAdapter':
        const { ChatGPTAdapter } = await import('./platforms/chatgpt');
        adapter = new ChatGPTAdapter();
        break;
        
      case 'ClaudeAdapter':
        const { ClaudeAdapter } = await import('./platforms/claude');
        adapter = new ClaudeAdapter();
        break;
        
      case 'GeminiAdapter':
        const { GeminiAdapter } = await import('./platforms/gemini');
        adapter = new GeminiAdapter();
        break;
        
      default:
        const { GenericAdapter } = await import('./platforms/generic');
        adapter = new GenericAdapter();
    }
    
    this.adapters.set(platform.name, adapter);
    return adapter;
  }
}
```

## 🏗️ 基础适配器架构

### 1. 基础适配器类

```typescript
// src/content/platforms/base.ts
export abstract class BasePlatformAdapter {
  protected observer: MutationObserver | null = null;
  protected lastProcessedMessage: string = '';
  
  abstract readonly platform: string;
  abstract readonly selectors: PlatformSelectors;
  
  // 初始化适配器
  async initialize(): Promise<void> {
    await this.waitForPageReady();
    this.setupObserver();
    this.extractExistingConversation();
  }
  
  // 等待页面准备就绪
  protected async waitForPageReady(): Promise<void> {
    return new Promise((resolve) => {
      if (this.isPageReady()) {
        resolve();
        return;
      }
      
      const checkReady = () => {
        if (this.isPageReady()) {
          resolve();
        } else {
          setTimeout(checkReady, 100);
        }
      };
      
      checkReady();
    });
  }
  
  // 检查页面是否准备就绪
  protected abstract isPageReady(): boolean;
  
  // 设置DOM观察器
  protected setupObserver(): void {
    const targetNode = document.querySelector(this.selectors.conversationContainer);
    
    if (!targetNode) {
      console.warn(`Conversation container not found: ${this.selectors.conversationContainer}`);
      return;
    }
    
    this.observer = new MutationObserver((mutations) => {
      this.handleMutations(mutations);
    });
    
    this.observer.observe(targetNode, {
      childList: true,
      subtree: true,
      characterData: true
    });
  }
  
  // 处理DOM变化
  protected handleMutations(mutations: MutationRecord[]): void {
    let hasNewContent = false;
    
    mutations.forEach((mutation) => {
      if (this.isRelevantMutation(mutation)) {
        hasNewContent = true;
      }
    });
    
    if (hasNewContent) {
      this.debounceExtractConversation();
    }
  }
  
  // 防抖提取会话
  private debounceExtractConversation = this.debounce(() => {
    this.extractConversation();
  }, 1000);
  
  // 提取现有会话
  protected extractExistingConversation(): void {
    const conversation = this.extractConversation();
    if (conversation && conversation.messages.length > 0) {
      this.saveConversation(conversation);
    }
  }
  
  // 提取会话数据
  protected abstract extractConversation(): ConversationData | null;
  
  // 判断是否为相关的DOM变化
  protected abstract isRelevantMutation(mutation: MutationRecord): boolean;
  
  // 保存会话数据
  protected async saveConversation(conversation: ConversationData): Promise<void> {
    // 发送消息到background script
    chrome.runtime.sendMessage({
      type: 'SAVE_CONVERSATION',
      data: conversation
    });
  }
  
  // 清理资源
  destroy(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  }
  
  // 工具函数：防抖
  private debounce(func: Function, wait: number): Function {
    let timeout: NodeJS.Timeout;
    return function executedFunction(...args: any[]) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
}

export interface PlatformSelectors {
  conversationContainer: string;
  messageElements: string;
  userMessage: string;
  assistantMessage: string;
  messageContent: string;
  conversationTitle?: string;
  loadingIndicator?: string;
}
```

### 2. ChatGPT 适配器实现

```typescript
// src/content/platforms/chatgpt.ts
export class ChatGPTAdapter extends BasePlatformAdapter {
  readonly platform = 'ChatGPT';
  readonly selectors: PlatformSelectors = {
    conversationContainer: '[data-testid="conversation-turn"]',
    messageElements: '[data-testid="conversation-turn"]',
    userMessage: '[data-message-author-role="user"]',
    assistantMessage: '[data-message-author-role="assistant"]',
    messageContent: '.markdown',
    conversationTitle: 'h1',
    loadingIndicator: '.result-streaming'
  };
  
  protected isPageReady(): boolean {
    return document.querySelector(this.selectors.conversationContainer) !== null;
  }
  
  protected isRelevantMutation(mutation: MutationRecord): boolean {
    // 检查是否有新的消息元素添加
    if (mutation.type === 'childList') {
      for (const node of Array.from(mutation.addedNodes)) {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element;
          if (element.matches(this.selectors.messageElements) ||
              element.querySelector(this.selectors.messageElements)) {
            return true;
          }
        }
      }
    }
    
    // 检查消息内容是否有变化（流式输出）
    if (mutation.type === 'characterData' || mutation.type === 'childList') {
      const target = mutation.target as Element;
      if (target.closest && target.closest(this.selectors.assistantMessage)) {
        return true;
      }
    }
    
    return false;
  }
  
  protected extractConversation(): ConversationData | null {
    const messages = this.extractMessages();
    
    if (messages.length === 0) {
      return null;
    }
    
    const title = this.extractTitle();
    const url = window.location.href;
    
    return {
      id: this.generateConversationId(url),
      platform: this.platform,
      url,
      title,
      messages,
      tags: [this.platform],
      notes: '',
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        messageCount: messages.length,
        lastActivity: new Date(),
        isArchived: false
      }
    };
  }
  
  private extractMessages(): MessageData[] {
    const messages: MessageData[] = [];
    const messageElements = document.querySelectorAll(this.selectors.messageElements);
    
    messageElements.forEach((element, index) => {
      const userMsg = element.querySelector(this.selectors.userMessage);
      const assistantMsg = element.querySelector(this.selectors.assistantMessage);
      
      if (userMsg) {
        const content = this.extractMessageContent(userMsg);
        if (content) {
          messages.push({
            id: `user_${index}`,
            conversationId: '',
            type: 'user',
            content,
            timestamp: new Date(),
            metadata: {
              platform: this.platform,
              originalElement: userMsg.outerHTML
            }
          });
        }
      }
      
      if (assistantMsg) {
        const content = this.extractMessageContent(assistantMsg);
        if (content) {
          messages.push({
            id: `assistant_${index}`,
            conversationId: '',
            type: 'assistant',
            content,
            timestamp: new Date(),
            metadata: {
              platform: this.platform,
              originalElement: assistantMsg.outerHTML
            }
          });
        }
      }
    });
    
    return messages;
  }
  
  private extractMessageContent(element: Element): string {
    const contentElement = element.querySelector(this.selectors.messageContent);
    if (!contentElement) return '';
    
    // 处理代码块
    const codeBlocks = contentElement.querySelectorAll('pre code');
    codeBlocks.forEach((block, index) => {
      const language = this.detectCodeLanguage(block);
      block.textContent = `[代码块:${language}]\n${block.textContent}`;
    });
    
    // 处理图片
    const images = contentElement.querySelectorAll('img');
    images.forEach(img => {
      img.replaceWith(document.createTextNode('[图片]'));
    });
    
    // 处理链接
    const links = contentElement.querySelectorAll('a');
    links.forEach(link => {
      const text = link.textContent || '[链接]';
      const href = link.getAttribute('href');
      link.replaceWith(document.createTextNode(`${text}(${href})`));
    });
    
    return contentElement.textContent?.trim() || '';
  }
  
  private extractTitle(): string {
    const titleElement = document.querySelector(this.selectors.conversationTitle!);
    return titleElement?.textContent?.trim() || '未命名会话';
  }
  
  private detectCodeLanguage(codeElement: Element): string {
    const classes = codeElement.className;
    const match = classes.match(/language-(\w+)/);
    return match ? match[1] : '未知';
  }
  
  private generateConversationId(url: string): string {
    // 从URL中提取会话ID
    const match = url.match(/\/c\/([a-zA-Z0-9-]+)/);
    return match ? match[1] : btoa(url).slice(0, 16);
  }
}
```

### 3. Claude 适配器实现

```typescript
// src/content/platforms/claude.ts
export class ClaudeAdapter extends BasePlatformAdapter {
  readonly platform = 'Claude';
  readonly selectors: PlatformSelectors = {
    conversationContainer: '[data-testid="conversation"]',
    messageElements: '[data-testid="message"]',
    userMessage: '[data-is-author="true"]',
    assistantMessage: '[data-is-author="false"]',
    messageContent: '.font-claude-message',
    conversationTitle: 'h1[data-testid="conversation-title"]'
  };
  
  protected isPageReady(): boolean {
    return document.querySelector(this.selectors.conversationContainer) !== null;
  }
  
  protected isRelevantMutation(mutation: MutationRecord): boolean {
    // Claude特定的变化检测逻辑
    if (mutation.type === 'childList') {
      for (const node of Array.from(mutation.addedNodes)) {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element;
          if (element.matches('[data-testid="message"]')) {
            return true;
          }
        }
      }
    }
    
    return false;
  }
  
  protected extractConversation(): ConversationData | null {
    // Claude特定的提取逻辑
    // 实现细节类似ChatGPT，但使用Claude特定的选择器
    // ...
    return null; // 简化示例
  }
}
```

## 🔄 自适应检测机制

### 1. 页面结构变化检测

```typescript
// src/content/structure-detector.ts
export class StructureDetector {
  private static readonly STRUCTURE_CACHE = new Map<string, StructureSignature>();
  
  static analyzePageStructure(): StructureSignature {
    const signature: StructureSignature = {
      selectors: this.extractKeySelectors(),
      attributes: this.extractKeyAttributes(),
      textPatterns: this.extractTextPatterns(),
      timestamp: Date.now()
    };
    
    return signature;
  }
  
  static detectStructureChange(platform: string): boolean {
    const currentStructure = this.analyzePageStructure();
    const cachedStructure = this.STRUCTURE_CACHE.get(platform);
    
    if (!cachedStructure) {
      this.STRUCTURE_CACHE.set(platform, currentStructure);
      return false;
    }
    
    const similarity = this.calculateSimilarity(currentStructure, cachedStructure);
    
    if (similarity < 0.8) { // 80%相似度阈值
      console.warn(`Structure change detected for ${platform}:`, {
        similarity,
        current: currentStructure,
        cached: cachedStructure
      });
      
      this.STRUCTURE_CACHE.set(platform, currentStructure);
      return true;
    }
    
    return false;
  }
  
  private static extractKeySelectors(): string[] {
    const selectors: string[] = [];
    
    // 提取常见的消息容器选择器
    const commonPatterns = [
      '[data-testid*="message"]',
      '[data-testid*="conversation"]',
      '[class*="message"]',
      '[class*="chat"]',
      '[role="main"]'
    ];
    
    commonPatterns.forEach(pattern => {
      if (document.querySelector(pattern)) {
        selectors.push(pattern);
      }
    });
    
    return selectors;
  }
  
  private static calculateSimilarity(
    current: StructureSignature, 
    cached: StructureSignature
  ): number {
    const selectorSimilarity = this.arrayJaccardSimilarity(
      current.selectors, 
      cached.selectors
    );
    
    const attributeSimilarity = this.arrayJaccardSimilarity(
      current.attributes,
      cached.attributes
    );
    
    return (selectorSimilarity + attributeSimilarity) / 2;
  }
  
  private static arrayJaccardSimilarity(arr1: string[], arr2: string[]): number {
    const set1 = new Set(arr1);
    const set2 = new Set(arr2);
    
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);
    
    return intersection.size / union.size;
  }
}

interface StructureSignature {
  selectors: string[];
  attributes: string[];
  textPatterns: string[];
  timestamp: number;
}
```

### 2. 智能选择器生成

```typescript
// src/content/selector-generator.ts
export class SelectorGenerator {
  static generateRobustSelector(element: Element): string {
    const selectors: string[] = [];
    
    // 1. 尝试使用data属性
    const dataAttrs = this.getDataAttributes(element);
    if (dataAttrs.length > 0) {
      selectors.push(...dataAttrs.map(attr => `[${attr}]`));
    }
    
    // 2. 尝试使用class组合
    const classSelector = this.generateClassSelector(element);
    if (classSelector) {
      selectors.push(classSelector);
    }
    
    // 3. 尝试使用结构选择器
    const structuralSelector = this.generateStructuralSelector(element);
    if (structuralSelector) {
      selectors.push(structuralSelector);
    }
    
    // 返回最稳定的选择器
    return this.selectBestSelector(selectors, element);
  }
  
  private static getDataAttributes(element: Element): string[] {
    const dataAttrs: string[] = [];
    
    for (const attr of element.attributes) {
      if (attr.name.startsWith('data-')) {
        dataAttrs.push(`${attr.name}="${attr.value}"`);
      }
    }
    
    return dataAttrs;
  }
  
  private static generateClassSelector(element: Element): string | null {
    const classes = Array.from(element.classList);
    
    // 过滤掉动态类名
    const stableClasses = classes.filter(cls => 
      !cls.match(/^(css-|_|[a-f0-9]{6,})/) // 过滤CSS-in-JS生成的类名
    );
    
    if (stableClasses.length > 0) {
      return '.' + stableClasses.join('.');
    }
    
    return null;
  }
  
  private static selectBestSelector(selectors: string[], element: Element): string {
    // 测试每个选择器的唯一性和稳定性
    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length === 1 && elements[0] === element) {
        return selector;
      }
    }
    
    // 如果没有唯一选择器，返回最具体的一个
    return selectors[0] || this.generateFallbackSelector(element);
  }
  
  private static generateFallbackSelector(element: Element): string {
    const path: string[] = [];
    let current: Element | null = element;
    
    while (current && current !== document.body) {
      const tagName = current.tagName.toLowerCase();
      const index = Array.from(current.parentElement?.children || [])
        .filter(child => child.tagName === current!.tagName)
        .indexOf(current);
      
      path.unshift(`${tagName}:nth-of-type(${index + 1})`);
      current = current.parentElement;
    }
    
    return path.join(' > ');
  }
}
```

这个多平台适配方案的优势：

1. **轻量级**: 纯前端实现，无外部依赖
2. **可扩展**: 易于添加新平台支持
3. **自适应**: 能检测页面结构变化
4. **稳定性**: 多层选择器策略确保可靠性
5. **性能优化**: 防抖机制避免频繁处理

您觉得这个方案如何？需要我详细展开某个特定平台的适配实现吗？
