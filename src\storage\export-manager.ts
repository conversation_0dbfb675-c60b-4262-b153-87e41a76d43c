/**
 * 导出管理器
 * 负责会话数据的导出功能，支持多种格式
 */

import { ConversationData, MessageData } from '@types/conversation';
import { DatabaseManager } from './database';
import { Logger } from '@shared/logger';

export interface ExportOptions {
  format: 'markdown' | 'pdf' | 'json' | 'html' | 'txt';
  includeMetadata: boolean;
  includeImages: boolean;
  dateFormat: string;
  conversationIds?: string[]; // 指定导出的会话ID，为空则导出所有
  dateRange?: {
    start: Date;
    end: Date;
  };
  platforms?: string[];
  tags?: string[];
}

export interface ExportResult {
  success: boolean;
  data?: Blob | string;
  filename: string;
  mimeType: string;
  error?: string;
}

export class ExportManager {
  private db: DatabaseManager;
  private logger: Logger;

  constructor(db: DatabaseManager) {
    this.db = db;
    this.logger = new Logger('ExportManager');
  }

  /**
   * 导出会话数据
   */
  async exportConversations(options: ExportOptions): Promise<ExportResult> {
    try {
      this.logger.info(`开始导出会话数据，格式: ${options.format}`);
      
      // 获取要导出的会话
      const conversations = await this.getConversationsForExport(options);
      
      if (conversations.length === 0) {
        return {
          success: false,
          filename: '',
          mimeType: '',
          error: '没有找到符合条件的会话数据'
        };
      }

      // 根据格式执行导出
      switch (options.format) {
        case 'markdown':
          return await this.exportToMarkdown(conversations, options);
        case 'json':
          return await this.exportToJSON(conversations, options);
        case 'html':
          return await this.exportToHTML(conversations, options);
        case 'txt':
          return await this.exportToText(conversations, options);
        case 'pdf':
          return await this.exportToPDF(conversations, options);
        default:
          throw new Error(`不支持的导出格式: ${options.format}`);
      }
    } catch (error) {
      this.logger.error('导出失败:', error);
      return {
        success: false,
        filename: '',
        mimeType: '',
        error: error.message
      };
    }
  }

  /**
   * 获取要导出的会话数据
   */
  private async getConversationsForExport(options: ExportOptions): Promise<ConversationData[]> {
    let conversations: ConversationData[];

    if (options.conversationIds && options.conversationIds.length > 0) {
      // 导出指定的会话
      conversations = [];
      for (const id of options.conversationIds) {
        const conversation = await this.db.get('conversations', id);
        if (conversation) {
          // 获取消息
          const messages = await this.db.getByIndex('messages', 'conversationId', id);
          conversation.messages = messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
          conversations.push(conversation);
        }
      }
    } else {
      // 导出所有会话
      conversations = await this.db.getAll('conversations');
      
      // 为每个会话加载消息
      for (const conversation of conversations) {
        const messages = await this.db.getByIndex('messages', 'conversationId', conversation.id);
        conversation.messages = messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
      }
    }

    // 应用过滤器
    conversations = this.applyFilters(conversations, options);
    
    return conversations;
  }

  /**
   * 应用过滤器
   */
  private applyFilters(conversations: ConversationData[], options: ExportOptions): ConversationData[] {
    let filtered = conversations;

    // 日期范围过滤
    if (options.dateRange) {
      filtered = filtered.filter(c => 
        c.createdAt >= options.dateRange!.start && 
        c.createdAt <= options.dateRange!.end
      );
    }

    // 平台过滤
    if (options.platforms && options.platforms.length > 0) {
      filtered = filtered.filter(c => options.platforms!.includes(c.platform));
    }

    // 标签过滤
    if (options.tags && options.tags.length > 0) {
      filtered = filtered.filter(c => 
        options.tags!.some(tag => c.tags.includes(tag))
      );
    }

    return filtered;
  }

  /**
   * 导出为 Markdown 格式
   */
  private async exportToMarkdown(conversations: ConversationData[], options: ExportOptions): Promise<ExportResult> {
    let content = '# AI Chat Memo 导出\n\n';
    
    if (options.includeMetadata) {
      content += `**导出时间**: ${this.formatDate(new Date(), options.dateFormat)}\n`;
      content += `**会话数量**: ${conversations.length}\n`;
      content += `**消息总数**: ${conversations.reduce((sum, c) => sum + c.messages.length, 0)}\n\n`;
      content += '---\n\n';
    }

    for (const conversation of conversations) {
      content += `## ${conversation.title}\n\n`;
      
      if (options.includeMetadata) {
        content += `**平台**: ${conversation.platform}\n`;
        content += `**创建时间**: ${this.formatDate(conversation.createdAt, options.dateFormat)}\n`;
        content += `**更新时间**: ${this.formatDate(conversation.updatedAt, options.dateFormat)}\n`;
        content += `**URL**: ${conversation.url}\n`;
        
        if (conversation.tags.length > 0) {
          content += `**标签**: ${conversation.tags.map(tag => `\`${tag}\``).join(', ')}\n`;
        }
        
        content += '\n';
      }

      for (const message of conversation.messages) {
        const role = message.type === 'user' ? '👤 用户' : '🤖 AI助手';
        content += `### ${role}\n\n`;
        content += `${this.processMessageContent(message.content, options)}\n\n`;
        
        if (options.includeMetadata) {
          content += `*时间: ${this.formatDate(message.timestamp, options.dateFormat)}*\n\n`;
        }
      }
      
      content += '---\n\n';
    }

    const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });
    const filename = `ai-chat-memo-${this.formatDate(new Date(), 'YYYY-MM-DD')}.md`;

    return {
      success: true,
      data: blob,
      filename,
      mimeType: 'text/markdown'
    };
  }

  /**
   * 导出为 JSON 格式
   */
  private async exportToJSON(conversations: ConversationData[], options: ExportOptions): Promise<ExportResult> {
    const exportData = {
      metadata: {
        exportTime: new Date().toISOString(),
        version: '1.0.0',
        totalConversations: conversations.length,
        totalMessages: conversations.reduce((sum, c) => sum + c.messages.length, 0)
      },
      conversations: conversations.map(conversation => ({
        ...conversation,
        messages: conversation.messages.map(message => ({
          ...message,
          content: options.includeImages ? message.content : this.stripImages(message.content)
        }))
      }))
    };

    const content = JSON.stringify(exportData, null, 2);
    const blob = new Blob([content], { type: 'application/json;charset=utf-8' });
    const filename = `ai-chat-memo-${this.formatDate(new Date(), 'YYYY-MM-DD')}.json`;

    return {
      success: true,
      data: blob,
      filename,
      mimeType: 'application/json'
    };
  }

  /**
   * 导出为 HTML 格式
   */
  private async exportToHTML(conversations: ConversationData[], options: ExportOptions): Promise<ExportResult> {
    let html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Memo 导出</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { border-bottom: 2px solid #eee; padding-bottom: 20px; margin-bottom: 30px; }
        .conversation { margin-bottom: 40px; border: 1px solid #ddd; border-radius: 8px; padding: 20px; }
        .conversation-title { color: #333; margin-bottom: 15px; }
        .conversation-meta { background: #f8f9fa; padding: 10px; border-radius: 4px; margin-bottom: 20px; font-size: 0.9em; }
        .message { margin-bottom: 20px; padding: 15px; border-radius: 8px; }
        .message.user { background: #e3f2fd; border-left: 4px solid #2196f3; }
        .message.assistant { background: #f3e5f5; border-left: 4px solid #9c27b0; }
        .message-role { font-weight: bold; margin-bottom: 8px; }
        .message-content { white-space: pre-wrap; }
        .message-time { font-size: 0.8em; color: #666; margin-top: 8px; }
        code { background: #f5f5f5; padding: 2px 4px; border-radius: 3px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI Chat Memo 导出</h1>`;

    if (options.includeMetadata) {
      html += `
        <p><strong>导出时间</strong>: ${this.formatDate(new Date(), options.dateFormat)}</p>
        <p><strong>会话数量</strong>: ${conversations.length}</p>
        <p><strong>消息总数</strong>: ${conversations.reduce((sum, c) => sum + c.messages.length, 0)}</p>`;
    }

    html += `
    </div>`;

    for (const conversation of conversations) {
      html += `
    <div class="conversation">
        <h2 class="conversation-title">${this.escapeHtml(conversation.title)}</h2>`;

      if (options.includeMetadata) {
        html += `
        <div class="conversation-meta">
            <p><strong>平台</strong>: ${conversation.platform}</p>
            <p><strong>创建时间</strong>: ${this.formatDate(conversation.createdAt, options.dateFormat)}</p>
            <p><strong>URL</strong>: <a href="${conversation.url}" target="_blank">${conversation.url}</a></p>
            ${conversation.tags.length > 0 ? `<p><strong>标签</strong>: ${conversation.tags.map(tag => `<span style="background: #e0e0e0; padding: 2px 6px; border-radius: 3px; margin-right: 5px;">${this.escapeHtml(tag)}</span>`).join('')}</p>` : ''}
        </div>`;
      }

      for (const message of conversation.messages) {
        const roleText = message.type === 'user' ? '👤 用户' : '🤖 AI助手';
        html += `
        <div class="message ${message.type}">
            <div class="message-role">${roleText}</div>
            <div class="message-content">${this.processMessageContentForHTML(message.content, options)}</div>
            ${options.includeMetadata ? `<div class="message-time">${this.formatDate(message.timestamp, options.dateFormat)}</div>` : ''}
        </div>`;
      }

      html += `
    </div>`;
    }

    html += `
</body>
</html>`;

    const blob = new Blob([html], { type: 'text/html;charset=utf-8' });
    const filename = `ai-chat-memo-${this.formatDate(new Date(), 'YYYY-MM-DD')}.html`;

    return {
      success: true,
      data: blob,
      filename,
      mimeType: 'text/html'
    };
  }

  /**
   * 导出为纯文本格式
   */
  private async exportToText(conversations: ConversationData[], options: ExportOptions): Promise<ExportResult> {
    let content = 'AI Chat Memo 导出\n';
    content += '='.repeat(50) + '\n\n';
    
    if (options.includeMetadata) {
      content += `导出时间: ${this.formatDate(new Date(), options.dateFormat)}\n`;
      content += `会话数量: ${conversations.length}\n`;
      content += `消息总数: ${conversations.reduce((sum, c) => sum + c.messages.length, 0)}\n\n`;
    }

    for (const conversation of conversations) {
      content += `${conversation.title}\n`;
      content += '-'.repeat(conversation.title.length) + '\n\n';
      
      if (options.includeMetadata) {
        content += `平台: ${conversation.platform}\n`;
        content += `创建时间: ${this.formatDate(conversation.createdAt, options.dateFormat)}\n`;
        content += `URL: ${conversation.url}\n`;
        if (conversation.tags.length > 0) {
          content += `标签: ${conversation.tags.join(', ')}\n`;
        }
        content += '\n';
      }

      for (const message of conversation.messages) {
        const role = message.type === 'user' ? '[用户]' : '[AI助手]';
        content += `${role}\n`;
        content += `${this.stripHtml(message.content)}\n\n`;
        
        if (options.includeMetadata) {
          content += `时间: ${this.formatDate(message.timestamp, options.dateFormat)}\n\n`;
        }
      }
      
      content += '='.repeat(50) + '\n\n';
    }

    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const filename = `ai-chat-memo-${this.formatDate(new Date(), 'YYYY-MM-DD')}.txt`;

    return {
      success: true,
      data: blob,
      filename,
      mimeType: 'text/plain'
    };
  }

  /**
   * 导出为 PDF 格式（简化实现）
   */
  private async exportToPDF(conversations: ConversationData[], options: ExportOptions): Promise<ExportResult> {
    // 这里应该使用 PDF 生成库，如 jsPDF 或 Puppeteer
    // 为了简化，我们先返回一个错误
    throw new Error('PDF 导出功能正在开发中，请使用其他格式');
  }

  /**
   * 处理消息内容
   */
  private processMessageContent(content: string, options: ExportOptions): string {
    let processed = content;
    
    if (!options.includeImages) {
      processed = this.stripImages(processed);
    }
    
    return processed;
  }

  /**
   * 处理 HTML 格式的消息内容
   */
  private processMessageContentForHTML(content: string, options: ExportOptions): string {
    let processed = this.escapeHtml(content);
    
    if (!options.includeImages) {
      processed = this.stripImages(processed);
    }
    
    // 简单的 Markdown 到 HTML 转换
    processed = processed
      .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
      .replace(/`([^`]+)`/g, '<code>$1</code>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>');
    
    return processed;
  }

  /**
   * 移除图片内容
   */
  private stripImages(content: string): string {
    return content
      .replace(/!\[.*?\]\(.*?\)/g, '[图片]')
      .replace(/<img[^>]*>/g, '[图片]');
  }

  /**
   * 移除 HTML 标签
   */
  private stripHtml(content: string): string {
    return content.replace(/<[^>]*>/g, '');
  }

  /**
   * HTML 转义
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 格式化日期
   */
  private formatDate(date: Date, format: string): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  }
}
