/**
 * 会话管理器
 * 负责会话的创建、更新、合并、删除等核心管理功能
 */

import { ConversationData, MessageData, ConversationFilter, ConversationSortOptions } from '@types/conversation';
import { storageService } from '@storage/index';
import { Logger } from '@shared/logger';
import { MessageBus } from '@shared/message-bus';
import { EVENTS } from '@types/events';

export interface ConversationMergeOptions {
  targetId: string;
  sourceIds: string[];
  mergeStrategy: 'chronological' | 'by-source' | 'manual';
  keepSeparateMessages: boolean;
  newTitle?: string;
  newTags?: string[];
}

export interface ConversationDuplicateOptions {
  similarity: number; // 0-1, 相似度阈值
  checkTitle: boolean;
  checkContent: boolean;
  checkPlatform: boolean;
  checkTimeRange: number; // 小时数
}

export interface ConversationArchiveOptions {
  archiveOlderThan?: Date;
  platforms?: string[];
  tags?: string[];
  messageCountLessThan?: number;
}

export class ConversationManager {
  private logger: Logger;
  private messageBus: MessageBus;
  private duplicateCheckCache: Map<string, string[]> = new Map();

  constructor() {
    this.logger = new Logger('ConversationManager');
    this.messageBus = new MessageBus('conversation-manager');
    this.setupEventListeners();
  }

  /**
   * 初始化会话管理器
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('初始化会话管理器...');
      
      // 确保存储服务已初始化
      if (!storageService.isInitialized) {
        await storageService.initialize();
      }
      
      // 执行启动时的维护任务
      await this.performStartupMaintenance();
      
      this.logger.info('会话管理器初始化完成');
    } catch (error) {
      this.logger.error('会话管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 创建新会话
   */
  async createConversation(conversationData: Partial<ConversationData>): Promise<ConversationData> {
    try {
      this.logger.info(`创建新会话: ${conversationData.title}`);
      
      // 生成唯一ID
      const id = this.generateConversationId();
      const now = new Date();
      
      // 构建完整的会话数据
      const conversation: ConversationData = {
        id,
        title: conversationData.title || '未命名会话',
        platform: conversationData.platform || 'Unknown',
        url: conversationData.url || '',
        tags: conversationData.tags || [],
        messages: conversationData.messages || [],
        createdAt: now,
        updatedAt: now,
        metadata: {
          messageCount: conversationData.messages?.length || 0,
          isArchived: false,
          lastActivity: now,
          ...conversationData.metadata
        }
      };

      // 检查重复会话
      const duplicates = await this.findDuplicateConversations(conversation, {
        similarity: 0.8,
        checkTitle: true,
        checkContent: true,
        checkPlatform: true,
        checkTimeRange: 24
      });

      if (duplicates.length > 0) {
        this.logger.warn(`发现 ${duplicates.length} 个可能重复的会话`);
        // 可以选择合并或提示用户
      }

      // 保存会话
      await storageService.conversations.saveConversation(conversation);
      
      // 发送事件
      this.messageBus.emit(EVENTS.CONVERSATION_CREATED, conversation);
      
      this.logger.info(`会话创建成功: ${conversation.id}`);
      return conversation;
    } catch (error) {
      this.logger.error('创建会话失败:', error);
      throw error;
    }
  }

  /**
   * 更新会话
   */
  async updateConversation(id: string, updates: Partial<ConversationData>): Promise<ConversationData> {
    try {
      this.logger.debug(`更新会话: ${id}`);
      
      // 获取现有会话
      const existing = await storageService.conversations.getConversation(id);
      if (!existing) {
        throw new Error(`会话不存在: ${id}`);
      }

      // 合并更新数据
      const updated: ConversationData = {
        ...existing,
        ...updates,
        id, // 确保ID不被修改
        updatedAt: new Date(),
        metadata: {
          ...existing.metadata,
          ...updates.metadata,
          messageCount: updates.messages?.length || existing.metadata.messageCount,
          lastActivity: new Date()
        }
      };

      // 保存更新
      await storageService.conversations.saveConversation(updated);
      
      // 发送事件
      this.messageBus.emit(EVENTS.CONVERSATION_UPDATED, updated);
      
      this.logger.debug(`会话更新成功: ${id}`);
      return updated;
    } catch (error) {
      this.logger.error(`更新会话失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 删除会话
   */
  async deleteConversation(id: string, permanent: boolean = false): Promise<void> {
    try {
      this.logger.info(`删除会话: ${id} (永久删除: ${permanent})`);
      
      if (permanent) {
        // 永久删除
        await storageService.conversations.deleteConversation(id);
        this.messageBus.emit(EVENTS.CONVERSATION_DELETED, { id, permanent: true });
      } else {
        // 软删除（归档）
        await this.updateConversation(id, {
          metadata: { isArchived: true }
        });
        this.messageBus.emit(EVENTS.CONVERSATION_ARCHIVED, { id });
      }
      
      // 清理缓存
      this.duplicateCheckCache.delete(id);
      
      this.logger.info(`会话删除成功: ${id}`);
    } catch (error) {
      this.logger.error(`删除会话失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 批量删除会话
   */
  async deleteConversations(ids: string[], permanent: boolean = false): Promise<{ success: string[], failed: string[] }> {
    const results = { success: [], failed: [] };
    
    for (const id of ids) {
      try {
        await this.deleteConversation(id, permanent);
        results.success.push(id);
      } catch (error) {
        this.logger.error(`批量删除会话失败: ${id}`, error);
        results.failed.push(id);
      }
    }
    
    this.logger.info(`批量删除完成: 成功 ${results.success.length}, 失败 ${results.failed.length}`);
    return results;
  }

  /**
   * 合并会话
   */
  async mergeConversations(options: ConversationMergeOptions): Promise<ConversationData> {
    try {
      this.logger.info(`合并会话: ${options.sourceIds.join(', ')} -> ${options.targetId}`);
      
      // 获取目标会话和源会话
      const target = await storageService.conversations.getConversation(options.targetId);
      if (!target) {
        throw new Error(`目标会话不存在: ${options.targetId}`);
      }

      const sources = await Promise.all(
        options.sourceIds.map(id => storageService.conversations.getConversation(id))
      );

      // 检查源会话是否都存在
      const missingIds = options.sourceIds.filter((id, index) => !sources[index]);
      if (missingIds.length > 0) {
        throw new Error(`源会话不存在: ${missingIds.join(', ')}`);
      }

      // 合并消息
      const allMessages = [...target.messages];
      
      for (let i = 0; i < sources.length; i++) {
        const source = sources[i];
        if (options.keepSeparateMessages) {
          // 添加分隔符消息
          allMessages.push({
            id: `separator-${Date.now()}-${i}`,
            type: 'system',
            content: `--- 来自会话: ${source.title} ---`,
            timestamp: new Date(),
            metadata: { isSeparator: true, sourceConversationId: source.id }
          } as MessageData);
        }
        
        allMessages.push(...source.messages);
      }

      // 根据策略排序消息
      if (options.mergeStrategy === 'chronological') {
        allMessages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
      }

      // 合并标签
      const allTags = new Set([...target.tags]);
      sources.forEach(source => {
        source.tags.forEach(tag => allTags.add(tag));
      });
      
      if (options.newTags) {
        options.newTags.forEach(tag => allTags.add(tag));
      }

      // 更新目标会话
      const mergedConversation = await this.updateConversation(options.targetId, {
        title: options.newTitle || target.title,
        messages: allMessages,
        tags: Array.from(allTags),
        metadata: {
          ...target.metadata,
          messageCount: allMessages.length,
          mergedFrom: options.sourceIds,
          mergedAt: new Date()
        }
      });

      // 删除源会话
      await this.deleteConversations(options.sourceIds, true);
      
      // 发送事件
      this.messageBus.emit(EVENTS.CONVERSATIONS_MERGED, {
        target: mergedConversation,
        sources: options.sourceIds
      });
      
      this.logger.info(`会话合并成功: ${options.targetId}`);
      return mergedConversation;
    } catch (error) {
      this.logger.error('合并会话失败:', error);
      throw error;
    }
  }

  /**
   * 复制会话
   */
  async duplicateConversation(id: string, newTitle?: string): Promise<ConversationData> {
    try {
      this.logger.info(`复制会话: ${id}`);
      
      const original = await storageService.conversations.getConversation(id);
      if (!original) {
        throw new Error(`会话不存在: ${id}`);
      }

      // 创建副本
      const duplicate = await this.createConversation({
        ...original,
        title: newTitle || `${original.title} (副本)`,
        messages: original.messages.map(msg => ({
          ...msg,
          id: this.generateMessageId()
        })),
        metadata: {
          ...original.metadata,
          duplicatedFrom: id,
          duplicatedAt: new Date()
        }
      });

      this.logger.info(`会话复制成功: ${duplicate.id}`);
      return duplicate;
    } catch (error) {
      this.logger.error(`复制会话失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 查找重复会话
   */
  async findDuplicateConversations(
    conversation: ConversationData, 
    options: ConversationDuplicateOptions
  ): Promise<ConversationData[]> {
    try {
      // 检查缓存
      const cacheKey = this.getDuplicateCacheKey(conversation, options);
      if (this.duplicateCheckCache.has(cacheKey)) {
        const cachedIds = this.duplicateCheckCache.get(cacheKey)!;
        const cached = await Promise.all(
          cachedIds.map(id => storageService.conversations.getConversation(id))
        );
        return cached.filter(c => c !== null) as ConversationData[];
      }

      const filter: ConversationFilter = {};
      
      // 平台过滤
      if (options.checkPlatform) {
        filter.platforms = [conversation.platform];
      }
      
      // 时间范围过滤
      if (options.checkTimeRange > 0) {
        const timeThreshold = new Date(Date.now() - options.checkTimeRange * 60 * 60 * 1000);
        filter.dateRange = { start: timeThreshold, end: new Date() };
      }

      // 获取候选会话
      const candidates = await storageService.conversations.getConversations(filter);
      const duplicates: ConversationData[] = [];

      for (const candidate of candidates.conversations) {
        if (candidate.id === conversation.id) continue;
        
        const similarity = this.calculateSimilarity(conversation, candidate, options);
        if (similarity >= options.similarity) {
          duplicates.push(candidate);
        }
      }

      // 缓存结果
      this.duplicateCheckCache.set(cacheKey, duplicates.map(d => d.id));
      
      return duplicates;
    } catch (error) {
      this.logger.error('查找重复会话失败:', error);
      return [];
    }
  }

  /**
   * 批量归档会话
   */
  async archiveConversations(options: ConversationArchiveOptions): Promise<{ archived: string[], failed: string[] }> {
    try {
      this.logger.info('开始批量归档会话');

      const filter: ConversationFilter = {
        isArchived: false
      };

      // 应用过滤条件
      if (options.archiveOlderThan) {
        filter.dateRange = { start: new Date(0), end: options.archiveOlderThan };
      }

      if (options.platforms) {
        filter.platforms = options.platforms;
      }

      if (options.tags) {
        filter.tags = options.tags;
      }

      // 获取符合条件的会话
      const result = await storageService.conversations.getConversations(filter);
      let candidates = result.conversations;

      // 按消息数量过滤
      if (options.messageCountLessThan) {
        candidates = candidates.filter(c => c.metadata.messageCount < options.messageCountLessThan!);
      }

      // 批量归档
      const archived: string[] = [];
      const failed: string[] = [];

      for (const conversation of candidates) {
        try {
          await this.updateConversation(conversation.id, {
            metadata: { ...conversation.metadata, isArchived: true, archivedAt: new Date() }
          });
          archived.push(conversation.id);
        } catch (error) {
          this.logger.error(`归档会话失败: ${conversation.id}`, error);
          failed.push(conversation.id);
        }
      }

      this.logger.info(`批量归档完成: 成功 ${archived.length}, 失败 ${failed.length}`);
      return { archived, failed };
    } catch (error) {
      this.logger.error('批量归档失败:', error);
      throw error;
    }
  }

  /**
   * 恢复归档的会话
   */
  async unarchiveConversation(id: string): Promise<ConversationData> {
    try {
      this.logger.info(`恢复归档会话: ${id}`);

      const conversation = await this.updateConversation(id, {
        metadata: { isArchived: false, unarchivedAt: new Date() }
      });

      this.messageBus.emit(EVENTS.CONVERSATION_UNARCHIVED, { id });
      return conversation;
    } catch (error) {
      this.logger.error(`恢复归档会话失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 添加消息到会话
   */
  async addMessageToConversation(conversationId: string, message: Omit<MessageData, 'id'>): Promise<MessageData> {
    try {
      this.logger.debug(`添加消息到会话: ${conversationId}`);

      const conversation = await storageService.conversations.getConversation(conversationId);
      if (!conversation) {
        throw new Error(`会话不存在: ${conversationId}`);
      }

      // 创建新消息
      const newMessage: MessageData = {
        ...message,
        id: this.generateMessageId()
      };

      // 更新会话
      const updatedMessages = [...conversation.messages, newMessage];
      await this.updateConversation(conversationId, {
        messages: updatedMessages
      });

      this.messageBus.emit(EVENTS.MESSAGE_ADDED, { conversationId, message: newMessage });

      this.logger.debug(`消息添加成功: ${newMessage.id}`);
      return newMessage;
    } catch (error) {
      this.logger.error(`添加消息失败: ${conversationId}`, error);
      throw error;
    }
  }

  /**
   * 更新会话中的消息
   */
  async updateMessageInConversation(
    conversationId: string,
    messageId: string,
    updates: Partial<MessageData>
  ): Promise<MessageData> {
    try {
      this.logger.debug(`更新会话消息: ${conversationId}/${messageId}`);

      const conversation = await storageService.conversations.getConversation(conversationId);
      if (!conversation) {
        throw new Error(`会话不存在: ${conversationId}`);
      }

      const messageIndex = conversation.messages.findIndex(m => m.id === messageId);
      if (messageIndex === -1) {
        throw new Error(`消息不存在: ${messageId}`);
      }

      // 更新消息
      const updatedMessage = {
        ...conversation.messages[messageIndex],
        ...updates,
        id: messageId // 确保ID不被修改
      };

      const updatedMessages = [...conversation.messages];
      updatedMessages[messageIndex] = updatedMessage;

      // 更新会话
      await this.updateConversation(conversationId, {
        messages: updatedMessages
      });

      this.messageBus.emit(EVENTS.MESSAGE_UPDATED, { conversationId, message: updatedMessage });

      this.logger.debug(`消息更新成功: ${messageId}`);
      return updatedMessage;
    } catch (error) {
      this.logger.error(`更新消息失败: ${conversationId}/${messageId}`, error);
      throw error;
    }
  }

  /**
   * 从会话中删除消息
   */
  async deleteMessageFromConversation(conversationId: string, messageId: string): Promise<void> {
    try {
      this.logger.debug(`删除会话消息: ${conversationId}/${messageId}`);

      const conversation = await storageService.conversations.getConversation(conversationId);
      if (!conversation) {
        throw new Error(`会话不存在: ${conversationId}`);
      }

      const updatedMessages = conversation.messages.filter(m => m.id !== messageId);

      if (updatedMessages.length === conversation.messages.length) {
        throw new Error(`消息不存在: ${messageId}`);
      }

      // 更新会话
      await this.updateConversation(conversationId, {
        messages: updatedMessages
      });

      this.messageBus.emit(EVENTS.MESSAGE_DELETED, { conversationId, messageId });

      this.logger.debug(`消息删除成功: ${messageId}`);
    } catch (error) {
      this.logger.error(`删除消息失败: ${conversationId}/${messageId}`, error);
      throw error;
    }
  }

  /**
   * 获取最近的会话列表
   */
  async getRecentConversations(limit: number = 50): Promise<ConversationData[]> {
    try {
      this.logger.debug(`获取最近 ${limit} 个会话`);
      const conversations = await storageService.getAllConversations();

      // 按最后更新时间排序
      const sortedConversations = conversations
        .filter(conv => !conv.archived) // 排除已归档的会话
        .sort((a, b) => {
          const timeA = a.updatedAt || a.createdAt;
          const timeB = b.updatedAt || b.createdAt;
          return timeB.getTime() - timeA.getTime();
        })
        .slice(0, limit);

      this.logger.debug(`返回 ${sortedConversations.length} 个最近会话`);
      return sortedConversations;
    } catch (error) {
      this.logger.error('获取最近会话失败', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * 搜索会话
   */
  async searchConversations(query: string, limit: number = 50): Promise<ConversationData[]> {
    try {
      this.logger.debug(`搜索会话: "${query}"`);

      if (!query.trim()) {
        return this.getRecentConversations(limit);
      }

      const conversations = await storageService.getAllConversations();
      const searchTerm = query.toLowerCase().trim();

      // 搜索匹配的会话
      const matchedConversations = conversations.filter(conv => {
        // 搜索标题
        if (conv.title?.toLowerCase().includes(searchTerm)) {
          return true;
        }

        // 搜索标签
        if (conv.tags?.some(tag => tag.toLowerCase().includes(searchTerm))) {
          return true;
        }

        // 搜索平台
        if (conv.platform?.toLowerCase().includes(searchTerm)) {
          return true;
        }

        // 搜索消息内容
        if (conv.messages?.some(msg =>
          msg.content?.toLowerCase().includes(searchTerm)
        )) {
          return true;
        }

        return false;
      });

      // 按相关性和时间排序
      const sortedResults = matchedConversations
        .sort((a, b) => {
          const timeA = a.updatedAt || a.createdAt;
          const timeB = b.updatedAt || b.createdAt;
          return timeB.getTime() - timeA.getTime();
        })
        .slice(0, limit);

      this.logger.debug(`搜索到 ${sortedResults.length} 个匹配会话`);
      return sortedResults;
    } catch (error) {
      this.logger.error('搜索会话失败', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * 获取会话统计信息
   */
  async getConversationStats(id: string): Promise<{
    messageCount: number;
    userMessageCount: number;
    assistantMessageCount: number;
    averageMessageLength: number;
    totalCharacters: number;
    firstMessageTime: Date | null;
    lastMessageTime: Date | null;
    conversationDuration: number; // 分钟
  }> {
    try {
      const conversation = await storageService.conversations.getConversation(id);
      if (!conversation) {
        throw new Error(`会话不存在: ${id}`);
      }

      const messages = conversation.messages;
      const userMessages = messages.filter(m => m.type === 'user');
      const assistantMessages = messages.filter(m => m.type === 'assistant');

      const totalCharacters = messages.reduce((sum, m) => sum + m.content.length, 0);
      const averageMessageLength = messages.length > 0 ? totalCharacters / messages.length : 0;

      const timestamps = messages.map(m => m.timestamp.getTime()).sort((a, b) => a - b);
      const firstMessageTime = timestamps.length > 0 ? new Date(timestamps[0]) : null;
      const lastMessageTime = timestamps.length > 0 ? new Date(timestamps[timestamps.length - 1]) : null;

      const conversationDuration = firstMessageTime && lastMessageTime
        ? (lastMessageTime.getTime() - firstMessageTime.getTime()) / (1000 * 60)
        : 0;

      return {
        messageCount: messages.length,
        userMessageCount: userMessages.length,
        assistantMessageCount: assistantMessages.length,
        averageMessageLength,
        totalCharacters,
        firstMessageTime,
        lastMessageTime,
        conversationDuration
      };
    } catch (error) {
      this.logger.error(`获取会话统计失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 执行启动时的维护任务
   */
  private async performStartupMaintenance(): Promise<void> {
    try {
      this.logger.info('执行启动维护任务...');

      // 清理过期的缓存
      this.duplicateCheckCache.clear();

      // 检查数据完整性
      await this.validateDataIntegrity();

      // 更新搜索索引
      await this.updateSearchIndexes();

      this.logger.info('启动维护任务完成');
    } catch (error) {
      this.logger.error('启动维护任务失败:', error);
    }
  }

  /**
   * 验证数据完整性
   */
  private async validateDataIntegrity(): Promise<void> {
    try {
      this.logger.debug('验证数据完整性...');

      const result = await storageService.conversations.getConversations();
      const conversations = result.conversations;

      let fixedCount = 0;

      for (const conversation of conversations) {
        let needsUpdate = false;
        const updates: Partial<ConversationData> = {};

        // 检查消息计数
        if (conversation.metadata.messageCount !== conversation.messages.length) {
          updates.metadata = {
            ...conversation.metadata,
            messageCount: conversation.messages.length
          };
          needsUpdate = true;
        }

        // 检查最后活动时间
        if (conversation.messages.length > 0) {
          const lastMessageTime = Math.max(...conversation.messages.map(m => m.timestamp.getTime()));
          const lastActivity = new Date(lastMessageTime);

          if (!conversation.metadata.lastActivity ||
              conversation.metadata.lastActivity.getTime() !== lastActivity.getTime()) {
            updates.metadata = {
              ...updates.metadata,
              ...conversation.metadata,
              lastActivity
            };
            needsUpdate = true;
          }
        }

        if (needsUpdate) {
          await this.updateConversation(conversation.id, updates);
          fixedCount++;
        }
      }

      if (fixedCount > 0) {
        this.logger.info(`数据完整性验证完成，修复了 ${fixedCount} 个会话`);
      } else {
        this.logger.debug('数据完整性验证完成，无需修复');
      }
    } catch (error) {
      this.logger.error('数据完整性验证失败:', error);
    }
  }

  /**
   * 更新搜索索引
   */
  private async updateSearchIndexes(): Promise<void> {
    try {
      this.logger.debug('更新搜索索引...');

      const result = await storageService.conversations.getConversations();
      const conversations = result.conversations;

      for (const conversation of conversations) {
        await storageService.search.indexConversation(conversation);
      }

      this.logger.debug(`搜索索引更新完成，处理了 ${conversations.length} 个会话`);
    } catch (error) {
      this.logger.error('更新搜索索引失败:', error);
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听会话创建事件
    this.messageBus.on(EVENTS.CONVERSATION_CREATED, (conversation: ConversationData) => {
      // 清理相关缓存
      this.duplicateCheckCache.clear();
    });

    // 监听会话更新事件
    this.messageBus.on(EVENTS.CONVERSATION_UPDATED, (conversation: ConversationData) => {
      // 清理相关缓存
      this.duplicateCheckCache.delete(conversation.id);
    });
  }

  /**
   * 计算会话相似度
   */
  private calculateSimilarity(
    conv1: ConversationData,
    conv2: ConversationData,
    options: ConversationDuplicateOptions
  ): number {
    let totalWeight = 0;
    let totalScore = 0;

    // 标题相似度
    if (options.checkTitle) {
      const titleSimilarity = this.calculateTextSimilarity(conv1.title, conv2.title);
      totalScore += titleSimilarity * 0.4;
      totalWeight += 0.4;
    }

    // 内容相似度
    if (options.checkContent && conv1.messages.length > 0 && conv2.messages.length > 0) {
      const content1 = conv1.messages.map(m => m.content).join(' ');
      const content2 = conv2.messages.map(m => m.content).join(' ');
      const contentSimilarity = this.calculateTextSimilarity(content1, content2);
      totalScore += contentSimilarity * 0.6;
      totalWeight += 0.6;
    }

    return totalWeight > 0 ? totalScore / totalWeight : 0;
  }

  /**
   * 计算文本相似度
   */
  private calculateTextSimilarity(text1: string, text2: string): number {
    if (!text1 || !text2) return 0;

    const words1 = new Set(text1.toLowerCase().split(/\s+/));
    const words2 = new Set(text2.toLowerCase().split(/\s+/));

    const intersection = new Set([...words1].filter(word => words2.has(word)));
    const union = new Set([...words1, ...words2]);

    return union.size > 0 ? intersection.size / union.size : 0;
  }

  /**
   * 生成会话ID
   */
  private generateConversationId(): string {
    return `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取重复检查缓存键
   */
  private getDuplicateCacheKey(conversation: ConversationData, options: ConversationDuplicateOptions): string {
    return `${conversation.platform}_${conversation.title}_${options.similarity}_${options.checkTimeRange}`;
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.duplicateCheckCache.clear();
    this.logger.info('会话管理器资源已清理');
  }

  /**
   * 导出会话数据
   */
  async exportConversations(format: 'json' | 'csv' = 'json'): Promise<any> {
    try {
      this.logger.info(`导出会话数据，格式: ${format}`);
      const conversations = await storageService.getAllConversations();

      if (format === 'json') {
        return {
          exportTime: new Date().toISOString(),
          version: '1.0',
          totalConversations: conversations.length,
          conversations: conversations
        };
      } else if (format === 'csv') {
        // 简化的CSV格式
        const csvData = conversations.map(conv => ({
          id: conv.id,
          title: conv.title || '',
          platform: conv.platform || '',
          createdAt: conv.createdAt.toISOString(),
          updatedAt: (conv.updatedAt || conv.createdAt).toISOString(),
          messageCount: conv.messages?.length || 0,
          tags: (conv.tags || []).join(';'),
          url: conv.url || ''
        }));
        return csvData;
      }

      throw new Error(`不支持的导出格式: ${format}`);
    } catch (error) {
      this.logger.error('导出会话数据失败', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * 获取会话总数
   */
  async getConversationCount(): Promise<number> {
    try {
      const conversations = await storageService.getAllConversations();
      return conversations.length;
    } catch (error) {
      this.logger.error('获取会话总数失败', error instanceof Error ? error : new Error(String(error)));
      return 0;
    }
  }

  /**
   * 获取最近活动统计
   */
  async getRecentActivity(days: number = 7): Promise<Array<{ date: string; count: number }>> {
    try {
      const conversations = await storageService.getAllConversations();
      const now = new Date();
      const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

      // 按日期分组统计
      const activityMap = new Map<string, number>();

      conversations.forEach(conv => {
        const createdDate = conv.createdAt;
        if (createdDate >= startDate) {
          const dateKey = createdDate.toISOString().split('T')[0];
          activityMap.set(dateKey, (activityMap.get(dateKey) || 0) + 1);
        }
      });

      // 生成完整的日期序列
      const activity: Array<{ date: string; count: number }> = [];
      for (let i = 0; i < days; i++) {
        const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
        const dateKey = date.toISOString().split('T')[0];
        activity.unshift({
          date: dateKey,
          count: activityMap.get(dateKey) || 0
        });
      }

      return activity;
    } catch (error) {
      this.logger.error('获取最近活动统计失败', error instanceof Error ? error : new Error(String(error)));
      return [];
    }
  }
}
