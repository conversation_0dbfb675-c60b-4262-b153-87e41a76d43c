/**
 * <PERSON> 平台适配器
 * 专门处理 claude.ai 的页面结构和内容提取
 */

import { BasePlatformAdapter, PlatformSelectors } from './base';
import { ConversationData, MessageData } from '@types/conversation';

export class ClaudeAdapter extends BasePlatformAdapter {
  readonly platform = 'Claude';
  readonly selectors: PlatformSelectors = {
    // 基于Claude.ai常见结构模式优化 (2025-08-01)
    conversationContainer: '[role="main"], main, .conversation, [data-testid*="conversation"], .chat-container',
    messageElements: '[data-testid*="message"], .message, .conversation-turn, [role="article"], article',
    userMessage: '[data-is-author="true"], .message.user, .human-message, [data-author="user"], .user-message',
    assistantMessage: '[data-is-author="false"], .message.assistant, .ai-message, [data-author="assistant"], .assistant-message',
    messageContent: '.message-content, .prose, .markdown, .text-content, .whitespace-pre-wrap, p, div[class*="content"]',
    messageInput: 'textarea[placeholder*="Talk"], textarea[placeholder*="Message"], textarea[data-testid*="input"], .chat-input textarea, textarea',
    sendButton: '[data-testid*="send"], .send-button, button[type="submit"], button[aria-label*="Send"]',
    conversationTitle: '.conversation-title, h1, .chat-title, .title',
    conversationList: '.conversation-list li, .chat-list-item, nav li, .sidebar li'
  };

  protected features = {
    hasConversationList: true,
    hasMessageTimestamps: true,
    hasCodeBlocks: true,
    hasImageSupport: true,
    hasFileUpload: true,
    hasConversationExport: true
  };

  isPageReady(): boolean {
    // 检查是否在Claude域名
    if (!this.isCurrentPlatform()) {
      return false;
    }

    // 检查是否在会话页面
    const hasConversationContainer = document.querySelector(this.selectors.conversationContainer) !== null;
    const hasMessageInput = document.querySelector(this.selectors.messageInput) !== null;
    const isConversationUrl = window.location.pathname.includes('/chat') || 
                             window.location.pathname === '/' ||
                             window.location.pathname.startsWith('/conversation/');
    
    // 等待页面完全加载
    const isPageLoaded = document.readyState === 'complete' || 
                        document.readyState === 'interactive';
    
    this.logger.debug('Claude页面检查状态:', {
      hasConversationContainer,
      hasMessageInput,
      isConversationUrl,
      isPageLoaded,
      pathname: window.location.pathname
    });
    
    return hasConversationContainer && hasMessageInput && isConversationUrl && isPageLoaded;
  }

  extractConversation(): ConversationData | null {
    if (!this.isPageReady()) {
      this.logger.debug('Claude页面未准备就绪，跳过提取');
      return null;
    }

    const messages = this.extractMessages();
    
    if (messages.length === 0) {
      this.logger.debug('Claude未找到消息，跳过提取');
      return null;
    }

    const title = this.extractTitle();
    const url = window.location.href;
    const conversationId = this.generateConversationId(url);

    return {
      id: conversationId,
      platform: this.platform,
      url,
      title,
      messages,
      tags: [this.platform.toLowerCase()],
      notes: '',
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        messageCount: messages.length,
        lastActivity: new Date(),
        isArchived: false,
        platformSpecific: {
          hasCodeBlocks: this.hasCodeBlocks(messages),
          hasImages: this.hasImages(messages),
          conversationType: this.detectConversationType(messages),
          hasArtifacts: this.hasArtifacts(messages)
        }
      }
    };
  }

  private extractMessages(): MessageData[] {
    const messages: MessageData[] = [];
    
    // Claude的消息选择器
    let messageElements = document.querySelectorAll(this.selectors.messageElements);
    
    // 如果没有找到，尝试备用选择器
    if (messageElements.length === 0) {
      const alternativeSelectors = [
        '[data-testid*="message"]',
        '.conversation-turn',
        '.message-container',
        '.chat-message',
        '.prose'
      ];
      
      for (const selector of alternativeSelectors) {
        messageElements = document.querySelectorAll(selector);
        if (messageElements.length > 0) {
          this.logger.debug(`Claude使用备用选择器找到消息: ${selector}`);
          break;
        }
      }
    }

    this.logger.debug(`Claude找到 ${messageElements.length} 个消息元素`);

    messageElements.forEach((element, index) => {
      const messageData = this.extractSingleMessage(element, index);
      if (messageData) {
        messages.push(messageData);
      }
    });

    return this.sortMessagesByDOMOrder(messages);
  }

  private extractSingleMessage(element: Element, index: number): MessageData | null {
    const role = this.detectMessageRole(element);
    if (!role) {
      return null;
    }

    const content = this.extractMessageContent(element);
    if (!content.trim()) {
      this.logger.debug(`Claude消息 ${index} 内容为空，跳过`);
      return null;
    }

    const timestamp = this.extractMessageTimestamp(element) || new Date();

    return {
      id: `${role}_${index}_${Date.now()}`,
      conversationId: '',
      type: role as 'user' | 'assistant',
      content,
      timestamp,
      metadata: {
        platform: this.platform,
        originalElement: element.outerHTML.slice(0, 1000),
        elementIndex: index,
        hasCodeBlocks: this.elementHasCodeBlocks(element),
        hasImages: this.elementHasImages(element),
        hasArtifacts: this.elementHasArtifacts(element),
        wordCount: content.split(/\s+/).length,
        characterCount: content.length
      }
    };
  }

  private detectMessageRole(element: Element): string | null {
    // 方法1: 检查data属性
    const isAuthor = element.getAttribute('data-is-author');
    if (isAuthor === 'true') return 'user';
    if (isAuthor === 'false') return 'assistant';

    // 方法2: 检查类名
    if (element.classList.contains('user') || element.classList.contains('human-message')) {
      return 'user';
    }
    if (element.classList.contains('assistant') || element.classList.contains('ai-message')) {
      return 'assistant';
    }

    // 方法3: 检查子元素
    if (element.querySelector('.user-message, .human-message')) {
      return 'user';
    }
    if (element.querySelector('.assistant-message, .ai-message')) {
      return 'assistant';
    }

    // 方法4: 通过头像或标识符
    const avatar = element.querySelector('img, .avatar');
    if (avatar) {
      const alt = avatar.getAttribute('alt') || '';
      const src = avatar.getAttribute('src') || '';
      if (alt.includes('User') || alt.includes('Human') || src.includes('user')) {
        return 'user';
      }
      if (alt.includes('Claude') || alt.includes('Assistant') || src.includes('claude')) {
        return 'assistant';
      }
    }

    // 方法5: 通过文本内容和位置推断
    const textContent = element.textContent?.trim() || '';
    if (textContent.length > 0) {
      // Claude的助手消息通常更长且包含更多结构化内容
      if (element.querySelector('pre, code, .artifact')) {
        return 'assistant';
      }
      
      // 检查是否有典型的用户问题特征
      if (textContent.endsWith('?') || textContent.startsWith('请') || textContent.startsWith('帮我')) {
        return 'user';
      }
    }

    this.logger.debug('Claude无法确定消息角色:', element);
    return null;
  }

  private extractMessageTimestamp(element: Element): Date | null {
    const timeSelectors = [
      'time',
      '[datetime]',
      '.timestamp',
      '.message-time',
      '[data-testid="timestamp"]'
    ];

    for (const selector of timeSelectors) {
      const timeElement = element.querySelector(selector);
      if (timeElement) {
        const datetime = timeElement.getAttribute('datetime') || 
                        timeElement.getAttribute('title') ||
                        timeElement.textContent;
        
        if (datetime) {
          const date = new Date(datetime);
          if (!isNaN(date.getTime())) {
            return date;
          }
        }
      }
    }

    return null;
  }

  protected extractTitle(): string {
    // 尝试从页面标题获取
    const titleElement = document.querySelector(this.selectors.conversationTitle);
    if (titleElement) {
      const title = titleElement.textContent?.trim();
      if (title && title !== 'Claude' && title !== 'New conversation') {
        return title;
      }
    }

    // 从第一条用户消息生成标题
    const firstUserMessage = document.querySelector(this.selectors.userMessage);
    if (firstUserMessage) {
      const content = this.extractMessageContent(firstUserMessage);
      if (content) {
        return content.slice(0, 50) + (content.length > 50 ? '...' : '');
      }
    }

    return `Claude 会话 - ${new Date().toLocaleDateString()}`;
  }

  protected generateConversationId(url: string): string {
    // Claude URL 格式: https://claude.ai/chat/conversation-id
    const match = url.match(/\/chat\/([a-zA-Z0-9-]+)/);
    if (match) {
      return match[1];
    }

    // 检查其他可能的URL格式
    const conversationMatch = url.match(/\/conversation\/([a-zA-Z0-9-]+)/);
    if (conversationMatch) {
      return conversationMatch[1];
    }

    // 如果是新会话页面，生成临时ID
    if (url.includes('claude.ai')) {
      return `claude_temp_${Date.now()}`;
    }

    return super.generateConversationId(url);
  }

  // Claude特有的方法
  private elementHasArtifacts(element: Element): boolean {
    return element.querySelectorAll('.artifact, [data-testid="artifact"]').length > 0;
  }

  private hasArtifacts(messages: MessageData[]): boolean {
    return messages.some(msg => msg.metadata?.hasArtifacts);
  }

  private hasCodeBlocks(messages: MessageData[]): boolean {
    return messages.some(msg => msg.metadata?.hasCodeBlocks);
  }

  private hasImages(messages: MessageData[]): boolean {
    return messages.some(msg => msg.metadata?.hasImages);
  }

  private detectConversationType(messages: MessageData[]): string {
    const content = messages.map(m => m.content).join(' ').toLowerCase();
    
    if (content.includes('代码') || content.includes('code') || content.includes('编程')) {
      return 'coding';
    }
    if (content.includes('翻译') || content.includes('translate')) {
      return 'translation';
    }
    if (content.includes('写作') || content.includes('文章') || content.includes('writing')) {
      return 'writing';
    }
    if (content.includes('分析') || content.includes('analysis')) {
      return 'analysis';
    }
    
    return 'general';
  }
}
