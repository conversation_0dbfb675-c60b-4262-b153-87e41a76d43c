/**
 * 搜索引擎单元测试
 */

import { SearchEngine } from '../../../src/core/search/search-engine';
import { ConversationData, MessageData } from '../../../src/types/conversation';

describe('SearchEngine', () => {
  let searchEngine: SearchEngine;
  let mockConversations: ConversationData[];

  beforeEach(() => {
    searchEngine = new SearchEngine();
    
    // 创建测试数据
    mockConversations = [
      {
        id: 'conv_001',
        platform: 'ChatGPT',
        title: 'JavaScript异步编程讨论',
        url: 'https://chat.openai.com/c/123',
        timestamp: new Date('2024-01-15T10:30:00Z'),
        messages: [
          {
            id: 'msg_001',
            role: 'user',
            content: '请解释一下JavaScript中的Promise和async/await的区别',
            timestamp: new Date('2024-01-15T10:30:00Z')
          },
          {
            id: 'msg_002',
            role: 'assistant',
            content: 'Promise和async/await都是处理异步操作的方式。Promise是ES6引入的，提供了更好的异步编程体验。async/await是ES2017引入的语法糖，让异步代码看起来更像同步代码。',
            timestamp: new Date('2024-01-15T10:31:00Z')
          }
        ],
        metadata: {
          tags: ['JavaScript', '异步编程', 'Promise']
        }
      },
      {
        id: 'conv_002',
        platform: 'Claude',
        title: 'React性能优化技巧',
        url: 'https://claude.ai/chat/456',
        timestamp: new Date('2024-01-16T14:20:00Z'),
        messages: [
          {
            id: 'msg_003',
            role: 'user',
            content: '如何优化React应用的性能？',
            timestamp: new Date('2024-01-16T14:20:00Z')
          },
          {
            id: 'msg_004',
            role: 'assistant',
            content: 'React性能优化有多种方法：1. 使用React.memo避免不必要的重渲染 2. 使用useMemo和useCallback缓存计算结果 3. 代码分割和懒加载',
            timestamp: new Date('2024-01-16T14:21:00Z')
          }
        ],
        metadata: {
          tags: ['React', '性能优化', '前端开发']
        }
      },
      {
        id: 'conv_003',
        platform: 'Gemini',
        title: 'Python数据分析入门',
        url: 'https://gemini.google.com/chat/789',
        timestamp: new Date('2024-01-17T09:15:00Z'),
        messages: [
          {
            id: 'msg_005',
            role: 'user',
            content: 'Python中pandas库的基本用法是什么？',
            timestamp: new Date('2024-01-17T09:15:00Z')
          },
          {
            id: 'msg_006',
            role: 'assistant',
            content: 'pandas是Python中最重要的数据分析库。主要提供DataFrame和Series两种数据结构，支持数据读取、清洗、转换和分析。',
            timestamp: new Date('2024-01-17T09:16:00Z')
          }
        ],
        metadata: {
          tags: ['Python', '数据分析', 'pandas']
        }
      }
    ];
  });

  afterEach(() => {
    searchEngine.clearCache();
  });

  describe('索引构建', () => {
    test('应该能够构建对话索引', async () => {
      await searchEngine.buildIndex(mockConversations);
      
      const stats = searchEngine.getSearchStats();
      expect(stats.totalIndices).toBeGreaterThan(0);
      expect(stats.conversationIndices).toBe(3);
      expect(stats.messageIndices).toBe(6);
    });

    test('应该能够增量更新索引', async () => {
      // 先构建初始索引
      await searchEngine.buildIndex(mockConversations.slice(0, 2));
      
      let stats = searchEngine.getSearchStats();
      expect(stats.conversationIndices).toBe(2);
      expect(stats.messageIndices).toBe(4);

      // 增量更新
      await searchEngine.updateIndex(mockConversations[2]);
      
      stats = searchEngine.getSearchStats();
      expect(stats.conversationIndices).toBe(3);
      expect(stats.messageIndices).toBe(6);
    });

    test('应该能够删除索引', async () => {
      await searchEngine.buildIndex(mockConversations);
      
      searchEngine.removeIndex('conv_001');
      
      const stats = searchEngine.getSearchStats();
      expect(stats.conversationIndices).toBe(2);
      expect(stats.messageIndices).toBe(4);
    });
  });

  describe('文本处理', () => {
    test('应该正确标准化文本', () => {
      const engine = searchEngine as any;
      
      expect(engine.normalizeText('Hello World!')).toBe('hello world');
      expect(engine.normalizeText('JavaScript编程')).toBe('javascript编程');
      expect(engine.normalizeText('  多个  空格  ')).toBe('多个 空格');
    });

    test('应该正确分词', () => {
      const engine = searchEngine as any;
      
      // 英文分词
      const englishTokens = engine.tokenize('Hello World JavaScript');
      expect(englishTokens).toEqual(['hello', 'world', 'javascript']);

      // 中文分词（简单实现）
      const chineseTokens = engine.tokenize('JavaScript异步编程');
      expect(chineseTokens).toContain('javascript');
      expect(chineseTokens).toContain('异步');
      expect(chineseTokens).toContain('编程');

      // 混合分词
      const mixedTokens = engine.tokenize('React性能优化');
      expect(mixedTokens).toContain('react');
      expect(mixedTokens).toContain('性能');
      expect(mixedTokens).toContain('优化');
    });

    test('应该过滤停用词', () => {
      const engine = searchEngine as any;
      
      const tokens = engine.tokenize('这是一个测试的例子');
      expect(tokens).not.toContain('这');
      expect(tokens).not.toContain('是');
      expect(tokens).not.toContain('一个');
      expect(tokens).not.toContain('的');
      expect(tokens).toContain('测试');
      expect(tokens).toContain('例子');
    });
  });

  describe('搜索功能', () => {
    beforeEach(async () => {
      await searchEngine.buildIndex(mockConversations);
    });

    test('应该能够搜索标题', async () => {
      const result = await searchEngine.search('JavaScript');
      
      expect(result.results).toHaveLength(1);
      expect(result.results[0].conversation.title).toContain('JavaScript');
      expect(result.results[0].matchType).toBe('title');
    });

    test('应该能够搜索内容', async () => {
      const result = await searchEngine.search('Promise');
      
      expect(result.results).toHaveLength(1);
      expect(result.results[0].conversation.id).toBe('conv_001');
      expect(result.results[0].matchType).toBe('content');
    });

    test('应该能够搜索标签', async () => {
      const result = await searchEngine.search('React');
      
      expect(result.results).toHaveLength(1);
      expect(result.results[0].conversation.metadata?.tags).toContain('React');
      expect(result.results[0].matchType).toBe('tag');
    });

    test('应该能够搜索平台', async () => {
      const result = await searchEngine.search('ChatGPT');
      
      expect(result.results).toHaveLength(1);
      expect(result.results[0].conversation.platform).toBe('ChatGPT');
      expect(result.results[0].matchType).toBe('metadata');
    });

    test('应该支持多关键词搜索', async () => {
      const result = await searchEngine.search('JavaScript Promise');
      
      expect(result.results).toHaveLength(1);
      expect(result.results[0].conversation.id).toBe('conv_001');
    });

    test('应该支持中文搜索', async () => {
      const result = await searchEngine.search('异步编程');
      
      expect(result.results).toHaveLength(1);
      expect(result.results[0].conversation.title).toContain('异步编程');
    });

    test('应该返回相关度评分', async () => {
      const result = await searchEngine.search('JavaScript');
      
      expect(result.results[0].score).toBeGreaterThan(0);
      expect(result.results[0].score).toBeLessThanOrEqual(1);
    });

    test('应该生成高亮片段', async () => {
      const result = await searchEngine.search('Promise');
      
      expect(result.results[0].highlights).toHaveLength(1);
      expect(result.results[0].highlights[0]).toContain('<mark>');
    });

    test('应该支持搜索选项', async () => {
      const result = await searchEngine.search('性能', {
        limit: 1,
        offset: 0,
        sortBy: 'relevance',
        sortOrder: 'desc'
      });
      
      expect(result.results).toHaveLength(1);
      expect(result.pagination.limit).toBe(1);
      expect(result.pagination.offset).toBe(0);
    });

    test('应该支持过滤选项', async () => {
      const result = await searchEngine.search('优化', {
        filters: {
          platforms: ['Claude'],
          dateRange: {
            start: new Date('2024-01-16T00:00:00Z'),
            end: new Date('2024-01-17T00:00:00Z')
          }
        }
      });
      
      expect(result.results).toHaveLength(1);
      expect(result.results[0].conversation.platform).toBe('Claude');
    });

    test('空搜索应该返回空结果', async () => {
      const result = await searchEngine.search('');
      
      expect(result.results).toHaveLength(0);
      expect(result.totalCount).toBe(0);
    });

    test('无匹配搜索应该返回空结果', async () => {
      const result = await searchEngine.search('不存在的关键词xyz123');
      
      expect(result.results).toHaveLength(0);
      expect(result.totalCount).toBe(0);
    });
  });

  describe('排序功能', () => {
    beforeEach(async () => {
      await searchEngine.buildIndex(mockConversations);
    });

    test('应该支持按相关度排序', async () => {
      const result = await searchEngine.search('优化', {
        sortBy: 'relevance',
        sortOrder: 'desc'
      });
      
      if (result.results.length > 1) {
        expect(result.results[0].score).toBeGreaterThanOrEqual(result.results[1].score);
      }
    });

    test('应该支持按日期排序', async () => {
      const result = await searchEngine.search('编程', {
        sortBy: 'date',
        sortOrder: 'desc'
      });
      
      if (result.results.length > 1) {
        const date1 = new Date(result.results[0].conversation.timestamp);
        const date2 = new Date(result.results[1].conversation.timestamp);
        expect(date1.getTime()).toBeGreaterThanOrEqual(date2.getTime());
      }
    });

    test('应该支持按标题排序', async () => {
      const result = await searchEngine.search('', {
        sortBy: 'title',
        sortOrder: 'asc'
      });
      
      if (result.results.length > 1) {
        expect(result.results[0].conversation.title.localeCompare(result.results[1].conversation.title)).toBeLessThanOrEqual(0);
      }
    });
  });

  describe('缓存功能', () => {
    test('应该缓存分词结果', async () => {
      await searchEngine.buildIndex(mockConversations);
      
      const engine = searchEngine as any;
      const text = 'JavaScript编程';
      
      // 第一次分词
      const tokens1 = engine.tokenize(text);
      expect(engine.tokenCache.has(text)).toBe(true);
      
      // 第二次分词应该使用缓存
      const tokens2 = engine.tokenize(text);
      expect(tokens1).toEqual(tokens2);
    });

    test('应该能够清理缓存', async () => {
      await searchEngine.buildIndex(mockConversations);
      
      const engine = searchEngine as any;
      engine.tokenize('测试文本');
      expect(engine.tokenCache.size).toBeGreaterThan(0);
      
      searchEngine.clearCache();
      expect(engine.tokenCache.size).toBe(0);
    });
  });

  describe('统计信息', () => {
    test('应该返回正确的统计信息', async () => {
      await searchEngine.buildIndex(mockConversations);
      
      const stats = searchEngine.getSearchStats();
      
      expect(stats.totalIndices).toBe(9); // 3个对话 + 6个消息
      expect(stats.conversationIndices).toBe(3);
      expect(stats.messageIndices).toBe(6);
      expect(stats.cacheSize).toBeGreaterThanOrEqual(0);
    });
  });
});
