# AI Chat Memo 开发调试指南

## 开发环境设置

### 1. 安装依赖
```bash
npm install
```

### 2. 开发模式构建
```bash
# 启动监听模式，文件变更时自动重新构建
npm run dev
```

### 3. 生产构建
```bash
npm run build
```

## 浏览器插件调试

### Chrome 浏览器调试

1. **加载插件**
   - 打开 Chrome 浏览器
   - 访问 `chrome://extensions/`
   - 开启"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择项目的 `dist` 文件夹

2. **调试 Background Script**
   - 在扩展程序页面找到插件
   - 点击"检查视图"中的"背景页"
   - 打开开发者工具进行调试

3. **调试 Content Script**
   - 在目标网页上按 F12 打开开发者工具
   - 在 Console 中可以看到 content script 的日志
   - 在 Sources 面板中可以找到注入的脚本

4. **调试 Popup**
   - 右键点击插件图标
   - 选择"检查弹出内容"
   - 在弹出的开发者工具中调试

5. **调试 Options 页面**
   - 在扩展程序页面点击"详细信息"
   - 点击"扩展程序选项"
   - 在选项页面按 F12 打开开发者工具

### Firefox 浏览器调试

1. **临时加载插件**
   - 访问 `about:debugging`
   - 点击"此 Firefox"
   - 点击"临时载入附加组件"
   - 选择 `dist/manifest.json` 文件

2. **调试方法**
   - 类似 Chrome，但界面略有不同
   - 使用 Firefox 的开发者工具进行调试

## 开发工具

### 代码检查
```bash
# 运行 ESLint 检查
npm run lint

# 自动修复 ESLint 问题
npm run lint:fix
```

### 代码格式化
```bash
# 格式化代码
npm run format

# 检查代码格式
npm run format:check
```

### 类型检查
```bash
# TypeScript 类型检查
npm run type-check
```

### 测试
```bash
# 运行测试
npm run test

# 监听模式运行测试
npm run test:watch

# 生成测试覆盖率报告
npm run test:coverage
```

## 调试技巧

### 1. 日志调试
- 使用项目中的 Logger 类进行日志输出
- 在开发模式下会显示详细的调试信息
- 生产模式下会过滤掉调试日志

### 2. 断点调试
- 在 Chrome DevTools 中设置断点
- 使用 `debugger;` 语句强制断点
- 利用 Sources 面板查看源码

### 3. 网络请求调试
- 在 Network 面板查看 API 请求
- 检查请求头、响应数据等

### 4. 存储调试
- 在 Application 面板查看 localStorage、sessionStorage
- 检查 IndexedDB 数据
- 查看 Chrome Extension Storage

### 5. 消息传递调试
- 在各个模块中添加消息监听日志
- 使用 Chrome DevTools 的 Console 查看消息流

## 常见问题

### 1. 插件无法加载
- 检查 manifest.json 语法是否正确
- 确认所有引用的文件是否存在
- 查看浏览器控制台的错误信息

### 2. Content Script 不工作
- 检查 manifest.json 中的 matches 配置
- 确认目标网站是否匹配
- 查看是否有 CSP 限制

### 3. 权限问题
- 检查 manifest.json 中的 permissions 配置
- 确认是否有足够的权限访问目标资源

### 4. 热重载不工作
- 确保运行了 `npm run dev` 命令
- 手动刷新插件（在扩展程序页面点击刷新按钮）
- 重新加载目标网页

## 发布准备

### 1. 构建生产版本
```bash
npm run build:prod
```

### 2. 打包插件
```bash
npm run zip
```

### 3. 测试
- 在多个浏览器中测试
- 测试所有支持的 AI 平台
- 进行性能测试

### 4. 代码质量检查
```bash
npm run lint
npm run format:check
npm run type-check
npm run test
```

## 目录结构说明

```
dist/                 # 构建输出目录
├── background.js     # 后台脚本
├── content.js        # 内容脚本
├── popup.js          # 弹窗脚本
├── popup.html        # 弹窗页面
├── options.js        # 选项脚本
├── options.html      # 选项页面
├── manifest.json     # 插件清单
└── chunks/           # 代码分块
```

## 性能优化建议

1. **减少包体积**
   - 使用 tree shaking 移除未使用的代码
   - 压缩图片和静态资源
   - 合理使用代码分割

2. **提升运行性能**
   - 避免在 content script 中执行重计算
   - 使用 requestIdleCallback 进行非关键任务
   - 合理使用缓存机制

3. **内存管理**
   - 及时清理事件监听器
   - 避免内存泄漏
   - 合理管理 DOM 引用
