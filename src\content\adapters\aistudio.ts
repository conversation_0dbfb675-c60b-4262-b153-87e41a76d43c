/**
 * AI Studio 平台适配器
 * 专门处理 aistudio.google.com 的页面结构和内容提取
 */

import { BasePlatformAdapter, PlatformSelectors } from './base';
import { ConversationData, MessageData } from '@types/conversation';

export class AistudioAdapter extends BasePlatformAdapter {
  readonly platform = 'Aistudio';
  readonly selectors: PlatformSelectors = {
    // 基于Google AI Studio结构模式 (2025-08-01)
    conversationContainer: '[role="main"], main, .conversation, [data-testid*="conversation"], .chat-container, .studio-chat',
    messageElements: '[data-testid*="message"], .message, .conversation-turn, [role="article"], article, .chat-message, .studio-message',
    userMessage: '[data-author="user"], .user-message, .prompt, .human-message, [data-role="user"], .user-input',
    assistantMessage: '[data-author="assistant"], .assistant-message, .response, .ai-message, [data-role="assistant"], .model-response, .studio-response',
    messageContent: '.message-content, .prose, .markdown, .text-content, .whitespace-pre-wrap, p, div[class*="content"], .response-text',
    messageInput: 'textarea[placeholder*="Enter"], textarea[placeholder*="Ask"], textarea[data-testid*="input"], .chat-input textarea, textarea, input[type="text"]',
    sendButton: '[data-testid*="send"], .send-button, button[type="submit"], button[aria-label*="Send"], button[title*="Send"]',
    conversationTitle: '.conversation-title, h1, .chat-title, .title, .studio-title',
    conversationList: '.conversation-list li, .chat-list-item, nav li, .sidebar li'
  };

  protected features = {
    hasConversationList: true,
    hasMessageTimestamps: false,
    hasCodeBlocks: true,
    hasImageSupport: true,
    hasFileUpload: true,
    hasConversationExport: false
  };

  isPageReady(): boolean {
    // 检查是否在AI Studio域名
    if (!this.isCurrentPlatform()) {
      return false;
    }

    // 检查是否在会话页面
    const hasConversationContainer = document.querySelector(this.selectors.conversationContainer) !== null;
    const hasMessageInput = document.querySelector(this.selectors.messageInput) !== null;
    const isConversationUrl = window.location.pathname.includes('/app') || 
                             window.location.pathname === '/' ||
                             window.location.pathname.includes('/chat') ||
                             window.location.pathname.includes('/studio');
    
    // 等待页面完全加载
    const isPageLoaded = document.readyState === 'complete' || 
                        document.readyState === 'interactive';
    
    this.logger.debug('AI Studio页面检查状态:', {
      hasConversationContainer,
      hasMessageInput,
      isConversationUrl,
      isPageLoaded,
      currentUrl: window.location.href
    });
    
    return hasConversationContainer && hasMessageInput && isConversationUrl && isPageLoaded;
  }

  extractConversation(): ConversationData | null {
    try {
      this.logger.debug('开始提取AI Studio对话内容');

      // 查找对话容器
      const conversationContainer = document.querySelector(this.selectors.conversationContainer);
      if (!conversationContainer) {
        this.logger.warn('未找到对话容器');
        return null;
      }

      // 提取消息
      const messages = this.extractMessages(conversationContainer);
      if (messages.length === 0) {
        this.logger.warn('未找到任何消息');
        return null;
      }

      // 提取会话标题
      const title = this.extractConversationTitle();

      // 生成会话ID
      const conversationId = this.generateConversationId();

      const conversation: ConversationData = {
        id: conversationId,
        platform: this.platform,
        title: title || '未命名对话',
        messages,
        url: window.location.href,
        timestamp: new Date().toISOString(),
        metadata: {
          userAgent: navigator.userAgent,
          pageTitle: document.title,
          extractedAt: new Date().toISOString(),
          messageCount: messages.length
        }
      };

      this.logger.info('AI Studio对话提取成功:', {
        conversationId,
        messageCount: messages.length,
        title
      });

      return conversation;

    } catch (error) {
      this.logger.error('AI Studio对话提取失败:', error);
      return null;
    }
  }

  protected determineMessageRole(element: Element): 'user' | 'assistant' | null {
    const className = element.className.toLowerCase();
    const textContent = element.textContent?.toLowerCase() || '';
    
    // 检查用户消息特征
    if (element.matches(this.selectors.userMessage)) {
      return 'user';
    }
    
    // 检查助手消息特征
    if (element.matches(this.selectors.assistantMessage)) {
      return 'assistant';
    }

    // 基于类名判断
    if (className.includes('user') || className.includes('human') || className.includes('prompt') || className.includes('input')) {
      return 'user';
    }
    
    if (className.includes('assistant') || className.includes('ai') || className.includes('model') || 
        className.includes('response') || className.includes('studio')) {
      return 'assistant';
    }

    // 基于数据属性判断
    const dataRole = element.getAttribute('data-role') || element.getAttribute('data-author');
    if (dataRole) {
      if (dataRole.includes('user') || dataRole.includes('human')) {
        return 'user';
      }
      if (dataRole.includes('assistant') || dataRole.includes('ai') || dataRole.includes('model')) {
        return 'assistant';
      }
    }

    // AI Studio特有的判断逻辑
    const parentElement = element.parentElement;
    if (parentElement) {
      const parentClass = parentElement.className.toLowerCase();
      if (parentClass.includes('user') || parentClass.includes('prompt')) {
        return 'user';
      }
      if (parentClass.includes('response') || parentClass.includes('assistant') || parentClass.includes('studio')) {
        return 'assistant';
      }
    }

    this.logger.debug('无法确定消息角色:', {
      className,
      tagName: element.tagName,
      textPreview: textContent.slice(0, 50)
    });

    return null;
  }

  protected extractConversationTitle(): string {
    try {
      const titleElement = document.querySelector(this.selectors.conversationTitle);
      if (titleElement) {
        const title = titleElement.textContent?.trim();
        if (title && title !== 'AI Studio' && title !== 'Google AI Studio') {
          return title;
        }
      }

      // 尝试从页面标题提取
      const pageTitle = document.title;
      if (pageTitle && pageTitle !== 'AI Studio' && pageTitle !== 'Google AI Studio') {
        return pageTitle.replace(' - AI Studio', '').replace(' - Google AI Studio', '').trim();
      }

      // 尝试从第一条用户消息生成标题
      const firstUserMessage = document.querySelector(this.selectors.userMessage);
      if (firstUserMessage) {
        const content = firstUserMessage.textContent?.trim();
        if (content) {
          return content.slice(0, 50) + (content.length > 50 ? '...' : '');
        }
      }

      return '新对话';

    } catch (error) {
      this.logger.error('提取对话标题失败:', error);
      return '未命名对话';
    }
  }

  protected extractMessagesFallback(container: Element): MessageData[] {
    const messages: MessageData[] = [];
    
    try {
      // AI Studio特有的备用提取策略
      const codeBlocks = container.querySelectorAll('pre, code');
      const textBlocks = container.querySelectorAll('div, p, article, section');
      
      // 合并所有可能的文本元素
      const allElements = [...Array.from(textBlocks), ...Array.from(codeBlocks)];
      
      allElements.forEach((element, index) => {
        const text = element.textContent?.trim();
        if (text && text.length > 10) {
          // 基于内容特征判断角色
          let role: 'user' | 'assistant' = 'assistant';
          
          // 如果包含问号或者是较短的文本，可能是用户消息
          if (text.includes('?') || text.length < 100) {
            role = 'user';
          }
          
          // 如果是代码块，通常是助手回复
          if (element.tagName.toLowerCase() === 'pre' || element.tagName.toLowerCase() === 'code') {
            role = 'assistant';
          }
          
          const message: MessageData = {
            id: this.generateMessageId(index, role),
            role,
            content: text,
            timestamp: new Date().toISOString(),
            metadata: {
              elementIndex: index,
              extractedFrom: 'fallback-aistudio',
              className: element.className,
              textLength: text.length,
              tagName: element.tagName.toLowerCase()
            }
          };
          
          messages.push(message);
        }
      });
      
    } catch (error) {
      this.logger.error('AI Studio备用消息提取失败:', error);
    }
    
    return messages;
  }
}
