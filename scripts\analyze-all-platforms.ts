/**
 * 所有AI平台页面结构分析脚本
 * 使用 Playwright 实际访问各个AI网站并分析页面结构
 */

import { PageStructureAnalyzer } from './page-analyzer/analyzer';

async function main() {
  console.log('🚀 开始分析所有AI平台页面结构...\n');

  const analyzer = new PageStructureAnalyzer();

  try {
    await analyzer.initialize();
    
    console.log('📋 将依次分析以下平台:');
    console.log('1. ChatGPT (chatgpt.com)');
    console.log('2. <PERSON> (claude.ai)');
    console.log('3. Gemini (gemini.google.com)');
    console.log();
    console.log('⚠️  注意事项:');
    console.log('- 某些平台可能需要登录，请在浏览器中完成登录后按回车继续');
    console.log('- 建议准备好各平台的登录账号以获得最佳分析结果');
    console.log('- 分析过程中会自动创建测试对话来获取完整页面结构');
    console.log('- 每个平台分析完成后会保存详细报告和截图');
    console.log();

    // 分析所有平台
    await analyzer.analyzeAllPlatforms();

    console.log('\n📊 所有平台分析完成！');
    console.log('📁 详细结果已保存到: analysis/results/');
    console.log('📸 页面截图已保存到: analysis/screenshots/');
    
    // 生成汇总报告
    await generateSummaryReport();

  } catch (error) {
    console.error('❌ 分析过程中出现错误:', error);
  } finally {
    await analyzer.cleanup();
  }
}

async function generateSummaryReport() {
  console.log('\n📋 生成汇总报告...');
  
  const fs = require('fs');
  const path = require('path');
  
  const resultsDir = 'analysis/results';
  const summaryFile = path.join(resultsDir, 'summary.md');
  
  if (!fs.existsSync(resultsDir)) {
    console.log('⚠️  未找到分析结果目录');
    return;
  }

  const platforms = ['chatgpt', 'claude', 'gemini'];
  let summary = '# AI平台页面结构分析汇总报告\n\n';
  summary += `生成时间: ${new Date().toLocaleString()}\n\n`;

  for (const platform of platforms) {
    const resultFile = path.join(resultsDir, `${platform}.json`);
    
    if (fs.existsSync(resultFile)) {
      try {
        const analysis = JSON.parse(fs.readFileSync(resultFile, 'utf-8'));
        
        summary += `## ${analysis.platform}\n\n`;
        summary += `- **URL**: ${analysis.url}\n`;
        summary += `- **分析时间**: ${new Date(analysis.timestamp).toLocaleString()}\n`;
        summary += `- **置信度**: ${analysis.recommendations.confidence}%\n`;
        summary += `- **消息容器**: ${analysis.messageContainers.length}个\n`;
        summary += `- **用户消息元素**: ${analysis.messageStructure.userElements.length}个\n`;
        summary += `- **助手消息元素**: ${analysis.messageStructure.assistantElements.length}个\n`;
        summary += `- **输入框**: ${analysis.inputElements.inputs.length}个\n\n`;
        
        summary += '### 推荐选择器\n\n';
        summary += `- **对话容器**: \`${analysis.recommendations.conversationContainer}\`\n`;
        summary += `- **用户消息**: \`${analysis.recommendations.userMessage}\`\n`;
        summary += `- **助手消息**: \`${analysis.recommendations.assistantMessage}\`\n`;
        summary += `- **消息输入**: \`${analysis.recommendations.messageInput}\`\n`;
        summary += `- **发送按钮**: \`${analysis.recommendations.sendButton}\`\n\n`;
        
        summary += '### 适配器代码片段\n\n';
        summary += '```typescript\n';
        summary += `export class ${analysis.platform}Adapter extends BasePlatformAdapter {\n`;
        summary += `  readonly platform = '${analysis.platform}';\n`;
        summary += `  readonly selectors: PlatformSelectors = {\n`;
        summary += `    conversationContainer: '${analysis.recommendations.conversationContainer}',\n`;
        summary += `    userMessage: '${analysis.recommendations.userMessage}',\n`;
        summary += `    assistantMessage: '${analysis.recommendations.assistantMessage}',\n`;
        summary += `    messageInput: '${analysis.recommendations.messageInput}',\n`;
        summary += `    sendButton: '${analysis.recommendations.sendButton}'\n`;
        summary += `  };\n`;
        summary += `  // 置信度: ${analysis.recommendations.confidence}%\n`;
        summary += `}\n`;
        summary += '```\n\n';
        
      } catch (error) {
        summary += `## ${platform.toUpperCase()}\n\n`;
        summary += `❌ 分析失败: ${error.message}\n\n`;
      }
    } else {
      summary += `## ${platform.toUpperCase()}\n\n`;
      summary += `⚠️  未找到分析结果文件\n\n`;
    }
  }

  summary += '## 使用说明\n\n';
  summary += '1. 这些适配器基于真实页面结构分析\n';
  summary += '2. 选择器已经过实际测试，具有较高的准确性\n';
  summary += '3. 可以直接集成到浏览器插件中使用\n';
  summary += '4. 建议根据实际使用情况调整选择器优先级\n';
  summary += '5. 添加适当的错误处理和回退机制\n\n';

  fs.writeFileSync(summaryFile, summary, 'utf-8');
  console.log(`📄 汇总报告已保存到: ${summaryFile}`);
}

// 处理程序退出
process.on('SIGINT', async () => {
  console.log('\n🛑 收到中断信号，正在清理...');
  process.exit(0);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

if (require.main === module) {
  main().catch(console.error);
}
