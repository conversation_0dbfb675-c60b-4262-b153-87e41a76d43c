<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI会话管理 - 移动端</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'chatgpt': '#10b981',
                        'claude': '#f59e0b', 
                        'gemini': '#3b82f6',
                        'aistudio': '#8b5cf6',
                        'monica': '#ec4899',
                        'poe': '#6366f1'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div class="px-4 py-3">
            <div class="flex items-center justify-between">
                <h1 class="text-lg font-semibold text-gray-900">AI会话</h1>
                <div class="flex items-center space-x-2">
                    <button class="p-2 text-gray-400 hover:text-gray-600 rounded-full" onclick="openSearch()">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                    <button class="p-2 text-gray-400 hover:text-gray-600 rounded-full" onclick="openMenu()">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 搜索栏 (隐藏状态) -->
    <div id="searchBar" class="bg-white border-b border-gray-200 px-4 py-3 hidden">
        <div class="flex items-center space-x-3">
            <button onclick="closeSearch()" class="text-gray-400">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <input type="text" 
                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm" 
                   placeholder="搜索会话内容...">
            <button class="text-blue-600 font-medium text-sm">搜索</button>
        </div>
    </div>

    <!-- 平台过滤标签 -->
    <div class="bg-white border-b border-gray-200 px-4 py-3">
        <div class="flex space-x-2 overflow-x-auto scrollbar-hide">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 whitespace-nowrap">
                全部
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700 whitespace-nowrap">
                ChatGPT
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700 whitespace-nowrap">
                Claude
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700 whitespace-nowrap">
                Gemini
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700 whitespace-nowrap">
                Aistudio
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700 whitespace-nowrap">
                Monica
            </span>
        </div>
    </div>

    <!-- 会话列表 -->
    <div class="px-4 py-4 space-y-3">
        <!-- 会话卡片 1 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4" onclick="openConversation('1')">
            <div class="flex justify-between items-start mb-3">
                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-chatgpt text-white">
                    ChatGPT
                </span>
                <button class="p-1 text-gray-400" onclick="event.stopPropagation(); showOptions('1')">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                    </svg>
                </button>
            </div>
            <h3 class="font-medium text-gray-900 mb-2 line-clamp-2">如何设计一个用户友好的界面？</h3>
            <p class="text-sm text-gray-600 mb-3 line-clamp-2">设计用户友好的界面需要考虑以下几个方面：1. 简洁明了的布局 2. 直观的导航结构...</p>
            <div class="flex justify-between items-center">
                <div class="flex space-x-1">
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">UI设计</span>
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">UX</span>
                </div>
                <span class="text-xs text-gray-500">2小时前</span>
            </div>
        </div>

        <!-- 会话卡片 2 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4" onclick="openConversation('2')">
            <div class="flex justify-between items-start mb-3">
                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-claude text-white">
                    Claude
                </span>
                <button class="p-1 text-gray-400" onclick="event.stopPropagation(); showOptions('2')">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                    </svg>
                </button>
            </div>
            <h3 class="font-medium text-gray-900 mb-2 line-clamp-2">JavaScript异步编程最佳实践</h3>
            <p class="text-sm text-gray-600 mb-3 line-clamp-2">JavaScript异步编程有几种主要方式：Promise、async/await、回调函数。推荐使用async/await...</p>
            <div class="flex justify-between items-center">
                <div class="flex space-x-1">
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800">JS</span>
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800">编程</span>
                </div>
                <span class="text-xs text-gray-500">1天前</span>
            </div>
        </div>

        <!-- 会话卡片 3 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4" onclick="openConversation('3')">
            <div class="flex justify-between items-start mb-3">
                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gemini text-white">
                    Gemini
                </span>
                <button class="p-1 text-gray-400" onclick="event.stopPropagation(); showOptions('3')">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                    </svg>
                </button>
            </div>
            <h3 class="font-medium text-gray-900 mb-2 line-clamp-2">机器学习模型部署策略</h3>
            <p class="text-sm text-gray-600 mb-3 line-clamp-2">机器学习模型部署需要考虑多个因素：模型大小、推理速度、资源消耗...</p>
            <div class="flex justify-between items-center">
                <div class="flex space-x-1">
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">ML</span>
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-indigo-100 text-indigo-800">部署</span>
                </div>
                <span class="text-xs text-gray-500">3天前</span>
            </div>
        </div>

        <!-- 加载更多 -->
        <div class="text-center py-4">
            <button class="text-blue-600 text-sm font-medium">加载更多</button>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
        <div class="flex justify-around">
            <button class="flex flex-col items-center py-2 text-blue-600">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                <span class="text-xs">会话</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400" onclick="showPage('export')">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span class="text-xs">导出</span>
            </button>
            <button class="flex flex-col items-center py-2 text-gray-400" onclick="showPage('settings')">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span class="text-xs">设置</span>
            </button>
        </div>
    </nav>

    <!-- 侧边菜单 -->
    <div id="sideMenu" class="fixed inset-0 z-50 hidden">
        <div class="absolute inset-0 bg-black bg-opacity-50" onclick="closeMenu()"></div>
        <div class="absolute right-0 top-0 h-full w-64 bg-white shadow-xl">
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">菜单</h3>
                    <button onclick="closeMenu()" class="text-gray-400">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="p-4 space-y-4">
                <button class="w-full text-left py-2 text-gray-700 hover:text-blue-600">批量管理</button>
                <button class="w-full text-left py-2 text-gray-700 hover:text-blue-600">导出数据</button>
                <button class="w-full text-left py-2 text-gray-700 hover:text-blue-600">清空数据</button>
                <button class="w-full text-left py-2 text-gray-700 hover:text-blue-600">关于插件</button>
            </div>
        </div>
    </div>

    <!-- 操作选项弹窗 -->
    <div id="optionsModal" class="fixed inset-0 z-50 hidden">
        <div class="absolute inset-0 bg-black bg-opacity-50" onclick="closeOptions()"></div>
        <div class="absolute bottom-0 left-0 right-0 bg-white rounded-t-lg">
            <div class="p-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">操作选项</h3>
            </div>
            <div class="p-4 space-y-3">
                <button class="w-full text-left py-3 text-gray-700 border-b border-gray-100" onclick="editConversation()">
                    编辑标签和备注
                </button>
                <button class="w-full text-left py-3 text-gray-700 border-b border-gray-100" onclick="shareConversation()">
                    分享会话
                </button>
                <button class="w-full text-left py-3 text-gray-700 border-b border-gray-100" onclick="exportConversation()">
                    导出会话
                </button>
                <button class="w-full text-left py-3 text-red-600" onclick="deleteConversation()">
                    删除会话
                </button>
            </div>
            <div class="p-4 border-t border-gray-200">
                <button class="w-full py-2 text-gray-500" onclick="closeOptions()">取消</button>
            </div>
        </div>
    </div>

    <script>
        function openSearch() {
            document.getElementById('searchBar').classList.remove('hidden');
        }

        function closeSearch() {
            document.getElementById('searchBar').classList.add('hidden');
        }

        function openMenu() {
            document.getElementById('sideMenu').classList.remove('hidden');
        }

        function closeMenu() {
            document.getElementById('sideMenu').classList.add('hidden');
        }

        function showOptions(id) {
            document.getElementById('optionsModal').classList.remove('hidden');
        }

        function closeOptions() {
            document.getElementById('optionsModal').classList.add('hidden');
        }

        function openConversation(id) {
            console.log('打开会话:', id);
        }

        function editConversation() {
            console.log('编辑会话');
            closeOptions();
        }

        function shareConversation() {
            console.log('分享会话');
            closeOptions();
        }

        function exportConversation() {
            console.log('导出会话');
            closeOptions();
        }

        function deleteConversation() {
            if (confirm('确定要删除这条会话吗？')) {
                console.log('删除会话');
                closeOptions();
            }
        }

        // 添加底部导航栏的间距
        document.body.style.paddingBottom = '80px';
    </script>
</body>
</html>
