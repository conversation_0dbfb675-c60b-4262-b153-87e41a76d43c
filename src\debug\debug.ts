/**
 * AI Chat Memo 调试工具
 * 运行在扩展上下文中，可以访问Chrome扩展API
 */

class DebugTool {
  private logElement: HTMLElement | null = null;

  constructor() {
    this.init();
  }

  private init(): void {
    document.addEventListener('DOMContentLoaded', () => {
      this.logElement = document.getElementById('test-result');
      this.loadSystemInfo();
      this.checkExtensionStatus();
      this.loadExtensionStats();
    });
  }

  private log(message: string, type: 'info' | 'success' | 'error' | 'warning' = 'info'): void {
    if (!this.logElement) return;

    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${message}\n`;
    
    this.logElement.textContent += logEntry;
    this.logElement.className = `result ${type}`;
    this.logElement.scrollTop = this.logElement.scrollHeight;
  }

  private clearLog(): void {
    if (this.logElement) {
      this.logElement.textContent = '';
      this.logElement.className = 'result';
    }
  }

  private async loadSystemInfo(): Promise<void> {
    const systemInfoEl = document.getElementById('system-info');
    if (!systemInfoEl) return;

    const info = {
      'Chrome版本': navigator.userAgent.match(/Chrome\/([0-9.]+)/)?.[1] || '未知',
      '扩展ID': chrome.runtime?.id || '未知',
      '扩展版本': chrome.runtime?.getManifest?.()?.version || '未知',
      '当前URL': window.location.href,
      '用户代理': navigator.userAgent
    };

    let html = '<table style="width: 100%; border-collapse: collapse;">';
    for (const [key, value] of Object.entries(info)) {
      html += `<tr style="border-bottom: 1px solid #ddd;">
        <td style="padding: 8px; font-weight: bold; width: 120px;">${key}:</td>
        <td style="padding: 8px;">${value}</td>
      </tr>`;
    }
    html += '</table>';
    
    systemInfoEl.innerHTML = html;
  }

  private checkExtensionStatus(): void {
    const statusEl = document.getElementById('extension-status');
    if (!statusEl) return;

    if (typeof chrome !== 'undefined' && chrome.runtime) {
      statusEl.innerHTML = `
        <span class="status-indicator status-online"></span>
        <span>扩展API可用 ✅</span>
      `;
    } else {
      statusEl.innerHTML = `
        <span class="status-indicator status-offline"></span>
        <span>扩展API不可用 ❌</span>
      `;
    }
  }

  private async loadExtensionStats(): Promise<void> {
    const statsEl = document.getElementById('extension-stats');
    if (!statsEl) return;

    try {
      // 获取存储使用情况
      const storage = await chrome.storage.local.getBytesInUse();
      const storageQuota = chrome.storage.local.QUOTA_BYTES;
      
      // 获取权限信息
      const permissions = await chrome.permissions.getAll();
      
      const stats = {
        '存储使用': `${storage} / ${storageQuota} 字节 (${((storage / storageQuota) * 100).toFixed(2)}%)`,
        '权限数量': permissions.permissions?.length || 0,
        '主机权限': permissions.origins?.length || 0,
        '活动标签页权限': permissions.permissions?.includes('activeTab') ? '✅' : '❌',
        '存储权限': permissions.permissions?.includes('storage') ? '✅' : '❌',
        '脚本注入权限': permissions.permissions?.includes('scripting') ? '✅' : '❌'
      };

      let html = '<table style="width: 100%; border-collapse: collapse;">';
      for (const [key, value] of Object.entries(stats)) {
        html += `<tr style="border-bottom: 1px solid #ddd;">
          <td style="padding: 8px; font-weight: bold; width: 150px;">${key}:</td>
          <td style="padding: 8px;">${value}</td>
        </tr>`;
      }
      html += '</table>';
      
      statsEl.innerHTML = html;
    } catch (error) {
      statsEl.innerHTML = `<div class="error">加载统计信息失败: ${error}</div>`;
    }
  }

  // 测试方法
  async testBackgroundConnection(): Promise<void> {
    this.clearLog();
    this.log('📡 测试Background Script连接...');
    
    try {
      const response = await chrome.runtime.sendMessage({ type: 'ping' });
      this.log('✅ Background Script连接成功', 'success');
      this.log(`📄 响应: ${JSON.stringify(response, null, 2)}`);
    } catch (error) {
      this.log(`❌ Background Script连接失败: ${error}`, 'error');
    }
  }

  async testContentScriptConnection(): Promise<void> {
    this.clearLog();
    this.log('📄 测试Content Script连接...');
    
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        this.log('❌ 无法获取当前标签页', 'error');
        return;
      }

      const currentTab = tabs[0];
      this.log(`📍 当前标签页: ${currentTab.url}`);
      
      const response = await chrome.tabs.sendMessage(currentTab.id!, { type: 'ping' });
      this.log('✅ Content Script连接成功', 'success');
      this.log(`📄 响应: ${JSON.stringify(response, null, 2)}`);
    } catch (error) {
      this.log(`❌ Content Script连接失败: ${error}`, 'error');
      
      if (error instanceof Error && error.message.includes('Could not establish connection')) {
        this.log('💡 可能原因:', 'warning');
        this.log('   • 当前页面不支持（不在manifest.json的匹配列表中）');
        this.log('   • Content Script还未加载完成');
        this.log('   • 页面需要刷新');
      }
    }
  }

  async testStorageAccess(): Promise<void> {
    this.clearLog();
    this.log('💾 测试存储访问...');
    
    try {
      // 测试写入
      const testKey = 'debug_test_' + Date.now();
      const testData = { message: 'Debug test', timestamp: Date.now() };
      
      await chrome.storage.local.set({ [testKey]: testData });
      this.log('✅ 存储写入成功', 'success');
      
      // 测试读取
      const result = await chrome.storage.local.get(testKey);
      if (result[testKey]) {
        this.log('✅ 存储读取成功', 'success');
        this.log(`📄 读取数据: ${JSON.stringify(result[testKey], null, 2)}`);
      } else {
        this.log('❌ 存储读取失败', 'error');
      }
      
      // 清理测试数据
      await chrome.storage.local.remove(testKey);
      this.log('✅ 测试数据已清理', 'success');
      
    } catch (error) {
      this.log(`❌ 存储访问失败: ${error}`, 'error');
    }
  }

  async testPermissions(): Promise<void> {
    this.clearLog();
    this.log('🔐 检查权限...');
    
    try {
      const permissions = await chrome.permissions.getAll();
      this.log('✅ 权限检查成功', 'success');
      this.log(`📄 权限列表: ${JSON.stringify(permissions.permissions, null, 2)}`);
      this.log(`📄 主机权限: ${JSON.stringify(permissions.origins, null, 2)}`);
      
      // 检查关键权限
      const requiredPermissions = ['storage', 'activeTab', 'scripting'];
      for (const permission of requiredPermissions) {
        const hasPermission = permissions.permissions?.includes(permission);
        this.log(`${hasPermission ? '✅' : '❌'} ${permission}: ${hasPermission ? '已授权' : '未授权'}`, hasPermission ? 'success' : 'error');
      }
      
    } catch (error) {
      this.log(`❌ 权限检查失败: ${error}`, 'error');
    }
  }

  async getCurrentTabInfo(): Promise<void> {
    this.clearLog();
    this.log('📍 获取当前标签页信息...');
    
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        this.log('❌ 无法获取当前标签页', 'error');
        return;
      }

      const tab = tabs[0];
      this.log('✅ 标签页信息获取成功', 'success');
      this.log(`📄 标签页详情: ${JSON.stringify({
        id: tab.id,
        url: tab.url,
        title: tab.title,
        status: tab.status,
        active: tab.active
      }, null, 2)}`);
      
    } catch (error) {
      this.log(`❌ 获取标签页信息失败: ${error}`, 'error');
    }
  }

  async testSaveConversation(): Promise<void> {
    this.clearLog();
    this.log('💾 测试保存会话...');
    
    try {
      const testConversation = {
        title: '调试测试会话',
        platform: 'debug',
        url: window.location.href,
        messages: [
          { role: 'user', content: '这是一个测试消息' },
          { role: 'assistant', content: '这是AI的回复' }
        ],
        timestamp: Date.now()
      };
      
      const response = await chrome.runtime.sendMessage({
        type: 'save-conversation',
        data: testConversation
      });
      
      if (response && response.success) {
        this.log('✅ 测试会话保存成功', 'success');
        this.log(`📄 保存结果: ${JSON.stringify(response, null, 2)}`);
      } else {
        this.log(`❌ 测试会话保存失败: ${response?.error}`, 'error');
      }
      
    } catch (error) {
      this.log(`❌ 测试会话保存失败: ${error}`, 'error');
    }
  }

  async reloadExtension(): Promise<void> {
    this.clearLog();
    this.log('🔄 重新加载扩展...');
    
    try {
      await chrome.runtime.reload();
      this.log('✅ 扩展重新加载成功', 'success');
    } catch (error) {
      this.log(`❌ 扩展重新加载失败: ${error}`, 'error');
    }
  }

  async openOptionsPage(): Promise<void> {
    this.clearLog();
    this.log('⚙️ 打开设置页面...');
    
    try {
      await chrome.runtime.openOptionsPage();
      this.log('✅ 设置页面已打开', 'success');
    } catch (error) {
      this.log(`❌ 打开设置页面失败: ${error}`, 'error');
    }
  }
}

// 全局函数，供HTML调用
const debugTool = new DebugTool();

function testBackgroundConnection() {
  debugTool.testBackgroundConnection();
}

function testContentScriptConnection() {
  debugTool.testContentScriptConnection();
}

function testStorageAccess() {
  debugTool.testStorageAccess();
}

function testPermissions() {
  debugTool.testPermissions();
}

function clearLog() {
  debugTool.clearLog();
}

function getCurrentTabInfo() {
  debugTool.getCurrentTabInfo();
}

function testSaveConversation() {
  debugTool.testSaveConversation();
}

function reloadExtension() {
  debugTool.reloadExtension();
}

function openOptionsPage() {
  debugTool.openOptionsPage();
}
