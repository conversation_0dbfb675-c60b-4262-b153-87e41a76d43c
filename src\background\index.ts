/**
 * Background Script 入口文件
 * 处理插件的后台逻辑和事件监听
 */

import { MessageBus } from '@shared/message-bus';
import { Logger } from '@shared/logger';
import { EVENTS } from '@types/events';
import { storageService } from '@storage/index';
import { conversationManager } from '@conversation/index';
import { tagManager } from '@tags/index';

class BackgroundService {
  private messageBus: MessageBus;
  private logger: Logger;
  private cleanupInterval: number | null = null;
  private performanceMonitor: PerformanceMonitor;

  constructor() {
    this.logger = new Logger('Background');
    this.messageBus = new MessageBus('background');
    this.performanceMonitor = new PerformanceMonitor();
    this.initialize();
  }

  async initialize(): Promise<void> {
    try {
      this.logger.info('初始化后台服务...');

      // 初始化存储服务
      await storageService.initialize();

      // 初始化会话管理器
      await conversationManager.initialize();

    // 初始化标签管理器
    await tagManager.initialize();

      this.setupMessageListeners();
      this.setupPeriodicTasks();
      this.setupErrorHandling();
      this.logger.info('后台服务初始化成功');
    } catch (error) {
      this.logger.error('后台服务初始化失败:', error);
    }
  }

  private setupMessageListeners(): void {
    // 设置Chrome runtime消息监听器
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleRuntimeMessage(message, sender, sendResponse);
      return true; // 保持消息通道开放以支持异步响应
    });

    // 响应 ping 请求
    this.messageBus.on('ping', () => {
      return { status: 'ok' };
    });

    // 处理平台检测
    this.messageBus.on('platform-detected', (data) => {
      this.logger.info(`检测到平台: ${data.platform} (${data.url})`);
      // TODO: 更新插件图标状态
    });

    // 处理会话保存
    this.messageBus.on('save-conversation', async (conversation) => {
      try {
        await storageService.conversations.saveConversation(conversation);
        this.logger.info(`会话已保存: ${conversation.title} (${conversation.messages.length} 条消息)`);
        return { success: true };
      } catch (error) {
        this.logger.error('保存会话失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理会话创建事件
    this.messageBus.on(EVENTS.CONVERSATION_CREATED, async (conversation) => {
      this.logger.info(`新会话创建: ${conversation.title}`);

      // 显示通知
      const showNotifications = await storageService.settings.get('notifications.enabled', true);
      if (showNotifications) {
        this.showNotification('新会话已保存', `${conversation.title} - ${conversation.platform}`);
      }
    });

    // 处理会话更新事件
    this.messageBus.on(EVENTS.CONVERSATION_UPDATED, async (conversation) => {
      this.logger.debug(`会话更新: ${conversation.title}`);

      // 自动保存会话
      try {
        await storageService.conversations.saveConversation(conversation);
      } catch (error) {
        this.logger.error('自动保存会话失败:', error);
      }
    });

    // 处理搜索请求
    this.messageBus.on('search-conversations', async (query, options) => {
      try {
        const results = await storageService.search.search(query, options);
        return { success: true, results };
      } catch (error) {
        this.logger.error('搜索失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理导出请求
    this.messageBus.on('export-conversations', async (options) => {
      try {
        const result = await storageService.export.exportConversations(options);
        return result;
      } catch (error) {
        this.logger.error('导出失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理设置获取
    this.messageBus.on('get-settings', async () => {
      try {
        const settings = await storageService.settings.getAppSettings();
        return { success: true, settings };
      } catch (error) {
        this.logger.error('获取设置失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理设置更新
    this.messageBus.on('update-settings', async (settings) => {
      try {
        await storageService.settings.updateAppSettings(settings);
        return { success: true };
      } catch (error) {
        this.logger.error('更新设置失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理统计信息请求
    this.messageBus.on('get-statistics', async () => {
      try {
        const stats = await storageService.conversations.getStatistics();
        const storageStats = await storageService.getStorageStats();
        return { success: true, stats: { ...stats, storage: storageStats } };
      } catch (error) {
        this.logger.error('获取统计信息失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理会话创建请求
    this.messageBus.on('create-conversation', async (conversationData) => {
      try {
        const conversation = await conversationManager.createConversation(conversationData);
        return { success: true, conversation };
      } catch (error) {
        this.logger.error('创建会话失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理会话更新请求
    this.messageBus.on('update-conversation', async ({ id, updates }) => {
      try {
        const conversation = await conversationManager.updateConversation(id, updates);
        return { success: true, conversation };
      } catch (error) {
        this.logger.error('更新会话失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理会话删除请求
    this.messageBus.on('delete-conversation', async ({ id, permanent }) => {
      try {
        await conversationManager.deleteConversation(id, permanent);
        return { success: true };
      } catch (error) {
        this.logger.error('删除会话失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理批量删除请求
    this.messageBus.on('delete-conversations', async ({ ids, permanent }) => {
      try {
        const result = await conversationManager.deleteConversations(ids, permanent);
        return { success: true, result };
      } catch (error) {
        this.logger.error('批量删除会话失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理会话合并请求
    this.messageBus.on('merge-conversations', async (options) => {
      try {
        const conversation = await conversationManager.mergeConversations(options);
        return { success: true, conversation };
      } catch (error) {
        this.logger.error('合并会话失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理会话复制请求
    this.messageBus.on('duplicate-conversation', async ({ id, newTitle }) => {
      try {
        const conversation = await conversationManager.duplicateConversation(id, newTitle);
        return { success: true, conversation };
      } catch (error) {
        this.logger.error('复制会话失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理重复会话查找请求
    this.messageBus.on('find-duplicate-conversations', async ({ conversation, options }) => {
      try {
        const duplicates = await conversationManager.findDuplicateConversations(conversation, options);
        return { success: true, duplicates };
      } catch (error) {
        this.logger.error('查找重复会话失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理批量归档请求
    this.messageBus.on('archive-conversations', async (options) => {
      try {
        const result = await conversationManager.archiveConversations(options);
        return { success: true, result };
      } catch (error) {
        this.logger.error('批量归档失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理恢复归档请求
    this.messageBus.on('unarchive-conversation', async (id) => {
      try {
        const conversation = await conversationManager.unarchiveConversation(id);
        return { success: true, conversation };
      } catch (error) {
        this.logger.error('恢复归档失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理添加消息请求
    this.messageBus.on('add-message', async ({ conversationId, message }) => {
      try {
        const newMessage = await conversationManager.addMessageToConversation(conversationId, message);
        return { success: true, message: newMessage };
      } catch (error) {
        this.logger.error('添加消息失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理更新消息请求
    this.messageBus.on('update-message', async ({ conversationId, messageId, updates }) => {
      try {
        const message = await conversationManager.updateMessageInConversation(conversationId, messageId, updates);
        return { success: true, message };
      } catch (error) {
        this.logger.error('更新消息失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理删除消息请求
    this.messageBus.on('delete-message', async ({ conversationId, messageId }) => {
      try {
        await conversationManager.deleteMessageFromConversation(conversationId, messageId);
        return { success: true };
      } catch (error) {
        this.logger.error('删除消息失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 处理会话统计请求
    this.messageBus.on('get-conversation-stats', async (id) => {
      try {
        const stats = await conversationManager.getConversationStats(id);
        return { success: true, stats };
      } catch (error) {
        this.logger.error('获取会话统计失败:', error);
        return { success: false, error: error.message };
      }
    });

    // 标签管理相关消息处理
    this.messageBus.on('generate-auto-tags', async (data) => {
      try {
        const { conversation } = data;
        const suggestions = await tagManager.generateAutoTags(conversation);
        return { success: true, suggestions };
      } catch (error) {
        this.logger.error('生成自动标签失败:', error);
        return { success: false, error: error.message };
      }
    });

    this.messageBus.on('apply-auto-tags', async (data) => {
      try {
        const { conversationId, suggestions, threshold } = data;
        const appliedTags = await tagManager.applyAutoTags(conversationId, suggestions, threshold);
        return { success: true, appliedTags };
      } catch (error) {
        this.logger.error('应用自动标签失败:', error);
        return { success: false, error: error.message };
      }
    });

    this.messageBus.on('add-tags', async (data) => {
      try {
        const { conversationId, tags } = data;
        await tagManager.addTagsToConversation(conversationId, tags);
        return { success: true };
      } catch (error) {
        this.logger.error('添加标签失败:', error);
        return { success: false, error: error.message };
      }
    });

    this.messageBus.on('remove-tags', async (data) => {
      try {
        const { conversationId, tags } = data;
        await tagManager.removeTagsFromConversation(conversationId, tags);
        return { success: true };
      } catch (error) {
        this.logger.error('移除标签失败:', error);
        return { success: false, error: error.message };
      }
    });

    this.messageBus.on('get-all-tags', async () => {
      try {
        const tags = await tagManager.getAllTags();
        return { success: true, tags };
      } catch (error) {
        this.logger.error('获取所有标签失败:', error);
        return { success: false, error: error.message };
      }
    });

    this.messageBus.on('search-tags', async (data) => {
      try {
        const { query, limit } = data;
        const tags = await tagManager.searchTags(query, limit);
        return { success: true, tags };
      } catch (error) {
        this.logger.error('搜索标签失败:', error);
        return { success: false, error: error.message };
      }
    });

    this.messageBus.on('get-tag-statistics', async () => {
      try {
        const statistics = await tagManager.getTagStatistics();
        return { success: true, statistics };
      } catch (error) {
        this.logger.error('获取标签统计失败:', error);
        return { success: false, error: error.message };
      }
    });

    this.messageBus.on('create-tag-rule', async (data) => {
      try {
        const { rule } = data;
        const newRule = await tagManager.createTagRule(rule);
        return { success: true, rule: newRule };
      } catch (error) {
        this.logger.error('创建标签规则失败:', error);
        return { success: false, error: error.message };
      }
    });

    this.messageBus.on('update-tag-rule', async (data) => {
      try {
        const { id, updates } = data;
        const updatedRule = await tagManager.updateTagRule(id, updates);
        return { success: true, rule: updatedRule };
      } catch (error) {
        this.logger.error('更新标签规则失败:', error);
        return { success: false, error: error.message };
      }
    });

    this.messageBus.on('delete-tag-rule', async (data) => {
      try {
        const { id } = data;
        await tagManager.deleteTagRule(id);
        return { success: true };
      } catch (error) {
        this.logger.error('删除标签规则失败:', error);
        return { success: false, error: error.message };
      }
    });

    this.messageBus.on('get-tag-rules', async () => {
      try {
        const rules = tagManager.getTagRules();
        return { success: true, rules };
      } catch (error) {
        this.logger.error('获取标签规则失败:', error);
        return { success: false, error: error.message };
      }
    });

    this.messageBus.on('batch-process-tags', async (data) => {
      try {
        const { conversationIds, options } = data;
        const result = await tagManager.batchProcessTags(conversationIds, options);
        return { success: true, result };
      } catch (error) {
        this.logger.error('批量处理标签失败:', error);
        return { success: false, error: error.message };
      }
    });
  }

  /**
   * 显示通知
   */
  private showNotification(title: string, message: string): void {
    if (chrome.notifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon-48.png',
        title,
        message
      });
    }
  }

  /**
   * 处理Chrome runtime消息
   */
  private async handleRuntimeMessage(
    message: any,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      this.logger.debug('收到runtime消息:', message);

      switch (message.type) {
        case 'ping':
          sendResponse({ success: true, status: 'ok' });
          break;

        case 'get-recent-conversations':
          const conversations = await conversationManager.getRecentConversations(message.data?.limit || 50);
          sendResponse({ success: true, conversations });
          break;

        case 'search-conversations':
          const searchResults = await conversationManager.searchConversations(message.data?.query || '');
          sendResponse({ success: true, conversations: searchResults });
          break;

        case 'export-conversations':
          const exportData = await conversationManager.exportConversations(message.data?.format || 'json');
          sendResponse({ success: true, data: exportData });
          break;

        case 'get-stats':
          const stats = await this.getStats();
          sendResponse({ success: true, stats });
          break;

        case 'save-current-conversation':
          try {
            // 获取当前活动标签页
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tabs.length === 0) {
              sendResponse({ success: false, error: '无法获取当前标签页' });
              break;
            }

            const currentTab = tabs[0];
            if (!currentTab.id) {
              sendResponse({ success: false, error: '当前标签页ID无效' });
              break;
            }

            // 发送消息到content script
            const response = await chrome.tabs.sendMessage(currentTab.id, {
              type: 'save-current-conversation'
            });

            sendResponse(response);
          } catch (error) {
            this.logger.error('保存当前会话失败:', error);
            sendResponse({
              success: false,
              error: error instanceof Error ? error.message : '保存会话时发生未知错误'
            });
          }
          break;

        case 'save-conversation':
          try {
            if (!message.data) {
              sendResponse({ success: false, error: '缺少会话数据' });
              break;
            }

            // 保存会话到存储
            const savedConversation = await conversationManager.createConversation(message.data);
            this.logger.info(`会话已保存: ${savedConversation.id}`);

            sendResponse({
              success: true,
              conversation: savedConversation,
              message: '会话保存成功'
            });
          } catch (error) {
            this.logger.error('保存会话失败:', error);
            sendResponse({
              success: false,
              error: error instanceof Error ? error.message : '保存会话时发生未知错误'
            });
          }
          break;

        default:
          this.logger.warn('未知的消息类型:', message.type);
          sendResponse({ success: false, error: '未知的消息类型' });
      }
    } catch (error) {
      this.logger.error('处理runtime消息失败:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  /**
   * 获取统计信息
   */
  private async getStats(): Promise<any> {
    try {
      const totalConversations = await conversationManager.getConversationCount();
      const recentActivity = await conversationManager.getRecentActivity(7); // 最近7天

      return {
        totalConversations,
        totalMessages: 0, // TODO: 实现消息计数
        recentActivity,
        platformStats: {}, // TODO: 实现平台统计
        popularTags: [] // TODO: 实现标签统计
      };
    } catch (error) {
      this.logger.error('获取统计信息失败:', error);
      return {
        totalConversations: 0,
        totalMessages: 0,
        recentActivity: [],
        platformStats: {},
        popularTags: []
      };
    }
  }

  /**
   * 设置定期任务
   */
  private setupPeriodicTasks(): void {
    // 每小时执行一次清理任务
    this.cleanupInterval = setInterval(async () => {
      try {
        await this.performMaintenance();
      } catch (error) {
        this.logger.error('定期维护任务失败:', error);
      }
    }, 60 * 60 * 1000); // 1小时

    this.logger.info('定期任务已设置');
  }

  /**
   * 设置错误处理
   */
  private setupErrorHandling(): void {
    // 监听未捕获的错误
    self.addEventListener('error', (event) => {
      this.logger.error('未捕获的错误:', event.error);
      this.performanceMonitor.recordError(event.error);
    });

    // 监听未处理的Promise拒绝
    self.addEventListener('unhandledrejection', (event) => {
      this.logger.error('未处理的Promise拒绝:', event.reason);
      this.performanceMonitor.recordError(event.reason);
    });

    this.logger.info('错误处理已设置');
  }

  /**
   * 执行维护任务
   */
  private async performMaintenance(): Promise<void> {
    this.logger.info('开始执行维护任务...');

    try {
      // 清理过期的缓存
      await this.cleanupExpiredCache();

      // 优化数据库
      await this.optimizeDatabase();

      // 清理日志
      await this.cleanupLogs();

      // 更新性能统计
      await this.updatePerformanceStats();

      this.logger.info('维护任务完成');
    } catch (error) {
      this.logger.error('维护任务失败:', error);
    }
  }

  /**
   * 清理过期缓存
   */
  private async cleanupExpiredCache(): Promise<void> {
    try {
      // 清理会话缓存
      await conversationManager.cleanupCache();

      // 清理标签缓存
      tagManager.cleanup();

      this.logger.debug('缓存清理完成');
    } catch (error) {
      this.logger.error('缓存清理失败:', error);
    }
  }

  /**
   * 优化数据库
   */
  private async optimizeDatabase(): Promise<void> {
    try {
      // 执行数据库优化
      await storageService.optimize();

      this.logger.debug('数据库优化完成');
    } catch (error) {
      this.logger.error('数据库优化失败:', error);
    }
  }

  /**
   * 清理日志
   */
  private async cleanupLogs(): Promise<void> {
    try {
      // 清理超过30天的日志
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      await storageService.settings.cleanupLogs(thirtyDaysAgo);

      this.logger.debug('日志清理完成');
    } catch (error) {
      this.logger.error('日志清理失败:', error);
    }
  }

  /**
   * 更新性能统计
   */
  private async updatePerformanceStats(): Promise<void> {
    try {
      const stats = this.performanceMonitor.getStats();
      await storageService.settings.set('performanceStats', stats);

      this.logger.debug('性能统计更新完成');
    } catch (error) {
      this.logger.error('性能统计更新失败:', error);
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    storageService.close();
    conversationManager.cleanup();
    tagManager.cleanup();
    this.performanceMonitor.cleanup();
    this.logger.info('后台服务已清理');
  }
}

/**
 * 性能监控类
 */
class PerformanceMonitor {
  private stats: {
    startTime: number;
    errorCount: number;
    operationCount: number;
    averageResponseTime: number;
    memoryUsage: number[];
    lastCleanup: number;
  };

  constructor() {
    this.stats = {
      startTime: Date.now(),
      errorCount: 0,
      operationCount: 0,
      averageResponseTime: 0,
      memoryUsage: [],
      lastCleanup: Date.now()
    };

    this.startMonitoring();
  }

  /**
   * 开始监控
   */
  private startMonitoring(): void {
    // 每分钟记录一次内存使用情况
    setInterval(() => {
      this.recordMemoryUsage();
    }, 60 * 1000);
  }

  /**
   * 记录错误
   */
  recordError(error: any): void {
    this.stats.errorCount++;
    console.error('Performance Monitor - Error recorded:', error);
  }

  /**
   * 记录操作
   */
  recordOperation(responseTime: number): void {
    this.stats.operationCount++;

    // 计算平均响应时间
    const totalTime = this.stats.averageResponseTime * (this.stats.operationCount - 1) + responseTime;
    this.stats.averageResponseTime = totalTime / this.stats.operationCount;
  }

  /**
   * 记录内存使用情况
   */
  private recordMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.stats.memoryUsage.push({
        timestamp: Date.now(),
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit
      });

      // 只保留最近24小时的数据
      const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000;
      this.stats.memoryUsage = this.stats.memoryUsage.filter(
        (record: any) => record.timestamp > oneDayAgo
      );
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): any {
    return {
      ...this.stats,
      uptime: Date.now() - this.stats.startTime,
      errorRate: this.stats.operationCount > 0 ? this.stats.errorCount / this.stats.operationCount : 0
    };
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    // 清理定时器等资源
    this.stats.memoryUsage = [];
  }
}

// 初始化服务
const backgroundService = new BackgroundService();

// 导出服务实例
export default backgroundService;


