/**
 * 内容检测引擎
 * 负责检测页面中的AI对话内容，管理平台适配器，处理内容变化
 */

import { AdapterManager } from './adapters/adapter-manager';
import { ConversationData } from '@types/conversation';
import { Logger } from '@shared/logger';
import { MessageBus } from '@shared/message-bus';
import { EVENTS } from '@types/index';

export class ContentDetectionEngine {
  private logger: Logger;
  private messageBus: MessageBus;
  private adapterManager: AdapterManager;
  private isActive = false;
  private checkInterval: number | null = null;
  private lastConversationId: string | null = null;

  constructor() {
    console.log('🔧 [AI Chat Memo] ContentDetectionEngine constructor called');
    this.logger = new Logger('ContentDetectionEngine');
    this.messageBus = new MessageBus('content-detection');
    this.adapterManager = new AdapterManager();
    console.log('✅ [AI Chat Memo] ContentDetectionEngine initialized');
  }

  /**
   * 启动内容检测
   */
  async start(): Promise<void> {
    console.log('🚀 [AI Chat Memo] ContentDetectionEngine.start() called');
    if (this.isActive) {
      console.log('⚠️ [AI Chat Memo] 检测引擎已经在运行');
      this.logger.warn('检测引擎已经在运行');
      return;
    }

    console.log('🔧 [AI Chat Memo] 启动内容检测引擎');
    this.logger.info('启动内容检测引擎');
    this.isActive = true;

    // 检测当前平台
    console.log('🔍 [AI Chat Memo] 检测当前平台...');
    await this.detectCurrentPlatform();

    // 开始监听页面变化
    console.log('👀 [AI Chat Memo] 开始监听页面变化...');
    this.startPageMonitoring();

    // 设置定期检查
    console.log('⏰ [AI Chat Memo] 设置定期检查...');
    this.startPeriodicCheck();

    console.log('✅ [AI Chat Memo] 内容检测引擎已启动');
    this.logger.info('内容检测引擎已启动');
  }

  /**
   * 停止内容检测
   */
  stop(): void {
    if (!this.isActive) {
      return;
    }

    this.logger.info('停止内容检测引擎');
    this.isActive = false;

    // 停止适配器管理器
    this.adapterManager.stopObserving();

    // 清除定期检查
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }

    this.logger.info('内容检测引擎已停止');
  }

  /**
   * 检测当前平台
   */
  private async detectCurrentPlatform(): Promise<void> {
    this.logger.debug('检测当前平台...');

    if (this.adapterManager.isPageReady()) {
      const currentAdapter = this.adapterManager.getCurrentAdapter();
      if (currentAdapter) {
        this.logger.info(`检测到平台: ${currentAdapter.platform}`);

        // 立即提取一次会话数据
        await this.extractAndSaveConversation();

        // 开始监听该平台的变化
        this.startAdapterObserving();

        return;
      }
    }

    this.logger.debug('未检测到支持的平台或页面未准备就绪');
  }

  /**
   * 开始适配器监听
   */
  private startAdapterObserving(): void {
    const success = this.adapterManager.startObserving((conversation: ConversationData) => {
      this.handleConversationUpdate(conversation);
    });

    if (!success) {
      this.logger.warn('无法开始监听页面变化');
    }
  }

  /**
   * 处理会话更新
   */
  private async handleConversationUpdate(conversation: ConversationData): Promise<void> {
    try {
      // 检查是否为新会话或有更新
      if (this.lastConversationId !== conversation.id) {
        this.logger.info(`检测到新会话: ${conversation.title}`);
        this.lastConversationId = conversation.id;
        
        // 发送会话创建事件
        await this.messageBus.emit(EVENTS.CONVERSATION_CREATED, conversation);
      } else {
        this.logger.debug(`会话更新: ${conversation.title} (${conversation.messages.length} 条消息)`);
        
        // 发送会话更新事件
        await this.messageBus.emit(EVENTS.CONVERSATION_UPDATED, conversation);
      }

      // 保存到本地存储
      await this.saveConversation(conversation);

    } catch (error) {
      this.logger.error('处理会话更新时出错:', error);
    }
  }

  /**
   * 提取并保存会话
   */
  private async extractAndSaveConversation(): Promise<void> {
    try {
      const conversation = this.adapterManager.extractConversation();
      if (conversation) {
        await this.handleConversationUpdate(conversation);
      }
    } catch (error) {
      this.logger.error('提取会话数据时出错:', error);
    }
  }

  /**
   * 保存会话到本地存储
   */
  private async saveConversation(conversation: ConversationData): Promise<void> {
    try {
      // 发送保存请求到 background script
      await this.messageBus.send('save-conversation', conversation);
      this.logger.debug(`会话已保存: ${conversation.id}`);
    } catch (error) {
      this.logger.error('保存会话时出错:', error);
    }
  }

  /**
   * 开始页面监听
   */
  private startPageMonitoring(): void {
    // 监听 URL 变化
    let currentUrl = window.location.href;
    
    const checkUrlChange = () => {
      if (window.location.href !== currentUrl) {
        currentUrl = window.location.href;
        this.logger.debug('URL 变化，重新检测平台');
        this.handleUrlChange();
      }
    };

    // 监听 popstate 事件（浏览器前进后退）
    window.addEventListener('popstate', checkUrlChange);

    // 监听 pushstate 和 replacestate（SPA 路由变化）
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function(...args) {
      originalPushState.apply(history, args);
      setTimeout(checkUrlChange, 100);
    };

    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args);
      setTimeout(checkUrlChange, 100);
    };
  }

  /**
   * 处理 URL 变化
   */
  private async handleUrlChange(): Promise<void> {
    // 停止当前适配器
    this.adapterManager.stopObserving();

    // 重置状态
    this.lastConversationId = null;

    // 等待页面加载
    setTimeout(async () => {
      this.adapterManager.refresh();
      await this.detectCurrentPlatform();
    }, 1000);
  }

  /**
   * 开始定期检查
   */
  private startPeriodicCheck(): void {
    this.checkInterval = window.setInterval(async () => {
      if (!this.isActive) {
        return;
      }

      // 检查适配器管理器状态
      if (!this.adapterManager.isPageReady()) {
        this.logger.debug('页面状态变化，重新检测');
        this.adapterManager.refresh();
        await this.detectCurrentPlatform();
      }
    }, 5000); // 每5秒检查一次
  }

  /**
   * 手动触发会话提取
   */
  async extractConversation(): Promise<ConversationData | null> {
    try {
      const conversation = this.adapterManager.extractConversation();
      if (conversation) {
        await this.handleConversationUpdate(conversation);
      }
      return conversation;
    } catch (error) {
      this.logger.error('手动提取会话时出错:', error);
      return null;
    }
  }

  /**
   * 获取当前平台信息
   */
  getCurrentPlatform(): string | null {
    const adapter = this.adapterManager.getCurrentAdapter();
    return adapter?.platform || null;
  }

  /**
   * 检查是否支持当前页面
   */
  isCurrentPageSupported(): boolean {
    return this.adapterManager.isCurrentPageSupported();
  }

  /**
   * 获取平台统计信息
   */
  getPlatformStats() {
    return this.adapterManager.getPlatformStats();
  }

  /**
   * 获取适配器信息
   */
  getAdapterInfo() {
    return this.adapterManager.getAdapterInfo();
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stop();
    this.adapterManager.cleanup();
  }
}
