/**
 * 基础平台适配器
 * 定义所有平台适配器的通用接口和基础功能
 */

import { ConversationData, MessageData } from '@types/conversation';
import { Logger } from '@shared/logger';

export interface PlatformSelectors {
  conversationContainer: string;
  messageElements: string;
  userMessage: string;
  assistantMessage: string;
  messageContent: string;
  messageInput: string;
  sendButton: string;
  conversationTitle?: string;
  conversationList?: string;
}

export interface PlatformFeatures {
  hasConversationList: boolean;
  hasMessageTimestamps: boolean;
  hasCodeBlocks: boolean;
  hasImageSupport: boolean;
  hasFileUpload: boolean;
  hasConversationExport: boolean;
}

export abstract class BasePlatformAdapter {
  protected logger: Logger;
  protected observer: MutationObserver | null = null;
  protected lastMessageCount = 0;

  abstract readonly platform: string;
  abstract readonly selectors: PlatformSelectors;
  
  protected features: PlatformFeatures = {
    hasConversationList: true,
    hasMessageTimestamps: false,
    hasCodeBlocks: true,
    hasImageSupport: false,
    hasFileUpload: false,
    hasConversationExport: false
  };

  constructor() {
    this.logger = new Logger(`Adapter:${this.constructor.name}`);
  }

  /**
   * 检查当前页面是否为该平台的会话页面
   */
  abstract isPageReady(): boolean;

  /**
   * 检查当前页面是否匹配该平台
   */
  isCurrentPlatform(): boolean {
    const hostname = window.location.hostname;
    const domains = this.getPlatformDomains();
    const isMatch = domains.some(domain => hostname.includes(domain));

    console.log(`🔍 [AI Chat Memo] ${this.platform} isCurrentPlatform check:`, {
      hostname,
      domains,
      isMatch
    });

    return isMatch;
  }

  /**
   * 获取平台的域名列表
   */
  protected getPlatformDomains(): string[] {
    const domainMap: Record<string, string[]> = {
      'ChatGPT': ['chatgpt.com', 'chat.openai.com'], // 基于真实分析：现在主要使用 chatgpt.com
      'Claude': ['claude.ai'],
      'Gemini': ['gemini.google.com'],
      'Aistudio': ['aistudio.google.com'],
      'Monica': ['monica.im'],
      'Poe': ['poe.com']
    };
    
    return domainMap[this.platform] || [];
  }

  /**
   * 提取当前页面的会话数据
   */
  abstract extractConversation(): ConversationData | null;

  /**
   * 开始监听页面变化
   */
  startObserving(callback: (conversation: ConversationData) => void): void {
    if (this.observer) {
      this.stopObserving();
    }

    const container = document.querySelector(this.selectors.conversationContainer);
    if (!container) {
      this.logger.warn('未找到会话容器，无法开始监听');
      return;
    }

    this.observer = new MutationObserver((mutations) => {
      let hasNewMessages = false;

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // 检查是否有新的消息节点
          const addedElements = Array.from(mutation.addedNodes)
            .filter(node => node.nodeType === Node.ELEMENT_NODE) as Element[];
          
          const hasMessageElements = addedElements.some(element => 
            element.matches(this.selectors.userMessage) ||
            element.matches(this.selectors.assistantMessage) ||
            element.querySelector(this.selectors.userMessage) ||
            element.querySelector(this.selectors.assistantMessage)
          );

          if (hasMessageElements) {
            hasNewMessages = true;
          }
        }
      });

      if (hasNewMessages) {
        this.logger.debug('检测到新消息，提取会话数据');
        const conversation = this.extractConversation();
        if (conversation && conversation.messages.length > this.lastMessageCount) {
          this.lastMessageCount = conversation.messages.length;
          callback(conversation);
        }
      }
    });

    this.observer.observe(container, {
      childList: true,
      subtree: true,
      attributes: false
    });

    this.logger.info('开始监听页面变化');
  }

  /**
   * 停止监听页面变化
   */
  stopObserving(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
      this.logger.info('停止监听页面变化');
    }
  }

  /**
   * 提取会话标题
   */
  protected extractTitle(): string {
    if (this.selectors.conversationTitle) {
      const titleElement = document.querySelector(this.selectors.conversationTitle);
      if (titleElement) {
        return titleElement.textContent?.trim() || '';
      }
    }

    // 尝试从页面标题提取
    const pageTitle = document.title;
    if (pageTitle && !pageTitle.includes(this.platform)) {
      return pageTitle;
    }

    // 尝试从第一条用户消息提取
    const firstUserMessage = document.querySelector(this.selectors.userMessage);
    if (firstUserMessage) {
      const content = this.extractMessageContent(firstUserMessage);
      return content.slice(0, 50) + (content.length > 50 ? '...' : '');
    }

    return `${this.platform} 会话 - ${new Date().toLocaleDateString()}`;
  }

  /**
   * 提取消息内容
   */
  protected extractMessageContent(element: Element): string {
    const contentElement = element.querySelector(this.selectors.messageContent) || element;
    
    if (!contentElement) return '';

    const cloned = contentElement.cloneNode(true) as Element;

    // 处理代码块
    if (this.features.hasCodeBlocks) {
      this.processCodeBlocks(cloned);
    }

    // 处理图片
    if (this.features.hasImageSupport) {
      this.processImages(cloned);
    }

    // 处理链接
    this.processLinks(cloned);

    return cloned.textContent?.trim() || '';
  }

  /**
   * 处理代码块
   */
  protected processCodeBlocks(element: Element): void {
    const codeBlocks = element.querySelectorAll('pre, code');
    codeBlocks.forEach((block) => {
      const language = this.detectCodeLanguage(block);
      const codeText = block.textContent || '';
      block.textContent = `[代码块:${language}]\n${codeText}`;
    });
  }

  /**
   * 处理图片
   */
  protected processImages(element: Element): void {
    const images = element.querySelectorAll('img');
    images.forEach(img => {
      const alt = img.getAttribute('alt') || '图片';
      img.replaceWith(document.createTextNode(`[${alt}]`));
    });
  }

  /**
   * 处理链接
   */
  protected processLinks(element: Element): void {
    const links = element.querySelectorAll('a');
    links.forEach(link => {
      const href = link.getAttribute('href');
      const text = link.textContent;
      if (href && href !== text) {
        link.textContent = `${text} (${href})`;
      }
    });
  }

  /**
   * 检测代码语言
   */
  protected detectCodeLanguage(element: Element): string {
    // 检查类名
    const className = element.className;
    const langMatch = className.match(/language-(\w+)|lang-(\w+)/);
    if (langMatch) {
      return langMatch[1] || langMatch[2];
    }

    // 检查data属性
    const dataLang = element.getAttribute('data-language') || 
                    element.getAttribute('data-lang');
    if (dataLang) {
      return dataLang;
    }

    return '未知';
  }

  /**
   * 按DOM顺序排序消息
   */
  protected sortMessagesByDOMOrder(messages: MessageData[]): MessageData[] {
    // 获取所有消息元素的DOM顺序
    const allMessageElements = document.querySelectorAll(
      `${this.selectors.userMessage}, ${this.selectors.assistantMessage}`
    );

    const elementOrder = new Map<string, number>();
    allMessageElements.forEach((element, index) => {
      elementOrder.set(element.outerHTML, index);
    });

    return messages.sort((a, b) => {
      const orderA = elementOrder.get(a.metadata?.originalElement || '') || 0;
      const orderB = elementOrder.get(b.metadata?.originalElement || '') || 0;
      return orderA - orderB;
    });
  }

  /**
   * 生成会话ID
   */
  protected generateConversationId(url: string): string {
    // 尝试从URL提取ID
    const patterns = [
      /\/c\/([a-zA-Z0-9-_]+)/,
      /\/chat\/([a-zA-Z0-9-_]+)/,
      /\/conversation\/([a-zA-Z0-9-_]+)/,
      /[?&]id=([a-zA-Z0-9-_]+)/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }

    // 如果无法从URL提取，使用URL的hash
    return btoa(url).slice(0, 16);
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopObserving();
  }
}
