/**
 * 浮动状态指示器
 * 显示扩展的工作状态和平台信息
 */

export class FloatingIndicator {
  private element: HTMLElement | null = null;
  private isVisible = false;
  private currentStatus: 'idle' | 'detecting' | 'saving' | 'saved' | 'error' = 'idle';
  private currentPlatform = '';

  constructor() {
    console.log('🎨 [AI Chat Memo] FloatingIndicator constructor called');
    this.createIndicator();
  }

  /**
   * 创建浮动指示器元素
   */
  private createIndicator(): void {
    console.log('🎨 [AI Chat Memo] Creating floating indicator');
    
    this.element = document.createElement('div');
    this.element.className = 'ai-chat-memo-indicator hidden';
    this.element.innerHTML = `
      <div class="icon">💬</div>
      <div class="content">
        <div class="status-text">AI Chat Memo</div>
        <div class="platform-text">正在检测平台...</div>
      </div>
      <div class="ai-chat-memo-tooltip">点击打开设置</div>
    `;

    // 添加点击事件
    this.element.addEventListener('click', () => {
      console.log('🎨 [AI Chat Memo] Floating indicator clicked');
      this.openPopup();
    });

    // 添加到页面
    document.body.appendChild(this.element);
    console.log('✅ [AI Chat Memo] Floating indicator added to page');

    // 延迟显示，确保页面加载完成
    setTimeout(() => {
      this.show();
    }, 1000);
  }

  /**
   * 显示指示器
   */
  show(): void {
    if (!this.element || this.isVisible) return;
    
    console.log('🎨 [AI Chat Memo] Showing floating indicator');
    this.element.classList.remove('hidden');
    this.element.classList.add('entering');
    this.isVisible = true;

    // 移除动画类
    setTimeout(() => {
      if (this.element) {
        this.element.classList.remove('entering');
      }
    }, 300);
  }

  /**
   * 隐藏指示器
   */
  hide(): void {
    if (!this.element || !this.isVisible) return;
    
    console.log('🎨 [AI Chat Memo] Hiding floating indicator');
    this.element.classList.add('hidden');
    this.isVisible = false;
  }

  /**
   * 更新状态
   */
  updateStatus(status: 'idle' | 'detecting' | 'saving' | 'saved' | 'error', message?: string): void {
    if (!this.element) return;

    console.log('🎨 [AI Chat Memo] Updating status:', status, message);
    this.currentStatus = status;

    // 更新样式
    this.element.className = `ai-chat-memo-indicator ${status}`;
    if (this.isVisible) {
      this.element.classList.remove('hidden');
    }

    // 更新图标和文本
    const iconElement = this.element.querySelector('.icon') as HTMLElement;
    const statusElement = this.element.querySelector('.status-text') as HTMLElement;

    if (iconElement && statusElement) {
      switch (status) {
        case 'detecting':
          iconElement.textContent = '🔍';
          statusElement.textContent = message || '检测中...';
          break;
        case 'saving':
          iconElement.textContent = '💾';
          statusElement.textContent = message || '保存中...';
          break;
        case 'saved':
          iconElement.textContent = '✅';
          statusElement.textContent = message || '已保存';
          break;
        case 'error':
          iconElement.textContent = '❌';
          statusElement.textContent = message || '出错了';
          break;
        default:
          iconElement.textContent = '💬';
          statusElement.textContent = message || 'AI Chat Memo';
      }
    }
  }

  /**
   * 更新平台信息
   */
  updatePlatform(platform: string): void {
    if (!this.element) return;

    console.log('🎨 [AI Chat Memo] Updating platform:', platform);
    this.currentPlatform = platform;

    const platformElement = this.element.querySelector('.platform-text') as HTMLElement;
    if (platformElement) {
      platformElement.textContent = platform ? `${platform} 平台` : '未检测到平台';
    }
  }

  /**
   * 打开扩展弹窗
   */
  private openPopup(): void {
    console.log('🎨 [AI Chat Memo] Opening extension popup');
    
    // 发送消息给background script打开popup
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.sendMessage({ type: 'open-popup' }).catch(error => {
        console.error('Failed to send open-popup message:', error);
      });
    }
  }

  /**
   * 销毁指示器
   */
  destroy(): void {
    console.log('🎨 [AI Chat Memo] Destroying floating indicator');
    
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    this.element = null;
    this.isVisible = false;
  }

  /**
   * 获取当前状态
   */
  getStatus(): string {
    return this.currentStatus;
  }

  /**
   * 获取当前平台
   */
  getPlatform(): string {
    return this.currentPlatform;
  }
}
