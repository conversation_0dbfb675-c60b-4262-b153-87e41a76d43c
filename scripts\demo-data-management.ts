/**
 * 数据管理系统演示脚本
 * 展示搜索、导出、同步、备份等功能的完整使用流程
 */

import { SearchEngine } from '../src/core/search/search-engine';
import { ExportManager } from '../src/core/export/export-manager';
import { SyncManager } from '../src/core/sync/sync-manager';
import { BackupManager } from '../src/core/backup/backup-manager';
import { ConversationData, MessageData } from '../src/types/conversation';

// 模拟对话数据
const mockConversations: ConversationData[] = [
  {
    id: 'conv_001',
    platform: 'ChatGPT',
    title: 'JavaScript异步编程讨论',
    url: 'https://chat.openai.com/c/123',
    timestamp: new Date('2024-01-15T10:30:00Z'),
    messages: [
      {
        id: 'msg_001',
        role: 'user',
        content: '请解释一下JavaScript中的Promise和async/await的区别',
        timestamp: new Date('2024-01-15T10:30:00Z')
      },
      {
        id: 'msg_002',
        role: 'assistant',
        content: 'Promise和async/await都是处理异步操作的方式。Promise是ES6引入的，提供了更好的异步编程体验...',
        timestamp: new Date('2024-01-15T10:31:00Z')
      }
    ],
    metadata: {
      tags: ['JavaScript', '异步编程', 'Promise']
    }
  },
  {
    id: 'conv_002',
    platform: 'Claude',
    title: 'React性能优化技巧',
    url: 'https://claude.ai/chat/456',
    timestamp: new Date('2024-01-16T14:20:00Z'),
    messages: [
      {
        id: 'msg_003',
        role: 'user',
        content: '如何优化React应用的性能？',
        timestamp: new Date('2024-01-16T14:20:00Z')
      },
      {
        id: 'msg_004',
        role: 'assistant',
        content: 'React性能优化有多种方法：1. 使用React.memo避免不必要的重渲染...',
        timestamp: new Date('2024-01-16T14:21:00Z')
      }
    ],
    metadata: {
      tags: ['React', '性能优化', '前端开发']
    }
  }
];

async function demonstrateDataManagement() {
  console.log('🚀 数据管理系统演示开始\n');

  // 1. 搜索引擎演示
  console.log('📊 1. 搜索引擎功能演示');
  console.log('=' .repeat(50));
  
  const searchEngine = new SearchEngine();
  
  // 构建索引
  console.log('构建搜索索引...');
  await searchEngine.buildIndex(mockConversations);
  
  // 执行搜索
  console.log('\n🔍 搜索 "JavaScript"：');
  const searchResults = await searchEngine.search('JavaScript', {
    limit: 10,
    sortBy: 'relevance'
  });
  
  searchResults.results.forEach((result, index) => {
    console.log(`  ${index + 1}. ${result.conversation.title}`);
    console.log(`     平台: ${result.conversation.platform}`);
    console.log(`     相关度: ${result.score.toFixed(2)}`);
    console.log(`     匹配类型: ${result.matchType}`);
    if (result.highlights.length > 0) {
      console.log(`     高亮: ${result.highlights[0]}`);
    }
    console.log('');
  });

  // 搜索统计
  const searchStats = searchEngine.getSearchStats();
  console.log('📈 搜索统计信息：');
  console.log(`  总索引数: ${searchStats.totalIndices}`);
  console.log(`  对话索引: ${searchStats.conversationIndices}`);
  console.log(`  消息索引: ${searchStats.messageIndices}`);
  console.log(`  缓存大小: ${searchStats.cacheSize}`);
  console.log('');

  // 2. 导出功能演示
  console.log('📤 2. 数据导出功能演示');
  console.log('=' .repeat(50));
  
  const exportManager = new ExportManager();
  
  // 导出为Markdown
  console.log('导出为Markdown格式...');
  const markdownResult = await exportManager.exportConversations({
    format: 'markdown',
    conversations: mockConversations,
    includeMetadata: true,
    includeTimestamps: true,
    includeTags: true
  });
  
  if (markdownResult.success) {
    console.log(`✅ Markdown导出成功:`);
    console.log(`  文件名: ${markdownResult.filename}`);
    console.log(`  大小: ${markdownResult.size} 字节`);
    console.log(`  对话数: ${markdownResult.conversationCount}`);
    console.log(`  消息数: ${markdownResult.messageCount}`);
  }

  // 导出为JSON
  console.log('\n导出为JSON格式...');
  const jsonResult = await exportManager.exportConversations({
    format: 'json',
    conversations: mockConversations,
    includeMetadata: true
  });
  
  if (jsonResult.success) {
    console.log(`✅ JSON导出成功:`);
    console.log(`  文件名: ${jsonResult.filename}`);
    console.log(`  大小: ${jsonResult.size} 字节`);
  }

  // 显示支持的格式
  console.log('\n📋 支持的导出格式：');
  const supportedFormats = exportManager.getSupportedFormats();
  supportedFormats.forEach(format => {
    console.log(`  ${format.name}: ${format.description}`);
  });

  // 估算文件大小
  console.log('\n📏 文件大小估算：');
  ['markdown', 'json', 'html', 'csv'].forEach(format => {
    const estimatedSize = exportManager.estimateFileSize(mockConversations, format as any);
    console.log(`  ${format.toUpperCase()}: ~${estimatedSize} 字节`);
  });
  console.log('');

  // 3. 同步功能演示
  console.log('🔄 3. 数据同步功能演示');
  console.log('=' .repeat(50));
  
  const syncConfig = {
    provider: 'chrome-storage' as const,
    enabled: true,
    autoSync: true,
    syncInterval: 60, // 60分钟
    settings: {
      conflictResolution: 'local' as const,
      maxRetries: 3,
      timeout: 30000
    }
  };
  
  const syncManager = new SyncManager(syncConfig);
  
  try {
    console.log('初始化同步服务...');
    await syncManager.initialize();
    console.log('✅ 同步服务初始化成功');
    
    // 执行同步
    console.log('\n执行数据同步...');
    const syncResult = await syncManager.sync(mockConversations);
    
    if (syncResult.success) {
      console.log(`✅ 同步成功:`);
      console.log(`  同步数量: ${syncResult.syncedCount}`);
      console.log(`  冲突数量: ${syncResult.conflictCount}`);
      console.log(`  错误数量: ${syncResult.errorCount}`);
      console.log(`  耗时: ${syncResult.duration}ms`);
    } else {
      console.log(`❌ 同步失败: ${syncResult.error}`);
    }
    
    // 显示同步状态
    const syncStatus = syncManager.getStatus();
    console.log('\n📊 同步状态：');
    console.log(`  已启用: ${syncStatus.isEnabled}`);
    console.log(`  已连接: ${syncStatus.isConnected}`);
    console.log(`  同步中: ${syncStatus.isSyncing}`);
    console.log(`  待同步: ${syncStatus.pendingChanges}`);
    console.log(`  总对话: ${syncStatus.totalConversations}`);
    if (syncStatus.lastSyncTime) {
      console.log(`  上次同步: ${syncStatus.lastSyncTime.toLocaleString()}`);
    }
    
  } catch (error) {
    console.log(`❌ 同步服务初始化失败: ${error}`);
  }

  // 显示支持的同步提供商
  console.log('\n🌐 支持的同步提供商：');
  const supportedProviders = SyncManager.getSupportedProviders();
  supportedProviders.forEach(provider => {
    console.log(`  ${provider.name}: ${provider.description}`);
  });
  console.log('');

  // 4. 备份功能演示
  console.log('💾 4. 数据备份功能演示');
  console.log('=' .repeat(50));
  
  const backupConfig = {
    autoBackup: true,
    backupInterval: 24, // 24小时
    maxBackups: 10,
    compressionEnabled: true,
    encryptionEnabled: false,
    backupLocation: 'local' as const
  };
  
  const backupManager = new BackupManager(backupConfig);
  
  try {
    console.log('创建备份...');
    const backupMetadata = await backupManager.createBackup(
      mockConversations,
      '演示备份 - 包含JavaScript和React相关对话',
      true
    );
    
    console.log(`✅ 备份创建成功:`);
    console.log(`  备份ID: ${backupMetadata.id}`);
    console.log(`  时间: ${backupMetadata.timestamp.toLocaleString()}`);
    console.log(`  对话数: ${backupMetadata.conversationCount}`);
    console.log(`  消息数: ${backupMetadata.messageCount}`);
    console.log(`  大小: ${backupMetadata.size} 字节`);
    console.log(`  压缩: ${backupMetadata.compressed ? '是' : '否'}`);
    console.log(`  加密: ${backupMetadata.encrypted ? '是' : '否'}`);
    
    // 显示备份列表
    console.log('\n📋 备份列表：');
    const backupList = backupManager.getBackupList();
    backupList.forEach((backup, index) => {
      console.log(`  ${index + 1}. ${backup.id}`);
      console.log(`     时间: ${backup.timestamp.toLocaleString()}`);
      console.log(`     描述: ${backup.description || '无描述'}`);
      console.log(`     大小: ${backup.size} 字节`);
    });
    
    // 备份统计
    const backupStats = backupManager.getBackupStats();
    console.log('\n📊 备份统计：');
    console.log(`  总备份数: ${backupStats.totalBackups}`);
    console.log(`  总大小: ${backupStats.totalSize} 字节`);
    console.log(`  平均大小: ${backupStats.averageSize} 字节`);
    if (backupStats.newestBackup) {
      console.log(`  最新备份: ${backupStats.newestBackup.toLocaleString()}`);
    }
    
    // 模拟恢复备份
    console.log('\n恢复备份...');
    const restoreResult = await backupManager.restoreBackup({
      backupId: backupMetadata.id,
      mergeMode: 'merge',
      restoreSettings: true,
      restoreTags: true,
      restoreStatistics: true
    });
    
    if (restoreResult.success) {
      console.log(`✅ 备份恢复成功:`);
      console.log(`  恢复对话: ${restoreResult.restoredConversations}`);
      console.log(`  跳过对话: ${restoreResult.skippedConversations}`);
      console.log(`  错误数量: ${restoreResult.errors.length}`);
      console.log(`  耗时: ${restoreResult.duration}ms`);
    } else {
      console.log(`❌ 备份恢复失败:`);
      restoreResult.errors.forEach(error => {
        console.log(`    - ${error}`);
      });
    }
    
  } catch (error) {
    console.log(`❌ 备份操作失败: ${error}`);
  }

  console.log('\n🎉 数据管理系统演示完成！');
  console.log('\n✨ 主要功能总结：');
  console.log('  🔍 全文搜索 - 支持中英文分词、相关度排序、高亮显示');
  console.log('  📤 多格式导出 - 支持Markdown、JSON、HTML、CSV、PDF等格式');
  console.log('  🔄 跨设备同步 - 支持Chrome存储、云存储等多种同步方式');
  console.log('  💾 数据备份 - 支持自动备份、压缩加密、版本管理');
  console.log('  🛡️ 数据安全 - 支持数据加密、校验和验证、冲突解决');
  console.log('  ⚡ 高性能 - 支持增量索引、缓存优化、批量操作');
}

// 运行演示
if (require.main === module) {
  demonstrateDataManagement().catch(console.error);
}

export { demonstrateDataManagement };
