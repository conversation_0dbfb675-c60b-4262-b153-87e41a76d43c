/**
 * 平台适配器管理器
 * 负责管理所有平台适配器的注册、检测和使用
 */

import { BasePlatformAdapter } from './base';
import { ChatGPTAdapter } from './chatgpt';
import { ClaudeAdapter } from './claude';
import { GeminiAdapter } from './gemini';
import { AistudioAdapter } from './aistudio';
import { MonicaAdapter } from './monica';
import { PoeAdapter } from './poe';
import { ConversationData } from '@types/conversation';
import { Logger } from '@shared/logger';

export class AdapterManager {
  private adapters: Map<string, BasePlatformAdapter> = new Map();
  private currentAdapter: BasePlatformAdapter | null = null;
  private logger: Logger;

  constructor() {
    console.log('🔧 [AI Chat Memo] AdapterManager constructor called');
    this.logger = new Logger('AdapterManager');
    this.registerAdapters();
    console.log('✅ [AI Chat Memo] AdapterManager initialized');
  }

  /**
   * 注册所有可用的适配器
   */
  private registerAdapters(): void {
    const adapters = [
      new ChatGPTAdapter(),
      new ClaudeAdapter(),
      new GeminiAdapter(),
      new AistudioAdapter(),
      new MonicaAdapter(),
      new PoeAdapter(),
    ];

    adapters.forEach(adapter => {
      this.adapters.set(adapter.platform, adapter);
      this.logger.debug(`注册适配器: ${adapter.platform}`);
    });

    this.logger.info(`已注册 ${this.adapters.size} 个平台适配器`);
  }

  /**
   * 检测当前页面适用的适配器
   */
  detectCurrentAdapter(): BasePlatformAdapter | null {
    console.log('🔍 [AI Chat Memo] detectCurrentAdapter() called, checking', this.adapters.size, 'adapters');
    console.log('🌐 [AI Chat Memo] Current URL:', window.location.href);

    for (const adapter of this.adapters.values()) {
      console.log(`🔍 [AI Chat Memo] Checking adapter: ${adapter.platform}`);
      if (adapter.isCurrentPlatform()) {
        console.log(`✅ [AI Chat Memo] 检测到平台: ${adapter.platform}`);
        this.logger.info(`检测到平台: ${adapter.platform}`);
        return adapter;
      }
    }

    console.log('❌ [AI Chat Memo] 未检测到支持的平台');
    this.logger.debug('未检测到支持的平台');
    return null;
  }

  /**
   * 获取当前活动的适配器
   */
  getCurrentAdapter(): BasePlatformAdapter | null {
    return this.currentAdapter;
  }

  /**
   * 设置当前活动的适配器
   */
  setCurrentAdapter(adapter: BasePlatformAdapter | null): void {
    if (this.currentAdapter) {
      this.currentAdapter.cleanup();
    }
    this.currentAdapter = adapter;
  }

  /**
   * 检查当前页面是否准备就绪
   */
  isPageReady(): boolean {
    if (!this.currentAdapter) {
      const adapter = this.detectCurrentAdapter();
      if (adapter) {
        this.setCurrentAdapter(adapter);
      } else {
        return false;
      }
    }

    return this.currentAdapter.isPageReady();
  }

  /**
   * 提取当前页面的会话数据
   */
  extractConversation(): ConversationData | null {
    if (!this.currentAdapter || !this.isPageReady()) {
      return null;
    }

    try {
      const conversation = this.currentAdapter.extractConversation();
      if (conversation) {
        this.logger.debug(`成功提取会话数据: ${conversation.title}`);
      }
      return conversation;
    } catch (error) {
      this.logger.error('提取会话数据失败:', error);
      return null;
    }
  }

  /**
   * 开始监听页面变化
   */
  startObserving(callback: (conversation: ConversationData) => void): boolean {
    if (!this.currentAdapter || !this.isPageReady()) {
      this.logger.warn('无法开始监听: 适配器未准备就绪');
      return false;
    }

    try {
      this.currentAdapter.startObserving(callback);
      this.logger.info(`开始监听 ${this.currentAdapter.platform} 页面变化`);
      return true;
    } catch (error) {
      this.logger.error('开始监听失败:', error);
      return false;
    }
  }

  /**
   * 停止监听页面变化
   */
  stopObserving(): void {
    if (this.currentAdapter) {
      this.currentAdapter.stopObserving();
      this.logger.info(`停止监听 ${this.currentAdapter.platform} 页面变化`);
    }
  }

  /**
   * 获取所有注册的适配器信息
   */
  getAdapterInfo(): Array<{platform: string, isActive: boolean, isReady: boolean}> {
    return Array.from(this.adapters.values()).map(adapter => ({
      platform: adapter.platform,
      isActive: adapter === this.currentAdapter,
      isReady: adapter.isCurrentPlatform() && adapter.isPageReady()
    }));
  }

  /**
   * 获取支持的平台列表
   */
  getSupportedPlatforms(): string[] {
    return Array.from(this.adapters.keys());
  }

  /**
   * 根据平台名称获取适配器
   */
  getAdapter(platform: string): BasePlatformAdapter | null {
    return this.adapters.get(platform) || null;
  }

  /**
   * 检查是否支持当前页面
   */
  isCurrentPageSupported(): boolean {
    return this.detectCurrentAdapter() !== null;
  }

  /**
   * 重新检测并设置适配器
   */
  refresh(): boolean {
    const adapter = this.detectCurrentAdapter();
    if (adapter) {
      this.setCurrentAdapter(adapter);
      return true;
    }
    return false;
  }

  /**
   * 清理所有资源
   */
  cleanup(): void {
    this.stopObserving();
    this.adapters.forEach(adapter => {
      adapter.cleanup();
    });
    this.currentAdapter = null;
    this.logger.info('适配器管理器已清理');
  }

  /**
   * 获取当前平台的统计信息
   */
  getPlatformStats(): {
    currentPlatform: string | null;
    isReady: boolean;
    supportedPlatforms: number;
    detectedPlatforms: string[];
  } {
    const detectedPlatforms = Array.from(this.adapters.values())
      .filter(adapter => adapter.isCurrentPlatform())
      .map(adapter => adapter.platform);

    return {
      currentPlatform: this.currentAdapter?.platform || null,
      isReady: this.isPageReady(),
      supportedPlatforms: this.adapters.size,
      detectedPlatforms
    };
  }

  /**
   * 测试所有适配器的检测能力
   */
  testAdapters(): {[platform: string]: {
    isCurrentPlatform: boolean;
    isPageReady: boolean;
    canExtract: boolean;
  }} {
    const results: {[platform: string]: any} = {};

    this.adapters.forEach((adapter, platform) => {
      results[platform] = {
        isCurrentPlatform: adapter.isCurrentPlatform(),
        isPageReady: adapter.isPageReady(),
        canExtract: false
      };

      if (results[platform].isCurrentPlatform && results[platform].isPageReady) {
        try {
          const conversation = adapter.extractConversation();
          results[platform].canExtract = conversation !== null;
        } catch (error) {
          this.logger.error(`测试 ${platform} 适配器失败:`, error);
          results[platform].canExtract = false;
        }
      }
    });

    return results;
  }
}

// 创建全局单例
export const adapterManager = new AdapterManager();
