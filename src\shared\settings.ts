/**
 * 简单的设置管理器
 */

import { StorageManager } from './storage';

export interface UserSettings {
  autoSave: boolean;
  platforms: string[];
  exportFormat: 'markdown' | 'pdf';
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
}

export const DEFAULT_SETTINGS: UserSettings = {
  autoSave: true,
  platforms: ['chatgpt', 'claude', 'gemini'],
  exportFormat: 'markdown',
  theme: 'auto',
  language: 'zh-CN'
};

export class SettingsManager {
  private storage: StorageManager;
  private settings: UserSettings;

  constructor(storage: StorageManager) {
    this.storage = storage;
    this.settings = { ...DEFAULT_SETTINGS };
  }

  async load(): Promise<UserSettings> {
    try {
      const saved = await this.storage.get('settings');
      if (saved) {
        this.settings = { ...DEFAULT_SETTINGS, ...saved };
      }
      return this.settings;
    } catch (error) {
      console.error('Failed to load settings:', error);
      return this.settings;
    }
  }

  async save(settings: Partial<UserSettings>): Promise<void> {
    try {
      this.settings = { ...this.settings, ...settings };
      await this.storage.set('settings', this.settings);
    } catch (error) {
      console.error('Failed to save settings:', error);
      throw error;
    }
  }

  get(): UserSettings {
    return { ...this.settings };
  }

  async reset(): Promise<void> {
    try {
      this.settings = { ...DEFAULT_SETTINGS };
      await this.storage.set('settings', this.settings);
    } catch (error) {
      console.error('Failed to reset settings:', error);
      throw error;
    }
  }
}
