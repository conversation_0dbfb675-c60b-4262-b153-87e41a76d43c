/**
 * 导出管理器单元测试
 */

import { ExportManager } from '../../../src/core/export/export-manager';
import { ConversationData } from '../../../src/types/conversation';

describe('ExportManager', () => {
  let exportManager: ExportManager;
  let mockConversations: ConversationData[];

  beforeEach(() => {
    exportManager = new ExportManager();
    
    // 创建测试数据
    mockConversations = [
      {
        id: 'conv_001',
        platform: 'ChatGPT',
        title: 'JavaScript异步编程讨论',
        url: 'https://chat.openai.com/c/123',
        timestamp: new Date('2024-01-15T10:30:00Z'),
        messages: [
          {
            id: 'msg_001',
            role: 'user',
            content: '请解释一下JavaScript中的Promise和async/await的区别',
            timestamp: new Date('2024-01-15T10:30:00Z')
          },
          {
            id: 'msg_002',
            role: 'assistant',
            content: 'Promise和async/await都是处理异步操作的方式。',
            timestamp: new Date('2024-01-15T10:31:00Z')
          }
        ],
        metadata: {
          tags: ['JavaScript', '异步编程', 'Promise']
        }
      },
      {
        id: 'conv_002',
        platform: 'Claude',
        title: 'React性能优化技巧',
        url: 'https://claude.ai/chat/456',
        timestamp: new Date('2024-01-16T14:20:00Z'),
        messages: [
          {
            id: 'msg_003',
            role: 'user',
            content: '如何优化React应用的性能？',
            timestamp: new Date('2024-01-16T14:20:00Z')
          },
          {
            id: 'msg_004',
            role: 'assistant',
            content: 'React性能优化有多种方法：使用React.memo、useMemo等。',
            timestamp: new Date('2024-01-16T14:21:00Z')
          }
        ],
        metadata: {
          tags: ['React', '性能优化', '前端开发']
        }
      }
    ];
  });

  describe('Markdown导出', () => {
    test('应该能够导出为Markdown格式', async () => {
      const result = await exportManager.exportConversations({
        format: 'markdown',
        conversations: mockConversations,
        includeMetadata: true,
        includeTimestamps: true,
        includeTags: true
      });

      expect(result.success).toBe(true);
      expect(result.filename).toMatch(/\.md$/);
      expect(result.content).toContain('# AI对话导出');
      expect(result.content).toContain('## 目录');
      expect(result.content).toContain('JavaScript异步编程讨论');
      expect(result.content).toContain('React性能优化技巧');
      expect(result.conversationCount).toBe(2);
      expect(result.messageCount).toBe(4);
    });

    test('应该包含元数据信息', async () => {
      const result = await exportManager.exportConversations({
        format: 'markdown',
        conversations: mockConversations,
        includeMetadata: true,
        includeTags: true
      });

      expect(result.content).toContain('平台: ChatGPT');
      expect(result.content).toContain('标签: JavaScript, 异步编程, Promise');
    });

    test('应该包含时间戳', async () => {
      const result = await exportManager.exportConversations({
        format: 'markdown',
        conversations: mockConversations,
        includeTimestamps: true
      });

      expect(result.content).toContain('*时间:');
    });

    test('应该支持自定义文件名', async () => {
      const customFilename = 'custom-export.md';
      const result = await exportManager.exportConversations({
        format: 'markdown',
        conversations: mockConversations,
        filename: customFilename
      });

      expect(result.filename).toBe(customFilename);
    });
  });

  describe('JSON导出', () => {
    test('应该能够导出为JSON格式', async () => {
      const result = await exportManager.exportConversations({
        format: 'json',
        conversations: mockConversations,
        includeMetadata: true
      });

      expect(result.success).toBe(true);
      expect(result.filename).toMatch(/\.json$/);
      
      const jsonData = JSON.parse(result.content!);
      expect(jsonData.exportInfo).toBeDefined();
      expect(jsonData.conversations).toHaveLength(2);
      expect(jsonData.exportInfo.conversationCount).toBe(2);
      expect(jsonData.exportInfo.messageCount).toBe(4);
    });

    test('JSON应该包含导出信息', async () => {
      const result = await exportManager.exportConversations({
        format: 'json',
        conversations: mockConversations
      });

      const jsonData = JSON.parse(result.content!);
      expect(jsonData.exportInfo.timestamp).toBeDefined();
      expect(jsonData.exportInfo.format).toBe('json');
      expect(jsonData.exportInfo.version).toBe('1.0');
    });
  });

  describe('HTML导出', () => {
    test('应该能够导出为HTML格式', async () => {
      const result = await exportManager.exportConversations({
        format: 'html',
        conversations: mockConversations,
        includeMetadata: true
      });

      expect(result.success).toBe(true);
      expect(result.filename).toMatch(/\.html$/);
      expect(result.content).toContain('<!DOCTYPE html>');
      expect(result.content).toContain('<title>AI对话导出</title>');
      expect(result.content).toContain('JavaScript异步编程讨论');
    });

    test('HTML应该包含样式', async () => {
      const result = await exportManager.exportConversations({
        format: 'html',
        conversations: mockConversations
      });

      expect(result.content).toContain('<style>');
      expect(result.content).toContain('user-message');
      expect(result.content).toContain('assistant-message');
    });
  });

  describe('CSV导出', () => {
    test('应该能够导出为CSV格式', async () => {
      const result = await exportManager.exportConversations({
        format: 'csv',
        conversations: mockConversations,
        includeTags: true
      });

      expect(result.success).toBe(true);
      expect(result.filename).toMatch(/\.csv$/);
      expect(result.content).toContain('对话ID,对话标题,平台');
      expect(result.content).toContain('conv_001');
      expect(result.content).toContain('JavaScript异步编程讨论');
    });

    test('CSV应该正确处理特殊字符', async () => {
      const conversationWithSpecialChars = {
        ...mockConversations[0],
        title: '包含"引号"和,逗号的标题',
        messages: [{
          ...mockConversations[0].messages[0],
          content: '包含换行\n和"引号"的内容'
        }]
      };

      const result = await exportManager.exportConversations({
        format: 'csv',
        conversations: [conversationWithSpecialChars]
      });

      expect(result.content).toContain('""引号""');
      expect(result.content).toContain('\\n');
    });
  });

  describe('TXT导出', () => {
    test('应该能够导出为纯文本格式', async () => {
      const result = await exportManager.exportConversations({
        format: 'txt',
        conversations: mockConversations,
        includeMetadata: true
      });

      expect(result.success).toBe(true);
      expect(result.filename).toMatch(/\.txt$/);
      expect(result.content).toContain('AI对话导出');
      expect(result.content).toContain('='.repeat(50));
      expect(result.content).toContain('[用户 1]');
      expect(result.content).toContain('[AI助手 1]');
    });
  });

  describe('PDF导出', () => {
    test('应该处理PDF导出请求', async () => {
      const result = await exportManager.exportConversations({
        format: 'pdf',
        conversations: mockConversations
      });

      expect(result.success).toBe(true);
      expect(result.filename).toMatch(/\.txt$/); // 当前实现返回txt文件
      expect(result.content).toContain('PDF导出功能需要额外的库支持');
    });
  });

  describe('过滤功能', () => {
    test('应该支持日期范围过滤', async () => {
      const result = await exportManager.exportConversations({
        format: 'json',
        conversations: mockConversations,
        dateRange: {
          start: new Date('2024-01-15T00:00:00Z'),
          end: new Date('2024-01-15T23:59:59Z')
        }
      });

      const jsonData = JSON.parse(result.content!);
      expect(jsonData.conversations).toHaveLength(1);
      expect(jsonData.conversations[0].id).toBe('conv_001');
    });

    test('应该支持平台过滤', async () => {
      const result = await exportManager.exportConversations({
        format: 'json',
        conversations: mockConversations,
        platforms: ['Claude']
      });

      const jsonData = JSON.parse(result.content!);
      expect(jsonData.conversations).toHaveLength(1);
      expect(jsonData.conversations[0].platform).toBe('Claude');
    });

    test('应该支持组合过滤', async () => {
      const result = await exportManager.exportConversations({
        format: 'json',
        conversations: mockConversations,
        dateRange: {
          start: new Date('2024-01-16T00:00:00Z'),
          end: new Date('2024-01-16T23:59:59Z')
        },
        platforms: ['Claude']
      });

      const jsonData = JSON.parse(result.content!);
      expect(jsonData.conversations).toHaveLength(1);
      expect(jsonData.conversations[0].id).toBe('conv_002');
    });
  });

  describe('工具方法', () => {
    test('应该返回支持的导出格式', () => {
      const formats = exportManager.getSupportedFormats();
      
      expect(formats).toHaveLength(6);
      expect(formats.map(f => f.format)).toContain('markdown');
      expect(formats.map(f => f.format)).toContain('json');
      expect(formats.map(f => f.format)).toContain('html');
      expect(formats.map(f => f.format)).toContain('csv');
      expect(formats.map(f => f.format)).toContain('txt');
      expect(formats.map(f => f.format)).toContain('pdf');
    });

    test('应该能够估算文件大小', () => {
      const markdownSize = exportManager.estimateFileSize(mockConversations, 'markdown');
      const jsonSize = exportManager.estimateFileSize(mockConversations, 'json');
      const htmlSize = exportManager.estimateFileSize(mockConversations, 'html');
      
      expect(markdownSize).toBeGreaterThan(0);
      expect(jsonSize).toBeGreaterThan(markdownSize);
      expect(htmlSize).toBeGreaterThan(markdownSize);
    });

    test('应该能够下载文件', () => {
      const mockResult = {
        success: true,
        filename: 'test.md',
        blob: new Blob(['test content'], { type: 'text/markdown' }),
        size: 12,
        conversationCount: 1,
        messageCount: 2
      };

      // Mock DOM elements
      const mockLink = {
        href: '',
        download: '',
        click: jest.fn()
      };
      
      const createElementSpy = jest.spyOn(document, 'createElement').mockReturnValue(mockLink as any);
      const appendChildSpy = jest.spyOn(document.body, 'appendChild').mockImplementation();
      const removeChildSpy = jest.spyOn(document.body, 'removeChild').mockImplementation();

      expect(() => exportManager.downloadFile(mockResult)).not.toThrow();
      
      expect(createElementSpy).toHaveBeenCalledWith('a');
      expect(mockLink.download).toBe('test.md');
      expect(mockLink.click).toHaveBeenCalled();
      
      createElementSpy.mockRestore();
      appendChildSpy.mockRestore();
      removeChildSpy.mockRestore();
    });
  });

  describe('错误处理', () => {
    test('应该处理不支持的格式', async () => {
      const result = await exportManager.exportConversations({
        format: 'unsupported' as any,
        conversations: mockConversations
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('不支持的导出格式');
    });

    test('应该处理空对话列表', async () => {
      const result = await exportManager.exportConversations({
        format: 'markdown',
        conversations: []
      });

      expect(result.success).toBe(true);
      expect(result.conversationCount).toBe(0);
      expect(result.messageCount).toBe(0);
    });

    test('应该处理无效的下载结果', () => {
      const invalidResult = {
        success: false,
        filename: '',
        size: 0,
        conversationCount: 0,
        messageCount: 0
      };

      expect(() => exportManager.downloadFile(invalidResult)).toThrow('导出结果无效，无法下载');
    });
  });
});
