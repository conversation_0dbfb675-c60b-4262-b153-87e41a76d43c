<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome扩展测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .result {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <h1>🔧 Chrome扩展调试工具</h1>
    
    <div class="section">
        <h2>📋 问题诊断</h2>
        <p>这个页面可以帮助你诊断Chrome扩展的消息通信问题。</p>
        
        <h3>常见的 "Could not establish connection" 错误原因：</h3>
        <ul>
            <li><strong>Content Script未加载</strong> - 当前页面不在manifest.json的匹配域名中</li>
            <li><strong>页面加载时机问题</strong> - Popup在Content Script准备好之前发送消息</li>
            <li><strong>扩展未正确安装</strong> - 扩展可能需要重新加载</li>
            <li><strong>权限问题</strong> - 缺少必要的权限配置</li>
        </ul>
    </div>

    <div class="section">
        <h2>🔍 扩展状态检查</h2>
        <button class="button" onclick="checkExtensionStatus()">检查扩展状态</button>
        <button class="button" onclick="testBackgroundConnection()">测试Background连接</button>
        <button class="button" onclick="testContentScript()">测试Content Script</button>
        <div id="status-result" class="result"></div>
    </div>

    <div class="section">
        <h2>🛠️ 解决方案</h2>
        <h3>如果遇到连接错误，请尝试以下步骤：</h3>
        <ol>
            <li><strong>重新加载扩展</strong>
                <ul>
                    <li>打开 chrome://extensions/</li>
                    <li>找到 "AI Chat Memo" 扩展</li>
                    <li>点击刷新按钮 🔄</li>
                </ul>
            </li>
            <li><strong>检查当前页面</strong>
                <ul>
                    <li>确保在支持的AI平台上（ChatGPT、Claude、Gemini等）</li>
                    <li>等待页面完全加载</li>
                </ul>
            </li>
            <li><strong>检查控制台错误</strong>
                <ul>
                    <li>按F12打开开发者工具</li>
                    <li>查看Console标签页的错误信息</li>
                </ul>
            </li>
            <li><strong>重启浏览器</strong>
                <ul>
                    <li>完全关闭Chrome浏览器</li>
                    <li>重新打开并测试</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="section">
        <h2>📝 支持的网站列表</h2>
        <p>AI Chat Memo扩展支持以下网站：</p>
        <ul>
            <li>🤖 <a href="https://chatgpt.com" target="_blank">ChatGPT (chatgpt.com)</a></li>
            <li>🤖 <a href="https://chat.openai.com" target="_blank">ChatGPT (chat.openai.com)</a></li>
            <li>🧠 <a href="https://claude.ai" target="_blank">Claude (claude.ai)</a></li>
            <li>💎 <a href="https://gemini.google.com" target="_blank">Gemini (gemini.google.com)</a></li>
            <li>🏗️ <a href="https://aistudio.google.com" target="_blank">AI Studio (aistudio.google.com)</a></li>
            <li>👩‍💼 <a href="https://monica.im" target="_blank">Monica (monica.im)</a></li>
            <li>🐾 <a href="https://poe.com" target="_blank">Poe (poe.com)</a></li>
        </ul>
    </div>

    <script>
        function log(message, type = 'info') {
            const result = document.getElementById('status-result');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            result.textContent += logEntry;
            result.className = `result ${type}`;
            result.scrollTop = result.scrollHeight;
        }

        function clearLog() {
            document.getElementById('status-result').textContent = '';
            document.getElementById('status-result').className = 'result';
        }

        async function checkExtensionStatus() {
            clearLog();
            log('🔍 开始检查扩展状态...');
            
            // 检查Chrome扩展API是否可用
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                log('❌ Chrome扩展API不可用', 'error');
                return;
            }
            
            log('✅ Chrome扩展API可用');
            log(`📍 当前页面: ${window.location.href}`);
            
            // 检查扩展ID
            if (chrome.runtime.id) {
                log(`🆔 扩展ID: ${chrome.runtime.id}`);
            } else {
                log('❌ 无法获取扩展ID', 'error');
            }
        }

        async function testBackgroundConnection() {
            clearLog();
            log('📡 测试Background Script连接...');
            
            try {
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({ type: 'ping' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                log('✅ Background Script连接成功', 'success');
                log(`📄 响应: ${JSON.stringify(response, null, 2)}`);
            } catch (error) {
                log(`❌ Background Script连接失败: ${error.message}`, 'error');
                log('💡 建议: 请重新加载扩展或重启浏览器');
            }
        }

        async function testContentScript() {
            clearLog();
            log('📄 测试Content Script连接...');
            
            try {
                // 获取当前标签页
                const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
                if (tabs.length === 0) {
                    log('❌ 无法获取当前标签页', 'error');
                    return;
                }
                
                const currentTab = tabs[0];
                log(`📍 当前标签页: ${currentTab.url}`);
                
                // 发送消息到content script
                const response = await chrome.tabs.sendMessage(currentTab.id, { type: 'ping' });
                
                log('✅ Content Script连接成功', 'success');
                log(`📄 响应: ${JSON.stringify(response, null, 2)}`);
            } catch (error) {
                log(`❌ Content Script连接失败: ${error.message}`, 'error');
                
                if (error.message.includes('Could not establish connection')) {
                    log('💡 可能原因:', 'error');
                    log('   • 当前页面不支持（不在manifest.json的匹配列表中）');
                    log('   • Content Script还未加载完成');
                    log('   • 页面需要刷新');
                }
            }
        }

        // 页面加载完成后自动检查
        window.addEventListener('load', () => {
            log('🚀 页面加载完成，开始自动检查...');
            checkExtensionStatus();
        });
    </script>
</body>
</html>
