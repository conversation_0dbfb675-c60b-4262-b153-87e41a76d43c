/**
 * 性能测试脚本
 * 测试搜索引擎、导出管理器等核心模块的性能
 */

import { SearchEngine } from '../src/core/search/search-engine';
import { ExportManager } from '../src/core/export/export-manager';
import { BackupManager } from '../src/core/backup/backup-manager';
import { ConversationData, MessageData } from '../src/types/conversation';

// 性能测试结果接口
interface PerformanceResult {
  operation: string;
  dataSize: number;
  duration: number;
  memoryUsage?: number;
  throughput?: number;
}

class PerformanceTester {
  private results: PerformanceResult[] = [];

  /**
   * 生成测试数据
   */
  generateTestData(conversationCount: number, messagesPerConversation: number): ConversationData[] {
    const conversations: ConversationData[] = [];
    const platforms = ['ChatGPT', 'Claude', 'Gemini', 'Aistudio', 'Monica', 'Poe'];
    const topics = [
      'JavaScript编程', 'React开发', 'Python数据科学', 'AI机器学习', 
      '前端优化', '后端架构', '数据库设计', '系统设计',
      '算法与数据结构', '网络安全', '云计算', '移动开发'
    ];

    for (let i = 0; i < conversationCount; i++) {
      const messages: MessageData[] = [];
      const topic = topics[i % topics.length];
      
      for (let j = 0; j < messagesPerConversation; j++) {
        // 用户消息
        messages.push({
          id: `msg_${i}_${j}_user`,
          role: 'user',
          content: `关于${topic}的问题 ${j + 1}：这是一个测试问题，包含一些关键词如性能、优化、实现、原理等。`,
          timestamp: new Date(Date.now() - (conversationCount - i) * 86400000 + j * 60000)
        });

        // AI回答
        messages.push({
          id: `msg_${i}_${j}_ai`,
          role: 'assistant',
          content: `关于${topic}的回答 ${j + 1}：这是一个详细的回答，包含技术细节、代码示例、最佳实践等内容。回答内容较长以模拟真实场景，包含多个段落和技术术语。`,
          timestamp: new Date(Date.now() - (conversationCount - i) * 86400000 + j * 60000 + 30000)
        });
      }

      conversations.push({
        id: `conv_${i.toString().padStart(6, '0')}`,
        platform: platforms[i % platforms.length] as any,
        title: `${topic}深度讨论 ${i + 1}`,
        url: `https://example.com/chat/${i}`,
        timestamp: new Date(Date.now() - (conversationCount - i) * 86400000),
        messages,
        metadata: {
          tags: [topic, '测试', '性能', `标签${i % 10}`]
        }
      });
    }

    return conversations;
  }

  /**
   * 测量内存使用
   */
  measureMemory(): number {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  /**
   * 执行性能测试
   */
  async runTest(
    operation: string,
    testFunction: () => Promise<any>,
    dataSize: number
  ): Promise<PerformanceResult> {
    const startMemory = this.measureMemory();
    const startTime = performance.now();

    await testFunction();

    const endTime = performance.now();
    const endMemory = this.measureMemory();
    
    const duration = endTime - startTime;
    const memoryUsage = endMemory - startMemory;
    const throughput = dataSize / (duration / 1000); // 每秒处理的数据量

    const result: PerformanceResult = {
      operation,
      dataSize,
      duration,
      memoryUsage: memoryUsage > 0 ? memoryUsage : undefined,
      throughput
    };

    this.results.push(result);
    return result;
  }

  /**
   * 测试搜索引擎性能
   */
  async testSearchEngine(): Promise<void> {
    console.log('\n🔍 搜索引擎性能测试');
    console.log('='.repeat(50));

    const searchEngine = new SearchEngine();
    
    // 测试不同数据规模
    const testSizes = [
      { conversations: 100, messages: 5 },
      { conversations: 500, messages: 10 },
      { conversations: 1000, messages: 15 },
      { conversations: 2000, messages: 20 }
    ];

    for (const size of testSizes) {
      const testData = this.generateTestData(size.conversations, size.messages);
      const totalMessages = size.conversations * size.messages * 2; // 用户+AI消息

      console.log(`\n📊 测试规模: ${size.conversations}个对话, ${totalMessages}条消息`);

      // 测试索引构建
      const indexResult = await this.runTest(
        '索引构建',
        async () => await searchEngine.buildIndex(testData),
        totalMessages
      );
      console.log(`  索引构建: ${indexResult.duration.toFixed(2)}ms, 吞吐量: ${indexResult.throughput?.toFixed(0)}条/秒`);

      // 测试搜索性能
      const searchQueries = ['JavaScript', '性能优化', 'React开发', 'Python', '算法'];
      
      for (const query of searchQueries) {
        const searchResult = await this.runTest(
          `搜索-${query}`,
          async () => await searchEngine.search(query),
          testData.length
        );
        console.log(`  搜索"${query}": ${searchResult.duration.toFixed(2)}ms`);
      }

      // 测试复杂搜索
      const complexResult = await this.runTest(
        '复杂搜索',
        async () => await searchEngine.search('JavaScript React 性能优化', {
          limit: 50,
          sortBy: 'relevance',
          filters: {
            platforms: ['ChatGPT', 'Claude'],
            dateRange: {
              start: new Date(Date.now() - 30 * 86400000),
              end: new Date()
            }
          }
        }),
        testData.length
      );
      console.log(`  复杂搜索: ${complexResult.duration.toFixed(2)}ms`);

      searchEngine.clearCache();
    }
  }

  /**
   * 测试导出管理器性能
   */
  async testExportManager(): Promise<void> {
    console.log('\n📤 导出管理器性能测试');
    console.log('='.repeat(50));

    const exportManager = new ExportManager();
    const testSizes = [100, 500, 1000, 2000];

    for (const size of testSizes) {
      const testData = this.generateTestData(size, 10);
      console.log(`\n📊 测试规模: ${size}个对话`);

      // 测试不同导出格式
      const formats: Array<'markdown' | 'json' | 'html' | 'csv' | 'txt'> = 
        ['markdown', 'json', 'html', 'csv', 'txt'];

      for (const format of formats) {
        const exportResult = await this.runTest(
          `导出-${format}`,
          async () => await exportManager.exportConversations({
            format,
            conversations: testData,
            includeMetadata: true,
            includeTags: true,
            includeTimestamps: true
          }),
          testData.length
        );
        console.log(`  ${format}导出: ${exportResult.duration.toFixed(2)}ms, 吞吐量: ${exportResult.throughput?.toFixed(0)}个/秒`);
      }

      // 测试文件大小估算
      const estimateResult = await this.runTest(
        '大小估算',
        async () => {
          formats.forEach(format => {
            exportManager.estimateFileSize(testData, format);
          });
        },
        testData.length * formats.length
      );
      console.log(`  大小估算: ${estimateResult.duration.toFixed(2)}ms`);
    }
  }

  /**
   * 测试备份管理器性能
   */
  async testBackupManager(): Promise<void> {
    console.log('\n💾 备份管理器性能测试');
    console.log('='.repeat(50));

    const backupManager = new BackupManager({
      autoBackup: false,
      backupInterval: 24,
      maxBackups: 10,
      compression: true,
      encryption: false
    });

    const testSizes = [100, 500, 1000];

    for (const size of testSizes) {
      const testData = this.generateTestData(size, 10);
      console.log(`\n📊 测试规模: ${size}个对话`);

      // 测试备份创建
      const createResult = await this.runTest(
        '备份创建',
        async () => await backupManager.createBackup(testData, `性能测试备份-${size}`),
        testData.length
      );
      console.log(`  备份创建: ${createResult.duration.toFixed(2)}ms, 吞吐量: ${createResult.throughput?.toFixed(0)}个/秒`);

      // 获取备份列表
      const backups = await backupManager.getBackupList();
      const latestBackup = backups[0];

      // 测试备份恢复
      const restoreResult = await this.runTest(
        '备份恢复',
        async () => await backupManager.restoreBackup(latestBackup.id),
        testData.length
      );
      console.log(`  备份恢复: ${restoreResult.duration.toFixed(2)}ms, 吞吐量: ${restoreResult.throughput?.toFixed(0)}个/秒`);

      // 测试备份导出
      const exportResult = await this.runTest(
        '备份导出',
        async () => await backupManager.exportBackup(latestBackup.id),
        testData.length
      );
      console.log(`  备份导出: ${exportResult.duration.toFixed(2)}ms`);

      // 清理测试备份
      await backupManager.deleteBackup(latestBackup.id);
    }

    backupManager.destroy();
  }

  /**
   * 测试内存使用情况
   */
  async testMemoryUsage(): Promise<void> {
    console.log('\n🧠 内存使用测试');
    console.log('='.repeat(50));

    const searchEngine = new SearchEngine();
    const initialMemory = this.measureMemory();

    // 逐步增加数据量，观察内存使用
    const steps = [100, 500, 1000, 2000, 5000];
    
    for (const step of steps) {
      const testData = this.generateTestData(step, 10);
      
      await searchEngine.buildIndex(testData);
      const currentMemory = this.measureMemory();
      const memoryIncrease = currentMemory - initialMemory;
      
      console.log(`📊 ${step}个对话: 内存增加 ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
      
      // 执行一些搜索操作
      await searchEngine.search('JavaScript');
      await searchEngine.search('React 性能');
      await searchEngine.search('Python 数据科学');
    }

    // 清理缓存并观察内存释放
    const beforeClear = this.measureMemory();
    searchEngine.clearCache();
    
    // 强制垃圾回收（如果可用）
    if (typeof global !== 'undefined' && (global as any).gc) {
      (global as any).gc();
    }
    
    setTimeout(() => {
      const afterClear = this.measureMemory();
      const memoryFreed = beforeClear - afterClear;
      console.log(`🧹 缓存清理: 释放内存 ${(memoryFreed / 1024 / 1024).toFixed(2)}MB`);
    }, 1000);
  }

  /**
   * 生成性能报告
   */
  generateReport(): void {
    console.log('\n📈 性能测试报告');
    console.log('='.repeat(50));

    // 按操作类型分组
    const groupedResults = this.results.reduce((groups, result) => {
      const operation = result.operation.split('-')[0];
      if (!groups[operation]) {
        groups[operation] = [];
      }
      groups[operation].push(result);
      return groups;
    }, {} as Record<string, PerformanceResult[]>);

    Object.entries(groupedResults).forEach(([operation, results]) => {
      console.log(`\n${operation}:`);
      
      const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
      const maxDuration = Math.max(...results.map(r => r.duration));
      const minDuration = Math.min(...results.map(r => r.duration));
      
      console.log(`  平均耗时: ${avgDuration.toFixed(2)}ms`);
      console.log(`  最大耗时: ${maxDuration.toFixed(2)}ms`);
      console.log(`  最小耗时: ${minDuration.toFixed(2)}ms`);
      
      if (results[0].throughput) {
        const avgThroughput = results.reduce((sum, r) => sum + (r.throughput || 0), 0) / results.length;
        console.log(`  平均吞吐量: ${avgThroughput.toFixed(0)}项/秒`);
      }
    });

    // 性能建议
    console.log('\n💡 性能优化建议:');
    
    const searchResults = groupedResults['搜索'] || [];
    if (searchResults.length > 0) {
      const avgSearchTime = searchResults.reduce((sum, r) => sum + r.duration, 0) / searchResults.length;
      if (avgSearchTime > 100) {
        console.log('  - 搜索性能较慢，建议优化索引结构或增加缓存');
      }
    }

    const exportResults = groupedResults['导出'] || [];
    if (exportResults.length > 0) {
      const avgExportTime = exportResults.reduce((sum, r) => sum + r.duration, 0) / exportResults.length;
      if (avgExportTime > 1000) {
        console.log('  - 导出性能较慢，建议使用流式处理或分批导出');
      }
    }
  }

  /**
   * 运行所有性能测试
   */
  async runAllTests(): Promise<void> {
    console.log('🚀 开始性能测试...\n');
    
    try {
      await this.testSearchEngine();
      await this.testExportManager();
      await this.testBackupManager();
      await this.testMemoryUsage();
      
      this.generateReport();
      
      console.log('\n✅ 性能测试完成！');
    } catch (error) {
      console.error('❌ 性能测试失败:', error);
    }
  }
}

// 运行性能测试
if (typeof require !== 'undefined' && require.main === module) {
  const tester = new PerformanceTester();
  tester.runAllTests().catch(console.error);
}

export { PerformanceTester };
