/**
 * Monica 平台适配器
 * 专门处理 monica.im 的页面结构和内容提取
 */

import { BasePlatformAdapter, PlatformSelectors } from './base';
import { ConversationData, MessageData } from '@types/conversation';

export class MonicaAdapter extends BasePlatformAdapter {
  readonly platform = 'Monica';
  readonly selectors: PlatformSelectors = {
    // 基于Monica.im结构模式 (2025-08-01)
    conversationContainer: '[role="main"], main, .conversation, [data-testid*="conversation"], .chat-container, .monica-chat',
    messageElements: '[data-testid*="message"], .message, .conversation-turn, [role="article"], article, .chat-message, .monica-message',
    userMessage: '[data-author="user"], .user-message, .prompt, .human-message, [data-role="user"], .user-input, .monica-user',
    assistantMessage: '[data-author="assistant"], .assistant-message, .response, .ai-message, [data-role="assistant"], .model-response, .monica-response',
    messageContent: '.message-content, .prose, .markdown, .text-content, .whitespace-pre-wrap, p, div[class*="content"], .response-text',
    messageInput: 'textarea[placeholder*="Enter"], textarea[placeholder*="Ask"], textarea[placeholder*="Message"], textarea[data-testid*="input"], .chat-input textarea, textarea, input[type="text"]',
    sendButton: '[data-testid*="send"], .send-button, button[type="submit"], button[aria-label*="Send"], button[title*="Send"]',
    conversationTitle: '.conversation-title, h1, .chat-title, .title, .monica-title',
    conversationList: '.conversation-list li, .chat-list-item, nav li, .sidebar li'
  };

  protected features = {
    hasConversationList: true,
    hasMessageTimestamps: true,
    hasCodeBlocks: true,
    hasImageSupport: true,
    hasFileUpload: true,
    hasConversationExport: false
  };

  isPageReady(): boolean {
    // 检查是否在Monica域名
    if (!this.isCurrentPlatform()) {
      return false;
    }

    // 检查是否在会话页面
    const hasConversationContainer = document.querySelector(this.selectors.conversationContainer) !== null;
    const hasMessageInput = document.querySelector(this.selectors.messageInput) !== null;
    const isConversationUrl = window.location.pathname.includes('/chat') || 
                             window.location.pathname === '/' ||
                             window.location.pathname.includes('/conversation') ||
                             window.location.pathname.includes('/monica');
    
    // 等待页面完全加载
    const isPageLoaded = document.readyState === 'complete' || 
                        document.readyState === 'interactive';
    
    this.logger.debug('Monica页面检查状态:', {
      hasConversationContainer,
      hasMessageInput,
      isConversationUrl,
      isPageLoaded,
      currentUrl: window.location.href
    });
    
    return hasConversationContainer && hasMessageInput && isConversationUrl && isPageLoaded;
  }

  extractConversation(): ConversationData | null {
    try {
      this.logger.debug('开始提取Monica对话内容');

      // 查找对话容器
      const conversationContainer = document.querySelector(this.selectors.conversationContainer);
      if (!conversationContainer) {
        this.logger.warn('未找到对话容器');
        return null;
      }

      // 提取消息
      const messages = this.extractMessages(conversationContainer);
      if (messages.length === 0) {
        this.logger.warn('未找到任何消息');
        return null;
      }

      // 提取会话标题
      const title = this.extractConversationTitle();

      // 生成会话ID
      const conversationId = this.generateConversationId();

      const conversation: ConversationData = {
        id: conversationId,
        platform: this.platform,
        title: title || '未命名对话',
        messages,
        url: window.location.href,
        timestamp: new Date().toISOString(),
        metadata: {
          userAgent: navigator.userAgent,
          pageTitle: document.title,
          extractedAt: new Date().toISOString(),
          messageCount: messages.length
        }
      };

      this.logger.info('Monica对话提取成功:', {
        conversationId,
        messageCount: messages.length,
        title
      });

      return conversation;

    } catch (error) {
      this.logger.error('Monica对话提取失败:', error);
      return null;
    }
  }

  protected determineMessageRole(element: Element): 'user' | 'assistant' | null {
    const className = element.className.toLowerCase();
    const textContent = element.textContent?.toLowerCase() || '';
    
    // 检查用户消息特征
    if (element.matches(this.selectors.userMessage)) {
      return 'user';
    }
    
    // 检查助手消息特征
    if (element.matches(this.selectors.assistantMessage)) {
      return 'assistant';
    }

    // 基于类名判断
    if (className.includes('user') || className.includes('human') || className.includes('prompt') || 
        className.includes('input') || className.includes('monica-user')) {
      return 'user';
    }
    
    if (className.includes('assistant') || className.includes('ai') || className.includes('model') || 
        className.includes('response') || className.includes('monica') || className.includes('bot')) {
      return 'assistant';
    }

    // 基于数据属性判断
    const dataRole = element.getAttribute('data-role') || element.getAttribute('data-author');
    if (dataRole) {
      if (dataRole.includes('user') || dataRole.includes('human')) {
        return 'user';
      }
      if (dataRole.includes('assistant') || dataRole.includes('ai') || dataRole.includes('model') || dataRole.includes('monica')) {
        return 'assistant';
      }
    }

    // Monica特有的判断逻辑
    const parentElement = element.parentElement;
    if (parentElement) {
      const parentClass = parentElement.className.toLowerCase();
      if (parentClass.includes('user') || parentClass.includes('prompt')) {
        return 'user';
      }
      if (parentClass.includes('response') || parentClass.includes('assistant') || parentClass.includes('monica')) {
        return 'assistant';
      }
    }

    // 基于位置判断（Monica通常用户消息在右侧，助手消息在左侧）
    const computedStyle = window.getComputedStyle(element);
    const textAlign = computedStyle.textAlign;
    const marginLeft = computedStyle.marginLeft;
    const marginRight = computedStyle.marginRight;
    
    if (textAlign === 'right' || marginLeft === 'auto') {
      return 'user';
    }
    if (textAlign === 'left' || marginRight === 'auto') {
      return 'assistant';
    }

    this.logger.debug('无法确定消息角色:', {
      className,
      tagName: element.tagName,
      textPreview: textContent.slice(0, 50)
    });

    return null;
  }

  protected extractConversationTitle(): string {
    try {
      const titleElement = document.querySelector(this.selectors.conversationTitle);
      if (titleElement) {
        const title = titleElement.textContent?.trim();
        if (title && title !== 'Monica' && title !== 'Monica AI') {
          return title;
        }
      }

      // 尝试从页面标题提取
      const pageTitle = document.title;
      if (pageTitle && pageTitle !== 'Monica' && pageTitle !== 'Monica AI') {
        return pageTitle.replace(' - Monica', '').replace(' - Monica AI', '').trim();
      }

      // 尝试从第一条用户消息生成标题
      const firstUserMessage = document.querySelector(this.selectors.userMessage);
      if (firstUserMessage) {
        const content = firstUserMessage.textContent?.trim();
        if (content) {
          return content.slice(0, 50) + (content.length > 50 ? '...' : '');
        }
      }

      return '新对话';

    } catch (error) {
      this.logger.error('提取对话标题失败:', error);
      return '未命名对话';
    }
  }

  protected extractMessagesFallback(container: Element): MessageData[] {
    const messages: MessageData[] = [];
    
    try {
      // Monica特有的备用提取策略
      const textElements = container.querySelectorAll('div, p, article, section, span');
      
      textElements.forEach((element, index) => {
        const text = element.textContent?.trim();
        if (text && text.length > 10) {
          // Monica特有的角色判断逻辑
          let role: 'user' | 'assistant' = 'assistant';
          
          // 基于样式判断
          const computedStyle = window.getComputedStyle(element);
          const backgroundColor = computedStyle.backgroundColor;
          const textAlign = computedStyle.textAlign;
          
          // 用户消息通常有不同的背景色或右对齐
          if (textAlign === 'right' || backgroundColor !== 'rgba(0, 0, 0, 0)') {
            role = 'user';
          }
          
          // 如果包含问号或者是较短的文本，可能是用户消息
          if (text.includes('?') || text.length < 100) {
            role = 'user';
          }
          
          const message: MessageData = {
            id: this.generateMessageId(index, role),
            role,
            content: text,
            timestamp: new Date().toISOString(),
            metadata: {
              elementIndex: index,
              extractedFrom: 'fallback-monica',
              className: element.className,
              textLength: text.length,
              tagName: element.tagName.toLowerCase(),
              textAlign: computedStyle.textAlign,
              backgroundColor: computedStyle.backgroundColor
            }
          };
          
          messages.push(message);
        }
      });
      
    } catch (error) {
      this.logger.error('Monica备用消息提取失败:', error);
    }
    
    return messages;
  }
}
