/**
 * 简化的Claude页面分析测试
 */

import { chromium } from 'playwright';

async function testClaudeAnalysis() {
  console.log('🚀 开始简化的Claude分析测试...');

  let browser;
  try {
    console.log('📱 启动浏览器...');
    browser = await chromium.launch({
      headless: false,
      slowMo: 500
    });

    console.log('📄 创建新页面...');
    const page = await browser.newPage();

    console.log('🌐 访问Claude网站...');
    await page.goto('https://claude.ai', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });

    console.log('⏰ 等待页面加载...');
    await page.waitForTimeout(3000);

    console.log('📊 分析页面结构...');
    
    // 检查基本页面信息
    const title = await page.title();
    const url = page.url();
    console.log(`页面标题: ${title}`);
    console.log(`当前URL: ${url}`);

    // 查找可能的消息容器
    const messageContainers = await page.$$eval('*', (elements) => {
      return elements
        .filter(el => {
          const text = el.textContent?.toLowerCase() || '';
          const className = el.className?.toLowerCase() || '';
          const tagName = el.tagName.toLowerCase();
          
          // 查找可能包含对话的元素
          return (
            (text.includes('message') || text.includes('conversation') || text.includes('chat')) ||
            (className.includes('message') || className.includes('conversation') || className.includes('chat')) ||
            (tagName === 'main' || tagName === 'article' || tagName === 'section')
          ) && el.children.length > 0;
        })
        .slice(0, 10)
        .map((el, index) => ({
          index,
          tagName: el.tagName,
          className: el.className,
          id: el.id,
          childCount: el.children.length,
          textPreview: el.textContent?.slice(0, 100) || ''
        }));
    });

    console.log(`找到 ${messageContainers.length} 个可能的消息容器:`);
    messageContainers.forEach(container => {
      console.log(`  ${container.index + 1}. <${container.tagName}> class="${container.className}" id="${container.id}"`);
      console.log(`     子元素: ${container.childCount}, 内容预览: ${container.textPreview}...`);
    });

    // 查找输入框
    const inputs = await page.$$eval('input, textarea', (elements) => {
      return elements.map((el, index) => ({
        index,
        tagName: el.tagName,
        type: el.getAttribute('type') || '',
        placeholder: el.getAttribute('placeholder') || '',
        className: el.className,
        id: el.id
      }));
    });

    console.log(`\n找到 ${inputs.length} 个输入元素:`);
    inputs.forEach(input => {
      console.log(`  ${input.index + 1}. <${input.tagName}> type="${input.type}" placeholder="${input.placeholder}"`);
      console.log(`     class="${input.className}" id="${input.id}"`);
    });

    // 查找按钮
    const buttons = await page.$$eval('button', (elements) => {
      return elements.map((el, index) => ({
        index,
        textContent: el.textContent?.trim() || '',
        className: el.className,
        id: el.id,
        type: el.getAttribute('type') || ''
      }));
    });

    console.log(`\n找到 ${buttons.length} 个按钮:`);
    buttons.slice(0, 10).forEach(button => {
      console.log(`  ${button.index + 1}. "${button.textContent}" type="${button.type}"`);
      console.log(`     class="${button.className}" id="${button.id}"`);
    });

    // 截图
    console.log('\n📸 保存页面截图...');
    await page.screenshot({ 
      path: 'analysis/screenshots/claude-simple.png',
      fullPage: true 
    });

    console.log('\n✅ 简化分析完成！');

  } catch (error) {
    console.error('❌ 分析失败:', error);
  } finally {
    if (browser) {
      console.log('🔒 关闭浏览器...');
      await browser.close();
    }
  }
}

if (require.main === module) {
  testClaudeAnalysis().catch(console.error);
}
