/**
 * 同步管理器单元测试
 */

import { SyncManager } from '../../../src/core/sync/sync-manager';
import { ConversationData } from '../../../src/types/conversation';
import { mockChromeAPI } from '../../setup';

describe('SyncManager', () => {
  let syncManager: SyncManager;
  let mockConversations: ConversationData[];

  beforeEach(() => {
    const config = {
      provider: 'chrome-storage' as const,
      enabled: true,
      autoSync: false, // 测试中禁用自动同步
      syncInterval: 60,
      settings: {
        conflictResolution: 'local' as const,
        maxRetries: 3,
        timeout: 30000
      }
    };

    syncManager = new SyncManager(config);
    
    // 创建测试数据
    mockConversations = [
      {
        id: 'conv_001',
        platform: 'ChatGPT',
        title: 'JavaScript异步编程讨论',
        url: 'https://chat.openai.com/c/123',
        timestamp: new Date('2024-01-15T10:30:00Z'),
        messages: [
          {
            id: 'msg_001',
            role: 'user',
            content: '请解释一下JavaScript中的Promise和async/await的区别',
            timestamp: new Date('2024-01-15T10:30:00Z')
          }
        ],
        metadata: {
          tags: ['JavaScript', '异步编程']
        }
      }
    ];
  });

  afterEach(() => {
    syncManager.destroy();
    jest.clearAllMocks();
  });

  describe('初始化', () => {
    test('应该能够初始化Chrome存储同步', async () => {
      mockChromeAPI.storage.sync.get.mockResolvedValue({ test: 'value' });
      
      await expect(syncManager.initialize()).resolves.not.toThrow();
      
      const status = syncManager.getStatus();
      expect(status.isConnected).toBe(true);
    });

    test('应该处理Chrome存储不可用的情况', async () => {
      // 临时移除chrome.storage.sync
      const originalSync = (global as any).chrome.storage.sync;
      delete (global as any).chrome.storage.sync;
      
      await expect(syncManager.initialize()).rejects.toThrow('Chrome存储API不可用');
      
      // 恢复
      (global as any).chrome.storage.sync = originalSync;
    });

    test('应该处理不支持的同步提供商', async () => {
      const invalidConfig = {
        provider: 'invalid-provider' as any,
        enabled: true,
        autoSync: false,
        syncInterval: 60
      };
      
      const invalidSyncManager = new SyncManager(invalidConfig);
      
      await expect(invalidSyncManager.initialize()).rejects.toThrow('不支持的同步提供商');
      
      invalidSyncManager.destroy();
    });
  });

  describe('同步功能', () => {
    beforeEach(async () => {
      mockChromeAPI.storage.sync.get.mockResolvedValue({ conversations: [] });
      await syncManager.initialize();
    });

    test('应该能够执行同步', async () => {
      mockChromeAPI.storage.sync.get.mockResolvedValue({ conversations: [] });
      mockChromeAPI.storage.sync.set.mockResolvedValue(undefined);
      
      const result = await syncManager.sync(mockConversations);
      
      expect(result.success).toBe(true);
      expect(result.syncedCount).toBe(1);
      expect(result.conflictCount).toBe(0);
      expect(result.errorCount).toBe(0);
    });

    test('应该处理同步冲突', async () => {
      const remoteConversation = {
        ...mockConversations[0],
        timestamp: new Date('2024-01-15T11:00:00Z'), // 不同的时间戳
        title: '修改后的标题'
      };
      
      mockChromeAPI.storage.sync.get.mockResolvedValue({ 
        conversations: [remoteConversation] 
      });
      
      const result = await syncManager.sync(mockConversations);
      
      // 由于配置为local优先，应该上传本地版本
      expect(result.success).toBe(true);
      expect(mockChromeAPI.storage.sync.set).toHaveBeenCalled();
    });

    test('应该处理网络错误', async () => {
      mockChromeAPI.storage.sync.get.mockRejectedValue(new Error('网络错误'));
      
      const result = await syncManager.sync(mockConversations);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('网络错误');
    });

    test('应该防止并发同步', async () => {
      mockChromeAPI.storage.sync.get.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ conversations: [] }), 100))
      );
      
      const promise1 = syncManager.sync(mockConversations);
      const promise2 = syncManager.sync(mockConversations);
      
      await expect(promise2).rejects.toThrow('同步正在进行中');
      await promise1; // 等待第一个同步完成
    });
  });

  describe('冲突解决', () => {
    beforeEach(async () => {
      await syncManager.initialize();
    });

    test('应该支持本地优先策略', async () => {
      const remoteConversation = {
        ...mockConversations[0],
        title: '远程标题'
      };
      
      mockChromeAPI.storage.sync.get.mockResolvedValue({ 
        conversations: [remoteConversation] 
      });
      
      const result = await syncManager.sync(mockConversations);
      
      expect(result.success).toBe(true);
      // 验证上传了本地版本
      const setCall = mockChromeAPI.storage.sync.set.mock.calls[0];
      expect(setCall[0].conversations[0].title).toBe('JavaScript异步编程讨论');
    });

    test('应该支持远程优先策略', async () => {
      // 更新配置为远程优先
      syncManager.updateConfig({
        settings: {
          conflictResolution: 'remote',
          maxRetries: 3,
          timeout: 30000
        }
      });
      
      const remoteConversation = {
        ...mockConversations[0],
        title: '远程标题'
      };
      
      mockChromeAPI.storage.sync.get.mockResolvedValue({ 
        conversations: [remoteConversation] 
      });
      
      let downloadedConversation: ConversationData | null = null;
      syncManager.on('conversationDownloaded', (conv) => {
        downloadedConversation = conv;
      });
      
      const result = await syncManager.sync(mockConversations);
      
      expect(result.success).toBe(true);
      expect(downloadedConversation?.title).toBe('远程标题');
    });

    test('应该支持合并策略', async () => {
      syncManager.updateConfig({
        settings: {
          conflictResolution: 'merge',
          maxRetries: 3,
          timeout: 30000
        }
      });
      
      const remoteConversation = {
        ...mockConversations[0],
        timestamp: new Date('2024-01-15T09:00:00Z'), // 更早的时间
        title: '远程标题'
      };
      
      mockChromeAPI.storage.sync.get.mockResolvedValue({ 
        conversations: [remoteConversation] 
      });
      
      const result = await syncManager.sync(mockConversations);
      
      expect(result.success).toBe(true);
      // 由于本地时间更新，应该使用本地版本
      const setCall = mockChromeAPI.storage.sync.set.mock.calls[0];
      expect(setCall[0].conversations[0].title).toBe('JavaScript异步编程讨论');
    });
  });

  describe('状态管理', () => {
    test('应该正确更新同步状态', async () => {
      let status = syncManager.getStatus();
      expect(status.isConnected).toBe(false);
      expect(status.isSyncing).toBe(false);
      
      await syncManager.initialize();
      
      status = syncManager.getStatus();
      expect(status.isConnected).toBe(true);
    });

    test('应该跟踪待同步更改', () => {
      syncManager.markForSync('conv_001');
      syncManager.markForSync('conv_002');
      
      const status = syncManager.getStatus();
      expect(status.pendingChanges).toBe(2);
    });

    test('应该在同步成功后清理待同步更改', async () => {
      await syncManager.initialize();
      
      syncManager.markForSync('conv_001');
      expect(syncManager.getStatus().pendingChanges).toBe(1);
      
      mockChromeAPI.storage.sync.get.mockResolvedValue({ conversations: [] });
      await syncManager.sync(mockConversations);
      
      expect(syncManager.getStatus().pendingChanges).toBe(0);
    });
  });

  describe('配置管理', () => {
    test('应该能够更新配置', () => {
      const newConfig = {
        autoSync: true,
        syncInterval: 120
      };
      
      syncManager.updateConfig(newConfig);
      
      const config = syncManager.getConfig();
      expect(config.autoSync).toBe(true);
      expect(config.syncInterval).toBe(120);
    });

    test('应该在启用自动同步时启动定时器', () => {
      const startAutoSyncSpy = jest.spyOn(syncManager as any, 'startAutoSync');
      
      syncManager.updateConfig({ autoSync: true });
      
      expect(startAutoSyncSpy).toHaveBeenCalled();
      
      startAutoSyncSpy.mockRestore();
    });

    test('应该在禁用自动同步时停止定时器', () => {
      const stopAutoSyncSpy = jest.spyOn(syncManager as any, 'stopAutoSync');
      
      syncManager.updateConfig({ autoSync: false });
      
      expect(stopAutoSyncSpy).toHaveBeenCalled();
      
      stopAutoSyncSpy.mockRestore();
    });
  });

  describe('事件系统', () => {
    test('应该发出连接事件', async () => {
      let connectedEventFired = false;
      syncManager.on('connected', () => {
        connectedEventFired = true;
      });
      
      await syncManager.initialize();
      
      expect(connectedEventFired).toBe(true);
    });

    test('应该发出同步开始和结束事件', async () => {
      await syncManager.initialize();
      
      let syncStartFired = false;
      let syncEndFired = false;
      
      syncManager.on('syncStart', () => {
        syncStartFired = true;
      });
      
      syncManager.on('syncEnd', () => {
        syncEndFired = true;
      });
      
      mockChromeAPI.storage.sync.get.mockResolvedValue({ conversations: [] });
      await syncManager.sync(mockConversations);
      
      expect(syncStartFired).toBe(true);
      expect(syncEndFired).toBe(true);
    });

    test('应该发出同步成功事件', async () => {
      await syncManager.initialize();
      
      let syncResult: any = null;
      syncManager.on('syncSuccess', (result) => {
        syncResult = result;
      });
      
      mockChromeAPI.storage.sync.get.mockResolvedValue({ conversations: [] });
      await syncManager.sync(mockConversations);
      
      expect(syncResult).not.toBeNull();
      expect(syncResult.success).toBe(true);
    });

    test('应该发出同步错误事件', async () => {
      await syncManager.initialize();
      
      let syncError: any = null;
      syncManager.on('syncError', (result) => {
        syncError = result;
      });
      
      mockChromeAPI.storage.sync.get.mockRejectedValue(new Error('测试错误'));
      await syncManager.sync(mockConversations);
      
      expect(syncError).not.toBeNull();
      expect(syncError.success).toBe(false);
    });
  });

  describe('工具方法', () => {
    test('应该返回支持的同步提供商', () => {
      const providers = SyncManager.getSupportedProviders();
      
      expect(providers).toHaveLength(5);
      expect(providers.map(p => p.provider)).toContain('chrome-storage');
      expect(providers.map(p => p.provider)).toContain('google-drive');
      expect(providers.map(p => p.provider)).toContain('dropbox');
    });

    test('应该能够断开连接', async () => {
      await syncManager.initialize();
      expect(syncManager.getStatus().isConnected).toBe(true);
      
      let disconnectedEventFired = false;
      syncManager.on('disconnected', () => {
        disconnectedEventFired = true;
      });
      
      await syncManager.disconnect();
      
      expect(syncManager.getStatus().isConnected).toBe(false);
      expect(disconnectedEventFired).toBe(true);
    });

    test('应该能够销毁管理器', () => {
      const stopAutoSyncSpy = jest.spyOn(syncManager as any, 'stopAutoSync');
      const removeAllListenersSpy = jest.spyOn(syncManager, 'removeAllListeners');
      
      syncManager.destroy();
      
      expect(stopAutoSyncSpy).toHaveBeenCalled();
      expect(removeAllListenersSpy).toHaveBeenCalled();
      
      stopAutoSyncSpy.mockRestore();
      removeAllListenersSpy.mockRestore();
    });
  });
});
