<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Memo - 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #4F46E5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #3730A3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🤖 AI Chat Memo - 内容检测引擎测试</h1>
    
    <div class="test-section">
        <h3>📋 插件状态检查</h3>
        <button class="button" onclick="checkExtensionStatus()">检查插件状态</button>
        <div id="extension-status" class="result"></div>
    </div>

    <div class="test-section">
        <h3>🔍 平台检测测试</h3>
        <p>当前页面: <span id="current-url"></span></p>
        <button class="button" onclick="testPlatformDetection()">测试平台检测</button>
        <div id="platform-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>💬 模拟 ChatGPT 对话</h3>
        <button class="button" onclick="createMockChatGPT()">创建模拟对话</button>
        <button class="button" onclick="addMockMessage()">添加消息</button>
        <button class="button" onclick="testExtraction()">测试提取</button>
        <div id="mock-chat"></div>
        <div id="extraction-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>📊 实时监听测试</h3>
        <button class="button" onclick="startMonitoring()">开始监听</button>
        <button class="button" onclick="stopMonitoring()">停止监听</button>
        <div id="monitoring-status" class="result"></div>
    </div>

    <div class="test-section">
        <h3>💾 数据存储测试</h3>
        <button class="button" onclick="testStorage()">测试存储功能</button>
        <button class="button" onclick="testSearch()">测试搜索功能</button>
        <button class="button" onclick="testExport()">测试导出功能</button>
        <div id="storage-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>⚙️ 设置管理测试</h3>
        <button class="button" onclick="testSettings()">测试设置功能</button>
        <button class="button" onclick="getStatistics()">获取统计信息</button>
        <div id="settings-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>💬 会话管理测试</h3>
        <button class="button" onclick="testConversationManager()">测试会话管理器</button>
        <button class="button" onclick="testConversationCRUD()">测试会话CRUD</button>
        <button class="button" onclick="testConversationMerge()">测试会话合并</button>
        <button class="button" onclick="testDuplicateDetection()">测试重复检测</button>
        <button class="button" onclick="testConversationArchive()">测试会话归档</button>
        <button class="button" onclick="testMessageOperations()">测试消息操作</button>
        <div id="conversation-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>💬 会话管理测试</h3>
        <button class="button" onclick="testConversationManager()">测试会话管理器</button>
        <button class="button" onclick="testConversationCRUD()">测试会话CRUD</button>
        <button class="button" onclick="testConversationMerge()">测试会话合并</button>
        <button class="button" onclick="testDuplicateDetection()">测试重复检测</button>
        <button class="button" onclick="testConversationArchive()">测试会话归档</button>
        <button class="button" onclick="testMessageOperations()">测试消息操作</button>
        <div id="conversation-result" class="result"></div>
    </div>

    <!-- 标签管理测试 -->
    <div class="test-section">
        <h3>标签管理测试</h3>
        <button class="button" onclick="testTagGeneration()">测试自动标签生成</button>
        <button class="button" onclick="testTagManagement()">测试标签管理</button>
        <button class="button" onclick="testTagRules()">测试标签规则</button>
        <button class="button" onclick="testTagSearch()">测试标签搜索</button>
        <button class="button" onclick="testTagStatistics()">测试标签统计</button>
        <button class="button" onclick="testBatchTagProcessing()">测试批量标签处理</button>
        <div id="tag-result" class="result"></div>
    </div>

    <script>
        let messageCount = 0;

        // 显示当前URL
        document.getElementById('current-url').textContent = window.location.href;

        // 检查插件状态
        async function checkExtensionStatus() {
            const result = document.getElementById('extension-status');
            
            try {
                // 检查是否有content script注入
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    result.innerHTML = '<span class="status success">✅ Chrome扩展API可用</span>';
                    
                    // 尝试与content script通信
                    chrome.runtime.sendMessage({type: 'ping'}, (response) => {
                        if (response) {
                            result.innerHTML += '\n<span class="status success">✅ Background script响应正常</span>';
                        } else {
                            result.innerHTML += '\n<span class="status error">❌ Background script无响应</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 错误: ' + error.message + '</span>';
            }
        }

        // 测试平台检测
        async function testPlatformDetection() {
            const result = document.getElementById('platform-result');
            result.textContent = '正在检测平台...';
            
            try {
                // 模拟不同平台的URL
                const testUrls = [
                    'https://chat.openai.com/c/test',
                    'https://claude.ai/chat/test',
                    'https://gemini.google.com/app',
                    'https://test.example.com'
                ];
                
                let results = '平台检测结果:\n';
                testUrls.forEach(url => {
                    const hostname = new URL(url).hostname;
                    let platform = 'unknown';
                    
                    if (hostname.includes('chat.openai.com')) platform = 'ChatGPT';
                    else if (hostname.includes('claude.ai')) platform = 'Claude';
                    else if (hostname.includes('gemini.google.com')) platform = 'Gemini';
                    
                    results += `${url} -> ${platform}\n`;
                });
                
                result.textContent = results;
            } catch (error) {
                result.textContent = '错误: ' + error.message;
            }
        }

        // 创建模拟ChatGPT对话
        function createMockChatGPT() {
            const mockChat = document.getElementById('mock-chat');
            mockChat.innerHTML = `
                <div role="main" style="padding: 20px; border: 1px solid #ccc; margin: 10px 0;">
                    <h4>模拟 ChatGPT 对话</h4>
                    <div id="messages-container">
                        <div data-message-author-role="user">
                            <div class="markdown">你好，请介绍一下JavaScript的基本语法。</div>
                        </div>
                        <div data-message-author-role="assistant">
                            <div class="markdown">
                                <p>JavaScript是一种动态编程语言，主要用于网页开发。以下是一些基本语法：</p>
                                <pre><code class="language-javascript">
// 变量声明
let name = "张三";
const age = 25;

// 函数定义
function greet(name) {
    return "Hello, " + name;
}

// 调用函数
console.log(greet(name));
                                </code></pre>
                                <p>这些是JavaScript的基础语法结构。</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 添加模拟消息
        function addMockMessage() {
            const container = document.getElementById('messages-container');
            if (!container) {
                alert('请先创建模拟对话');
                return;
            }
            
            messageCount++;
            const isUser = messageCount % 2 === 1;
            const role = isUser ? 'user' : 'assistant';
            const content = isUser ? 
                `这是第 ${messageCount} 条用户消息` : 
                `这是第 ${messageCount} 条AI回复，包含一些<strong>HTML标签</strong>和链接：<a href="https://example.com">示例链接</a>`;
            
            const messageDiv = document.createElement('div');
            messageDiv.setAttribute('data-message-author-role', role);
            messageDiv.innerHTML = `<div class="markdown">${content}</div>`;
            
            container.appendChild(messageDiv);
        }

        // 测试内容提取
        function testExtraction() {
            const result = document.getElementById('extraction-result');
            result.textContent = '正在测试内容提取...';
            
            try {
                // 模拟内容提取逻辑
                const messages = document.querySelectorAll('[data-message-author-role]');
                let extractedData = '提取的消息:\n';
                
                messages.forEach((msg, index) => {
                    const role = msg.getAttribute('data-message-author-role');
                    const content = msg.querySelector('.markdown')?.textContent || msg.textContent;
                    extractedData += `${index + 1}. [${role}] ${content.trim()}\n\n`;
                });
                
                result.textContent = extractedData;
            } catch (error) {
                result.textContent = '提取失败: ' + error.message;
            }
        }

        // 开始监听
        function startMonitoring() {
            const status = document.getElementById('monitoring-status');
            status.innerHTML = '<span class="status info">🔄 开始监听页面变化...</span>';
            
            // 模拟监听逻辑
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        const addedElements = Array.from(mutation.addedNodes)
                            .filter(node => node.nodeType === Node.ELEMENT_NODE);
                        
                        const hasMessages = addedElements.some(element => 
                            element.matches && element.matches('[data-message-author-role]')
                        );
                        
                        if (hasMessages) {
                            status.innerHTML += '\n<span class="status success">✅ 检测到新消息</span>';
                        }
                    }
                });
            });
            
            const container = document.getElementById('messages-container');
            if (container) {
                observer.observe(container, {
                    childList: true,
                    subtree: true
                });
                
                // 保存observer以便停止
                window.testObserver = observer;
            }
        }

        // 停止监听
        function stopMonitoring() {
            const status = document.getElementById('monitoring-status');
            
            if (window.testObserver) {
                window.testObserver.disconnect();
                window.testObserver = null;
                status.innerHTML += '\n<span class="status info">⏹️ 已停止监听</span>';
            }
        }

        // 测试数据存储功能
        async function testStorage() {
            const result = document.getElementById('storage-result');
            result.textContent = '正在测试存储功能...';

            try {
                // 模拟会话数据
                const mockConversation = {
                    id: 'test-conversation-' + Date.now(),
                    title: '测试会话 - JavaScript 学习',
                    platform: 'ChatGPT',
                    url: 'https://chat.openai.com/c/test',
                    tags: ['编程', '学习', 'JavaScript'],
                    messages: [
                        {
                            id: 'msg-1',
                            type: 'user',
                            content: '请介绍一下 JavaScript 的基本语法',
                            timestamp: new Date(),
                            metadata: { platform: 'ChatGPT' }
                        },
                        {
                            id: 'msg-2',
                            type: 'assistant',
                            content: 'JavaScript 是一种动态编程语言，主要用于网页开发...',
                            timestamp: new Date(),
                            metadata: { platform: 'ChatGPT' }
                        }
                    ],
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    metadata: {
                        messageCount: 2,
                        isArchived: false,
                        lastActivity: new Date()
                    }
                };

                // 测试保存会话
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    chrome.runtime.sendMessage({
                        type: 'save-conversation',
                        data: mockConversation
                    }, (response) => {
                        if (response && response.success) {
                            result.innerHTML = '<span class="status success">✅ 会话保存成功</span>';
                        } else {
                            result.innerHTML = '<span class="status error">❌ 会话保存失败: ' + (response?.error || '未知错误') + '</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        // 测试搜索功能
        async function testSearch() {
            const result = document.getElementById('storage-result');
            result.textContent = '正在测试搜索功能...';

            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    chrome.runtime.sendMessage({
                        type: 'search-conversations',
                        query: 'JavaScript',
                        options: { limit: 10 }
                    }, (response) => {
                        if (response && response.success) {
                            result.innerHTML = `<span class="status success">✅ 搜索成功，找到 ${response.results.length} 个结果</span>`;
                            if (response.results.length > 0) {
                                result.innerHTML += '\n搜索结果:\n' + JSON.stringify(response.results, null, 2);
                            }
                        } else {
                            result.innerHTML = '<span class="status error">❌ 搜索失败: ' + (response?.error || '未知错误') + '</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        // 测试导出功能
        async function testExport() {
            const result = document.getElementById('storage-result');
            result.textContent = '正在测试导出功能...';

            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    chrome.runtime.sendMessage({
                        type: 'export-conversations',
                        options: {
                            format: 'markdown',
                            includeMetadata: true,
                            includeImages: false,
                            dateFormat: 'YYYY-MM-DD HH:mm:ss'
                        }
                    }, (response) => {
                        if (response && response.success) {
                            result.innerHTML = `<span class="status success">✅ 导出成功，文件名: ${response.filename}</span>`;
                        } else {
                            result.innerHTML = '<span class="status error">❌ 导出失败: ' + (response?.error || '未知错误') + '</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        // 测试设置功能
        async function testSettings() {
            const result = document.getElementById('settings-result');
            result.textContent = '正在测试设置功能...';

            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    // 先获取当前设置
                    chrome.runtime.sendMessage({
                        type: 'get-settings'
                    }, (response) => {
                        if (response && response.success) {
                            result.innerHTML = '<span class="status success">✅ 获取设置成功</span>\n';
                            result.innerHTML += '当前设置:\n' + JSON.stringify(response.settings, null, 2);

                            // 测试更新设置
                            chrome.runtime.sendMessage({
                                type: 'update-settings',
                                settings: {
                                    ui: { theme: 'dark' },
                                    autoSave: { enabled: false }
                                }
                            }, (updateResponse) => {
                                if (updateResponse && updateResponse.success) {
                                    result.innerHTML += '\n<span class="status success">✅ 设置更新成功</span>';
                                } else {
                                    result.innerHTML += '\n<span class="status error">❌ 设置更新失败</span>';
                                }
                            });
                        } else {
                            result.innerHTML = '<span class="status error">❌ 获取设置失败: ' + (response?.error || '未知错误') + '</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        // 获取统计信息
        async function getStatistics() {
            const result = document.getElementById('settings-result');
            result.textContent = '正在获取统计信息...';

            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    chrome.runtime.sendMessage({
                        type: 'get-statistics'
                    }, (response) => {
                        if (response && response.success) {
                            result.innerHTML = '<span class="status success">✅ 统计信息获取成功</span>\n';
                            result.innerHTML += '统计数据:\n' + JSON.stringify(response.stats, null, 2);
                        } else {
                            result.innerHTML = '<span class="status error">❌ 获取统计信息失败: ' + (response?.error || '未知错误') + '</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        // 会话管理测试函数
        async function testConversationManager() {
            const result = document.getElementById('conversation-result');
            result.textContent = '正在测试会话管理器...';

            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    // 测试创建会话
                    const testConversation = {
                        title: '测试会话 - ' + new Date().toLocaleString(),
                        platform: 'test',
                        url: window.location.href,
                        messages: [
                            {
                                type: 'user',
                                content: '这是一个测试用户消息',
                                timestamp: new Date()
                            },
                            {
                                type: 'assistant',
                                content: '这是一个测试助手回复',
                                timestamp: new Date()
                            }
                        ],
                        tags: ['测试', '会话管理'],
                        metadata: {
                            messageCount: 2,
                            lastActivity: new Date(),
                            isArchived: false
                        }
                    };

                    chrome.runtime.sendMessage({
                        type: 'create-conversation',
                        data: testConversation
                    }, (response) => {
                        if (response && response.success) {
                            result.innerHTML = '<span class="status success">✅ 会话管理器测试成功</span>\n';
                            result.innerHTML += '创建的会话:\n' + JSON.stringify(response.conversation, null, 2);
                        } else {
                            result.innerHTML = '<span class="status error">❌ 会话管理器测试失败: ' + (response?.error || '未知错误') + '</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        async function testConversationCRUD() {
            const result = document.getElementById('conversation-result');
            result.textContent = '正在测试会话CRUD操作...';

            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    // 先创建一个会话用于测试
                    const testConversation = {
                        title: 'CRUD测试会话',
                        platform: 'test',
                        url: window.location.href,
                        messages: [{
                            type: 'user',
                            content: 'CRUD测试消息',
                            timestamp: new Date()
                        }],
                        tags: ['CRUD', '测试'],
                        metadata: {
                            messageCount: 1,
                            lastActivity: new Date(),
                            isArchived: false
                        }
                    };

                    chrome.runtime.sendMessage({
                        type: 'create-conversation',
                        data: testConversation
                    }, (createResponse) => {
                        if (createResponse && createResponse.success) {
                            const conversationId = createResponse.conversation.id;

                            // 测试更新会话
                            chrome.runtime.sendMessage({
                                type: 'update-conversation',
                                data: {
                                    id: conversationId,
                                    updates: {
                                        title: 'CRUD测试会话 - 已更新',
                                        tags: ['CRUD', '测试', '已更新']
                                    }
                                }
                            }, (updateResponse) => {
                                if (updateResponse && updateResponse.success) {
                                    result.innerHTML = '<span class="status success">✅ 会话CRUD测试成功</span>\n';
                                    result.innerHTML += '更新后的会话:\n' + JSON.stringify(updateResponse.conversation, null, 2);
                                } else {
                                    result.innerHTML = '<span class="status error">❌ 会话更新失败: ' + (updateResponse?.error || '未知错误') + '</span>';
                                }
                            });
                        } else {
                            result.innerHTML = '<span class="status error">❌ 会话创建失败: ' + (createResponse?.error || '未知错误') + '</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        async function testConversationMerge() {
            const result = document.getElementById('conversation-result');
            result.textContent = '正在测试会话合并...';

            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    // 创建两个会话用于合并测试
                    const conv1 = {
                        title: '合并测试会话1',
                        platform: 'test',
                        url: window.location.href,
                        messages: [{
                            type: 'user',
                            content: '第一个会话的消息',
                            timestamp: new Date()
                        }],
                        tags: ['合并', '测试1'],
                        metadata: { messageCount: 1, lastActivity: new Date(), isArchived: false }
                    };

                    const conv2 = {
                        title: '合并测试会话2',
                        platform: 'test',
                        url: window.location.href,
                        messages: [{
                            type: 'user',
                            content: '第二个会话的消息',
                            timestamp: new Date()
                        }],
                        tags: ['合并', '测试2'],
                        metadata: { messageCount: 1, lastActivity: new Date(), isArchived: false }
                    };

                    // 创建第一个会话
                    chrome.runtime.sendMessage({
                        type: 'create-conversation',
                        data: conv1
                    }, (response1) => {
                        if (response1 && response1.success) {
                            // 创建第二个会话
                            chrome.runtime.sendMessage({
                                type: 'create-conversation',
                                data: conv2
                            }, (response2) => {
                                if (response2 && response2.success) {
                                    // 合并会话
                                    chrome.runtime.sendMessage({
                                        type: 'merge-conversations',
                                        data: {
                                            targetId: response1.conversation.id,
                                            sourceIds: [response2.conversation.id],
                                            mergeStrategy: 'chronological',
                                            keepSeparateMessages: false,
                                            newTitle: '合并后的测试会话'
                                        }
                                    }, (mergeResponse) => {
                                        if (mergeResponse && mergeResponse.success) {
                                            result.innerHTML = '<span class="status success">✅ 会话合并测试成功</span>\n';
                                            result.innerHTML += '合并后的会话:\n' + JSON.stringify(mergeResponse.conversation, null, 2);
                                        } else {
                                            result.innerHTML = '<span class="status error">❌ 会话合并失败: ' + (mergeResponse?.error || '未知错误') + '</span>';
                                        }
                                    });
                                } else {
                                    result.innerHTML = '<span class="status error">❌ 创建第二个会话失败</span>';
                                }
                            });
                        } else {
                            result.innerHTML = '<span class="status error">❌ 创建第一个会话失败</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        async function testDuplicateDetection() {
            const result = document.getElementById('conversation-result');
            result.textContent = '正在测试重复检测...';

            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    const testConversation = {
                        title: '重复检测测试会话',
                        platform: 'test',
                        url: window.location.href,
                        messages: [{
                            type: 'user',
                            content: '这是用于重复检测的测试消息',
                            timestamp: new Date()
                        }],
                        tags: ['重复检测', '测试'],
                        metadata: { messageCount: 1, lastActivity: new Date(), isArchived: false }
                    };

                    chrome.runtime.sendMessage({
                        type: 'find-duplicate-conversations',
                        data: {
                            conversation: testConversation,
                            options: {
                                similarity: 0.8,
                                checkTitle: true,
                                checkContent: true,
                                checkPlatform: true,
                                checkTimeRange: 24
                            }
                        }
                    }, (response) => {
                        if (response && response.success) {
                            result.innerHTML = '<span class="status success">✅ 重复检测测试成功</span>\n';
                            result.innerHTML += '找到 ' + response.duplicates.length + ' 个重复会话:\n' + JSON.stringify(response.duplicates, null, 2);
                        } else {
                            result.innerHTML = '<span class="status error">❌ 重复检测失败: ' + (response?.error || '未知错误') + '</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        async function testConversationArchive() {
            const result = document.getElementById('conversation-result');
            result.textContent = '正在测试会话归档...';

            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    chrome.runtime.sendMessage({
                        type: 'archive-conversations',
                        data: {
                            archiveOlderThan: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7天前
                            platforms: ['test'],
                            messageCountLessThan: 5
                        }
                    }, (response) => {
                        if (response && response.success) {
                            result.innerHTML = '<span class="status success">✅ 会话归档测试成功</span>\n';
                            result.innerHTML += '归档结果:\n' + JSON.stringify(response.result, null, 2);
                        } else {
                            result.innerHTML = '<span class="status error">❌ 会话归档失败: ' + (response?.error || '未知错误') + '</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        async function testMessageOperations() {
            const result = document.getElementById('conversation-result');
            result.textContent = '正在测试消息操作...';

            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    // 先创建一个会话
                    const testConversation = {
                        title: '消息操作测试会话',
                        platform: 'test',
                        url: window.location.href,
                        messages: [{
                            type: 'user',
                            content: '初始消息',
                            timestamp: new Date()
                        }],
                        tags: ['消息操作', '测试'],
                        metadata: { messageCount: 1, lastActivity: new Date(), isArchived: false }
                    };

                    chrome.runtime.sendMessage({
                        type: 'create-conversation',
                        data: testConversation
                    }, (createResponse) => {
                        if (createResponse && createResponse.success) {
                            const conversationId = createResponse.conversation.id;

                            // 添加新消息
                            chrome.runtime.sendMessage({
                                type: 'add-message',
                                data: {
                                    conversationId: conversationId,
                                    message: {
                                        type: 'assistant',
                                        content: '这是新添加的助手消息',
                                        timestamp: new Date()
                                    }
                                }
                            }, (addResponse) => {
                                if (addResponse && addResponse.success) {
                                    result.innerHTML = '<span class="status success">✅ 消息操作测试成功</span>\n';
                                    result.innerHTML += '添加的消息:\n' + JSON.stringify(addResponse.message, null, 2);
                                } else {
                                    result.innerHTML = '<span class="status error">❌ 添加消息失败: ' + (addResponse?.error || '未知错误') + '</span>';
                                }
                            });
                        } else {
                            result.innerHTML = '<span class="status error">❌ 创建会话失败: ' + (createResponse?.error || '未知错误') + '</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        // 标签管理测试函数
        async function testTagGeneration() {
            const result = document.getElementById('tag-result');
            result.textContent = '正在测试自动标签生成...';

            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    // 创建测试会话
                    const testConversation = {
                        title: '如何学习JavaScript编程',
                        platform: 'chatgpt',
                        url: 'https://chat.openai.com/c/test',
                        messages: [
                            {
                                type: 'user',
                                content: '我想学习JavaScript编程，应该从哪里开始？',
                                timestamp: new Date()
                            },
                            {
                                type: 'assistant',
                                content: '学习JavaScript编程可以从以下几个方面开始：1. 基础语法 2. DOM操作 3. 异步编程 4. 框架学习',
                                timestamp: new Date()
                            }
                        ],
                        tags: [],
                        metadata: {
                            messageCount: 2,
                            lastActivity: new Date(),
                            isArchived: false
                        }
                    };

                    chrome.runtime.sendMessage({
                        type: 'generate-auto-tags',
                        data: { conversation: testConversation }
                    }, (response) => {
                        if (response && response.success) {
                            result.innerHTML = '<span class="status success">✅ 自动标签生成成功</span>\n';
                            result.innerHTML += '生成的标签建议:\n' + JSON.stringify(response.suggestions, null, 2);
                        } else {
                            result.innerHTML = '<span class="status error">❌ 自动标签生成失败: ' + (response?.error || '未知错误') + '</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        async function testTagManagement() {
            const result = document.getElementById('tag-result');
            result.textContent = '正在测试标签管理...';

            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    // 先创建一个会话
                    const testConversation = {
                        title: '标签管理测试会话',
                        platform: 'test',
                        url: window.location.href,
                        messages: [{
                            type: 'user',
                            content: '这是用于测试标签管理的会话',
                            timestamp: new Date()
                        }],
                        tags: [],
                        metadata: { messageCount: 1, lastActivity: new Date(), isArchived: false }
                    };

                    chrome.runtime.sendMessage({
                        type: 'create-conversation',
                        data: testConversation
                    }, (createResponse) => {
                        if (createResponse && createResponse.success) {
                            const conversationId = createResponse.conversation.id;

                            // 添加标签
                            chrome.runtime.sendMessage({
                                type: 'add-tags',
                                data: {
                                    conversationId: conversationId,
                                    tags: ['测试', '标签管理', '自动化']
                                }
                            }, (addResponse) => {
                                if (addResponse && addResponse.success) {
                                    result.innerHTML = '<span class="status success">✅ 标签管理测试成功</span>\n';
                                    result.innerHTML += '已添加标签到会话: ' + conversationId;
                                } else {
                                    result.innerHTML = '<span class="status error">❌ 添加标签失败: ' + (addResponse?.error || '未知错误') + '</span>';
                                }
                            });
                        } else {
                            result.innerHTML = '<span class="status error">❌ 创建会话失败: ' + (createResponse?.error || '未知错误') + '</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        async function testTagRules() {
            const result = document.getElementById('tag-result');
            result.textContent = '正在测试标签规则...';

            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    // 创建标签规则
                    const testRule = {
                        name: '编程相关自动标签',
                        description: '自动为编程相关会话添加标签',
                        enabled: true,
                        priority: 10,
                        conditions: [
                            {
                                type: 'content_contains',
                                operator: 'contains',
                                value: '编程',
                                caseSensitive: false
                            }
                        ],
                        actions: [
                            {
                                type: 'add_tag',
                                value: '编程'
                            }
                        ]
                    };

                    chrome.runtime.sendMessage({
                        type: 'create-tag-rule',
                        data: { rule: testRule }
                    }, (response) => {
                        if (response && response.success) {
                            result.innerHTML = '<span class="status success">✅ 标签规则测试成功</span>\n';
                            result.innerHTML += '创建的规则:\n' + JSON.stringify(response.rule, null, 2);
                        } else {
                            result.innerHTML = '<span class="status error">❌ 标签规则测试失败: ' + (response?.error || '未知错误') + '</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        async function testTagSearch() {
            const result = document.getElementById('tag-result');
            result.textContent = '正在测试标签搜索...';

            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    chrome.runtime.sendMessage({
                        type: 'search-tags',
                        data: { query: '编程', limit: 10 }
                    }, (response) => {
                        if (response && response.success) {
                            result.innerHTML = '<span class="status success">✅ 标签搜索测试成功</span>\n';
                            result.innerHTML += '搜索结果:\n' + JSON.stringify(response.tags, null, 2);
                        } else {
                            result.innerHTML = '<span class="status error">❌ 标签搜索失败: ' + (response?.error || '未知错误') + '</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        async function testTagStatistics() {
            const result = document.getElementById('tag-result');
            result.textContent = '正在测试标签统计...';

            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    chrome.runtime.sendMessage({
                        type: 'get-tag-statistics'
                    }, (response) => {
                        if (response && response.success) {
                            result.innerHTML = '<span class="status success">✅ 标签统计测试成功</span>\n';
                            result.innerHTML += '统计信息:\n' + JSON.stringify(response.statistics, null, 2);
                        } else {
                            result.innerHTML = '<span class="status error">❌ 标签统计失败: ' + (response?.error || '未知错误') + '</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        async function testBatchTagProcessing() {
            const result = document.getElementById('tag-result');
            result.textContent = '正在测试批量标签处理...';

            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    // 先获取一些会话ID用于测试
                    chrome.runtime.sendMessage({
                        type: 'get-conversations',
                        data: { limit: 3 }
                    }, (getResponse) => {
                        if (getResponse && getResponse.success && getResponse.conversations.length > 0) {
                            const conversationIds = getResponse.conversations.map(conv => conv.id);

                            chrome.runtime.sendMessage({
                                type: 'batch-process-tags',
                                data: {
                                    conversationIds: conversationIds,
                                    options: {
                                        addTags: ['批量处理', '测试'],
                                        applyAutoTags: true,
                                        autoTagThreshold: 0.6
                                    }
                                }
                            }, (response) => {
                                if (response && response.success) {
                                    result.innerHTML = '<span class="status success">✅ 批量标签处理测试成功</span>\n';
                                    result.innerHTML += '处理结果:\n' + JSON.stringify(response.result, null, 2);
                                } else {
                                    result.innerHTML = '<span class="status error">❌ 批量标签处理失败: ' + (response?.error || '未知错误') + '</span>';
                                }
                            });
                        } else {
                            result.innerHTML = '<span class="status warning">⚠️ 没有找到会话进行批量处理测试</span>';
                        }
                    });
                } else {
                    result.innerHTML = '<span class="status error">❌ Chrome扩展API不可用</span>';
                }
            } catch (error) {
                result.innerHTML = '<span class="status error">❌ 测试失败: ' + error.message + '</span>';
            }
        }

        // 页面加载完成后自动检查状态
        window.addEventListener('load', () => {
            setTimeout(checkExtensionStatus, 1000);
        });
    </script>
</body>
</html>
