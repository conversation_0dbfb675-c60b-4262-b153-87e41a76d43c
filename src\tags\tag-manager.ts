/**
 * 标签管理器
 * 负责自动标签生成和手动标签管理功能
 */

import { ConversationData, MessageData } from '@types/conversation';
import { storageService } from '@storage/index';
import { Logger } from '@shared/logger';
import { MessageBus } from '@shared/message-bus';
import { EVENTS } from '@types/events';
import { TagManagerHelpers } from './tag-manager-helpers';

export interface TagRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  priority: number;
  conditions: TagCondition[];
  actions: TagAction[];
  createdAt: Date;
  updatedAt: Date;
}

export interface TagCondition {
  type: 'title_contains' | 'content_contains' | 'platform_is' | 'message_count' | 'duration' | 'time_range';
  field?: string;
  operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'greater_than' | 'less_than' | 'between' | 'regex';
  value: any;
  caseSensitive?: boolean;
}

export interface TagAction {
  type: 'add_tag' | 'remove_tag' | 'set_priority' | 'archive' | 'notify';
  value: any;
}

export interface TagSuggestion {
  tag: string;
  confidence: number;
  reason: string;
  source: 'auto' | 'pattern' | 'content' | 'platform' | 'user_history';
}

export interface TagStatistics {
  totalTags: number;
  mostUsedTags: { tag: string; count: number; }[];
  recentTags: { tag: string; lastUsed: Date; }[];
  tagsByPlatform: Record<string, string[]>;
  autoTagAccuracy: number;
}

export class TagManager {
  private logger: Logger;
  private messageBus: MessageBus;
  private tagRules: Map<string, TagRule> = new Map();
  private tagCache: Map<string, string[]> = new Map();
  private commonTags: Set<string> = new Set();
  private helpers: TagManagerHelpers;

  // 预定义的常用标签
  private readonly COMMON_TAGS = [
    '编程', '技术', '学习', '工作', '生活', '娱乐', '新闻', '科学',
    '艺术', '音乐', '电影', '书籍', '旅行', '美食', '健康', '运动',
    '问答', '教程', '讨论', '分析', '总结', '翻译', '创作', '调试'
  ];

  // 平台特定标签
  private readonly PLATFORM_TAGS: Record<string, string[]> = {
    'chatgpt': ['OpenAI', 'GPT', 'AI助手'],
    'claude': ['Anthropic', 'Claude', 'AI对话'],
    'gemini': ['Google', 'Gemini', 'Bard'],
    'aistudio': ['百度', '文心一言', '中文AI'],
    'monica': ['Monica', 'AI工具'],
    'poe': ['Poe', 'AI聚合']
  };

  constructor() {
    this.logger = new Logger('TagManager');
    this.messageBus = new MessageBus();
    this.helpers = new TagManagerHelpers();
    this.initializeCommonTags();
  }

  /**
   * 初始化标签管理器
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('初始化标签管理器...');
      
      // 加载标签规则
      await this.loadTagRules();
      
      // 加载常用标签
      await this.loadCommonTags();
      
      // 设置事件监听器
      this.setupEventListeners();
      
      this.logger.info('标签管理器初始化完成');
    } catch (error) {
      this.logger.error('标签管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 为会话自动生成标签
   */
  async generateAutoTags(conversation: ConversationData): Promise<TagSuggestion[]> {
    try {
      this.logger.debug(`为会话生成自动标签: ${conversation.id}`);
      
      const suggestions: TagSuggestion[] = [];
      
      // 基于平台的标签
      const platformTags = this.generatePlatformTags(conversation);
      suggestions.push(...platformTags);
      
      // 基于内容的标签
      const contentTags = await this.generateContentTags(conversation);
      suggestions.push(...contentTags);
      
      // 基于规则的标签
      const ruleTags = await this.helpers.generateRuleTags(conversation, this.tagRules);
      suggestions.push(...ruleTags);

      // 基于历史的标签
      const historyTags = await this.helpers.generateHistoryTags(conversation);
      suggestions.push(...historyTags);

      // 去重并排序
      const uniqueSuggestions = this.helpers.deduplicateAndSort(suggestions);
      
      this.logger.debug(`生成了 ${uniqueSuggestions.length} 个标签建议`);
      return uniqueSuggestions;
    } catch (error) {
      this.logger.error('生成自动标签失败:', error);
      return [];
    }
  }

  /**
   * 应用自动标签到会话
   */
  async applyAutoTags(conversationId: string, suggestions: TagSuggestion[], threshold: number = 0.7): Promise<string[]> {
    try {
      this.logger.debug(`应用自动标签到会话: ${conversationId}`);
      
      const conversation = await storageService.conversations.getConversation(conversationId);
      if (!conversation) {
        throw new Error(`会话不存在: ${conversationId}`);
      }
      
      // 筛选高置信度的标签
      const highConfidenceTags = suggestions
        .filter(s => s.confidence >= threshold)
        .map(s => s.tag);
      
      if (highConfidenceTags.length === 0) {
        this.logger.debug('没有高置信度的标签建议');
        return [];
      }
      
      // 合并现有标签和新标签
      const existingTags = conversation.tags || [];
      const newTags = [...new Set([...existingTags, ...highConfidenceTags])];
      
      // 更新会话标签
      await storageService.conversations.updateConversation(conversationId, {
        tags: newTags
      });
      
      // 更新标签缓存
      this.tagCache.set(conversationId, newTags);
      
      // 发送事件
      this.messageBus.emit(EVENTS.CONVERSATION_UPDATED, { 
        id: conversationId, 
        tags: newTags,
        autoTagsApplied: highConfidenceTags
      });
      
      this.logger.debug(`应用了 ${highConfidenceTags.length} 个自动标签`);
      return highConfidenceTags;
    } catch (error) {
      this.logger.error('应用自动标签失败:', error);
      throw error;
    }
  }

  /**
   * 手动添加标签到会话
   */
  async addTagsToConversation(conversationId: string, tags: string[]): Promise<void> {
    try {
      this.logger.debug(`添加标签到会话: ${conversationId}`);
      
      const conversation = await storageService.conversations.getConversation(conversationId);
      if (!conversation) {
        throw new Error(`会话不存在: ${conversationId}`);
      }
      
      // 标准化标签
      const normalizedTags = tags.map(tag => this.helpers.normalizeTag(tag));
      
      // 合并标签
      const existingTags = conversation.tags || [];
      const newTags = [...new Set([...existingTags, ...normalizedTags])];
      
      // 更新会话
      await storageService.conversations.updateConversation(conversationId, {
        tags: newTags
      });
      
      // 更新缓存和常用标签
      this.tagCache.set(conversationId, newTags);
      normalizedTags.forEach(tag => this.commonTags.add(tag));
      
      // 保存常用标签
      await this.saveCommonTags();
      
      // 发送事件
      this.messageBus.emit(EVENTS.CONVERSATION_UPDATED, { 
        id: conversationId, 
        tags: newTags,
        manualTagsAdded: normalizedTags
      });
      
      this.logger.debug(`添加了 ${normalizedTags.length} 个标签`);
    } catch (error) {
      this.logger.error('添加标签失败:', error);
      throw error;
    }
  }

  /**
   * 从会话中移除标签
   */
  async removeTagsFromConversation(conversationId: string, tags: string[]): Promise<void> {
    try {
      this.logger.debug(`从会话移除标签: ${conversationId}`);
      
      const conversation = await storageService.conversations.getConversation(conversationId);
      if (!conversation) {
        throw new Error(`会话不存在: ${conversationId}`);
      }
      
      // 移除指定标签
      const existingTags = conversation.tags || [];
      const newTags = existingTags.filter(tag => !tags.includes(tag));
      
      // 更新会话
      await storageService.conversations.updateConversation(conversationId, {
        tags: newTags
      });
      
      // 更新缓存
      this.tagCache.set(conversationId, newTags);
      
      // 发送事件
      this.messageBus.emit(EVENTS.CONVERSATION_UPDATED, { 
        id: conversationId, 
        tags: newTags,
        tagsRemoved: tags
      });
      
      this.logger.debug(`移除了 ${tags.length} 个标签`);
    } catch (error) {
      this.logger.error('移除标签失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有标签
   */
  async getAllTags(): Promise<string[]> {
    try {
      const result = await storageService.conversations.getConversations();
      const allTags = new Set<string>();
      
      result.conversations.forEach(conv => {
        if (conv.tags) {
          conv.tags.forEach(tag => allTags.add(tag));
        }
      });
      
      // 合并常用标签
      this.commonTags.forEach(tag => allTags.add(tag));
      
      return Array.from(allTags).sort();
    } catch (error) {
      this.logger.error('获取所有标签失败:', error);
      return [];
    }
  }

  /**
   * 搜索标签
   */
  async searchTags(query: string, limit: number = 10): Promise<string[]> {
    try {
      const allTags = await this.getAllTags();
      const normalizedQuery = query.toLowerCase();
      
      // 精确匹配优先
      const exactMatches = allTags.filter(tag => 
        tag.toLowerCase() === normalizedQuery
      );
      
      // 前缀匹配
      const prefixMatches = allTags.filter(tag => 
        tag.toLowerCase().startsWith(normalizedQuery) && 
        !exactMatches.includes(tag)
      );
      
      // 包含匹配
      const containsMatches = allTags.filter(tag => 
        tag.toLowerCase().includes(normalizedQuery) && 
        !exactMatches.includes(tag) && 
        !prefixMatches.includes(tag)
      );
      
      return [...exactMatches, ...prefixMatches, ...containsMatches].slice(0, limit);
    } catch (error) {
      this.logger.error('搜索标签失败:', error);
      return [];
    }
  }

  /**
   * 获取标签统计信息
   */
  async getTagStatistics(): Promise<TagStatistics> {
    try {
      const result = await storageService.conversations.getConversations();
      const conversations = result.conversations;
      
      const tagCounts = new Map<string, number>();
      const tagLastUsed = new Map<string, Date>();
      const tagsByPlatform: Record<string, Set<string>> = {};
      
      conversations.forEach(conv => {
        if (conv.tags) {
          conv.tags.forEach(tag => {
            // 计数
            tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
            
            // 最后使用时间
            const lastUsed = tagLastUsed.get(tag);
            if (!lastUsed || conv.metadata.lastActivity > lastUsed) {
              tagLastUsed.set(tag, conv.metadata.lastActivity);
            }
            
            // 按平台分组
            if (!tagsByPlatform[conv.platform]) {
              tagsByPlatform[conv.platform] = new Set();
            }
            tagsByPlatform[conv.platform].add(tag);
          });
        }
      });
      
      // 最常用标签
      const mostUsedTags = Array.from(tagCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 20)
        .map(([tag, count]) => ({ tag, count }));
      
      // 最近使用标签
      const recentTags = Array.from(tagLastUsed.entries())
        .sort((a, b) => b[1].getTime() - a[1].getTime())
        .slice(0, 20)
        .map(([tag, lastUsed]) => ({ tag, lastUsed }));
      
      // 转换平台标签
      const platformTags: Record<string, string[]> = {};
      Object.keys(tagsByPlatform).forEach(platform => {
        platformTags[platform] = Array.from(tagsByPlatform[platform]);
      });
      
      return {
        totalTags: tagCounts.size,
        mostUsedTags,
        recentTags,
        tagsByPlatform: platformTags,
        autoTagAccuracy: 0.85 // 这里可以基于用户反馈计算实际准确率
      };
    } catch (error) {
      this.logger.error('获取标签统计失败:', error);
      throw error;
    }
  }

  /**
   * 创建标签规则
   */
  async createTagRule(rule: Omit<TagRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<TagRule> {
    try {
      const newRule: TagRule = {
        ...rule,
        id: this.helpers.generateRuleId(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      this.tagRules.set(newRule.id, newRule);
      await this.saveTagRules();

      this.logger.info(`创建标签规则: ${newRule.name}`);
      return newRule;
    } catch (error) {
      this.logger.error('创建标签规则失败:', error);
      throw error;
    }
  }

  /**
   * 更新标签规则
   */
  async updateTagRule(id: string, updates: Partial<TagRule>): Promise<TagRule> {
    try {
      const rule = this.tagRules.get(id);
      if (!rule) {
        throw new Error(`标签规则不存在: ${id}`);
      }

      const updatedRule = {
        ...rule,
        ...updates,
        id, // 确保ID不被修改
        updatedAt: new Date()
      };

      this.tagRules.set(id, updatedRule);
      await this.saveTagRules();

      this.logger.info(`更新标签规则: ${updatedRule.name}`);
      return updatedRule;
    } catch (error) {
      this.logger.error('更新标签规则失败:', error);
      throw error;
    }
  }

  /**
   * 删除标签规则
   */
  async deleteTagRule(id: string): Promise<void> {
    try {
      const rule = this.tagRules.get(id);
      if (!rule) {
        throw new Error(`标签规则不存在: ${id}`);
      }

      this.tagRules.delete(id);
      await this.saveTagRules();

      this.logger.info(`删除标签规则: ${rule.name}`);
    } catch (error) {
      this.logger.error('删除标签规则失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有标签规则
   */
  getTagRules(): TagRule[] {
    return Array.from(this.tagRules.values()).sort((a, b) => b.priority - a.priority);
  }

  /**
   * 批量处理会话标签
   */
  async batchProcessTags(conversationIds: string[], options: {
    addTags?: string[];
    removeTags?: string[];
    applyAutoTags?: boolean;
    autoTagThreshold?: number;
  }): Promise<{ processed: string[], failed: string[] }> {
    try {
      this.logger.info(`批量处理 ${conversationIds.length} 个会话的标签`);

      const processed: string[] = [];
      const failed: string[] = [];

      for (const id of conversationIds) {
        try {
          // 添加标签
          if (options.addTags && options.addTags.length > 0) {
            await this.addTagsToConversation(id, options.addTags);
          }

          // 移除标签
          if (options.removeTags && options.removeTags.length > 0) {
            await this.removeTagsFromConversation(id, options.removeTags);
          }

          // 应用自动标签
          if (options.applyAutoTags) {
            const conversation = await storageService.conversations.getConversation(id);
            if (conversation) {
              const suggestions = await this.generateAutoTags(conversation);
              await this.applyAutoTags(id, suggestions, options.autoTagThreshold || 0.7);
            }
          }

          processed.push(id);
        } catch (error) {
          this.logger.error(`处理会话标签失败: ${id}`, error);
          failed.push(id);
        }
      }

      this.logger.info(`批量处理完成: 成功 ${processed.length}, 失败 ${failed.length}`);
      return { processed, failed };
    } catch (error) {
      this.logger.error('批量处理标签失败:', error);
      throw error;
    }
  }

  /**
   * 生成基于平台的标签
   */
  private generatePlatformTags(conversation: ConversationData): TagSuggestion[] {
    const suggestions: TagSuggestion[] = [];
    const platformTags = this.PLATFORM_TAGS[conversation.platform] || [];

    platformTags.forEach(tag => {
      suggestions.push({
        tag,
        confidence: 0.9,
        reason: `基于平台 ${conversation.platform}`,
        source: 'platform'
      });
    });

    return suggestions;
  }

  /**
   * 生成基于内容的标签
   */
  private async generateContentTags(conversation: ConversationData): Promise<TagSuggestion[]> {
    const suggestions: TagSuggestion[] = [];

    // 分析标题和内容
    const text = [
      conversation.title,
      ...conversation.messages.map(m => m.content)
    ].join(' ').toLowerCase();

    // 关键词匹配
    const keywords = {
      '编程': ['代码', '编程', '开发', 'code', 'programming', 'javascript', 'python', 'java', 'html', 'css'],
      '学习': ['学习', '教程', '如何', '怎么', '学会', 'learn', 'tutorial', 'how to'],
      '工作': ['工作', '职业', '面试', '简历', 'work', 'job', 'career', 'interview'],
      '技术': ['技术', '算法', '架构', '设计', 'technology', 'algorithm', 'architecture'],
      '问答': ['问题', '回答', '解答', '帮助', 'question', 'answer', 'help'],
      '翻译': ['翻译', '英文', '中文', 'translate', 'translation', 'english', 'chinese'],
      '创作': ['写作', '创作', '文章', '故事', 'writing', 'creative', 'story', 'article'],
      '分析': ['分析', '总结', '评价', 'analysis', 'summary', 'review', 'evaluate']
    };

    Object.entries(keywords).forEach(([tag, words]) => {
      const matches = words.filter(word => text.includes(word));
      if (matches.length > 0) {
        const confidence = Math.min(0.8, 0.3 + (matches.length * 0.1));
        suggestions.push({
          tag,
          confidence,
          reason: `内容包含关键词: ${matches.join(', ')}`,
          source: 'content'
        });
      }
    });

    return suggestions;
  }

  /**
   * 初始化常用标签
   */
  private initializeCommonTags(): void {
    this.COMMON_TAGS.forEach(tag => this.commonTags.add(tag));
  }

  /**
   * 加载标签规则
   */
  private async loadTagRules(): Promise<void> {
    try {
      const rules = await storageService.settings.get('tagRules', []);
      rules.forEach((rule: TagRule) => {
        this.tagRules.set(rule.id, rule);
      });
      this.logger.debug(`加载了 ${rules.length} 个标签规则`);
    } catch (error) {
      this.logger.error('加载标签规则失败:', error);
    }
  }

  /**
   * 保存标签规则
   */
  private async saveTagRules(): Promise<void> {
    try {
      const rules = Array.from(this.tagRules.values());
      await storageService.settings.set('tagRules', rules);
      this.logger.debug(`保存了 ${rules.length} 个标签规则`);
    } catch (error) {
      this.logger.error('保存标签规则失败:', error);
    }
  }

  /**
   * 加载常用标签
   */
  private async loadCommonTags(): Promise<void> {
    try {
      const tags = await storageService.settings.get('commonTags', []);
      tags.forEach((tag: string) => this.commonTags.add(tag));
      this.logger.debug(`加载了 ${tags.length} 个常用标签`);
    } catch (error) {
      this.logger.error('加载常用标签失败:', error);
    }
  }

  /**
   * 保存常用标签
   */
  private async saveCommonTags(): Promise<void> {
    try {
      const tags = Array.from(this.commonTags);
      await storageService.settings.set('commonTags', tags);
      this.logger.debug(`保存了 ${tags.length} 个常用标签`);
    } catch (error) {
      this.logger.error('保存常用标签失败:', error);
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听会话创建事件，自动生成标签
    this.messageBus.on(EVENTS.CONVERSATION_CREATED, async (data: any) => {
      try {
        const conversation = data.conversation;
        if (conversation && data.source === 'auto') {
          const suggestions = await this.generateAutoTags(conversation);
          await this.applyAutoTags(conversation.id, suggestions);
        }
      } catch (error) {
        this.logger.error('自动标签生成失败:', error);
      }
    });
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.tagCache.clear();
    this.tagRules.clear();
    this.logger.info('标签管理器资源已清理');
  }
}
