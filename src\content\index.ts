/**
 * Content Script 入口文件
 * 在AI网站页面中运行，负责检测和提取对话内容
 */

import { MessageBus } from '@shared/message-bus';
import { Logger } from '@shared/logger';
import { ContentDetectionEngine } from './detection-engine';
import { FloatingIndicator } from './ui/floating-indicator';

class ContentService {
  private messageBus: MessageBus;
  private logger: Logger;
  private detectionEngine: ContentDetectionEngine;
  private floatingIndicator: FloatingIndicator;

  constructor() {
    console.log('🚀 [AI Chat Memo] ContentService constructor called');
    console.log('🌐 Current URL:', window.location.href);
    console.log('📄 Document ready state:', document.readyState);
    console.log('🕐 Current time:', new Date().toISOString());

    this.logger = new Logger('Content');
    this.messageBus = new MessageBus('content');
    this.detectionEngine = new ContentDetectionEngine();
    this.floatingIndicator = new FloatingIndicator();

    console.log('✅ [AI Chat Memo] All components initialized, starting init...');
    this.init();
  }

  private async init(): Promise<void> {
    console.log('🔧 [AI Chat Memo] init() called');
    this.logger.info('Content service initializing...');

    // 等待页面完全加载
    if (document.readyState === 'loading') {
      console.log('📄 [AI Chat Memo] Document still loading, waiting for DOMContentLoaded');
      document.addEventListener('DOMContentLoaded', () => {
        console.log('📄 [AI Chat Memo] DOMContentLoaded fired, starting detection');
        this.startDetection();
      });
    } else {
      console.log('📄 [AI Chat Memo] Document already loaded, starting detection in 1s');
      // 页面已经加载完成
      setTimeout(() => {
        this.startDetection();
      }, 1000);
    }

    this.setupMessageListeners();
    console.log('✅ [AI Chat Memo] Content service initialized');
    this.logger.info('Content service initialized');
  }

  private async startDetection(): Promise<void> {
    console.log('🔍 [AI Chat Memo] startDetection() called');

    // 更新浮动指示器状态
    this.floatingIndicator.updateStatus('detecting', '检测平台中...');

    try {
      console.log('🔍 [AI Chat Memo] Starting detection engine...');
      await this.detectionEngine.start();

      const platform = this.detectionEngine.getCurrentPlatform();
      console.log('🔍 [AI Chat Memo] Detected platform:', platform);

      if (platform) {
        console.log(`✅ [AI Chat Memo] 开始监听 ${platform} 平台的对话内容`);
        this.logger.info(`开始监听 ${platform} 平台的对话内容`);

        // 更新浮动指示器
        this.floatingIndicator.updatePlatform(platform);
        this.floatingIndicator.updateStatus('idle', '监听中...');

        // 通知 background script 当前平台
        console.log('📡 [AI Chat Memo] Sending platform-detected message to background');
        await this.messageBus.send('platform-detected', {
          platform,
          url: window.location.href,
          supported: this.detectionEngine.isCurrentPageSupported()
        });
        console.log('📡 [AI Chat Memo] Platform-detected message sent successfully');
      } else {
        console.log('❌ [AI Chat Memo] 当前页面不支持或未检测到AI平台');
        this.logger.debug('当前页面不支持或未检测到AI平台');

        // 更新浮动指示器
        this.floatingIndicator.updatePlatform('未知');
        this.floatingIndicator.updateStatus('error', '不支持的平台');
      }
    } catch (error) {
      console.error('❌ [AI Chat Memo] 启动内容检测时出错:', error);
      this.logger.error('启动内容检测时出错:', error instanceof Error ? error : new Error(String(error)));

      // 更新浮动指示器
      this.floatingIndicator.updateStatus('error', '检测失败');
    }
  }

  private setupMessageListeners(): void {
    // 设置Chrome runtime消息监听器
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleRuntimeMessage(message, sender, sendResponse);
      return true; // 保持消息通道开放以支持异步响应
    });

    // 响应 ping 请求
    this.messageBus.on('ping', () => {
      const platform = this.detectionEngine.getCurrentPlatform();
      return {
        status: 'ok',
        platform: platform || 'unknown',
        supported: this.detectionEngine.isCurrentPageSupported(),
        url: window.location.href
      };
    });

    // 手动提取会话
    this.messageBus.on('extract-conversation', async () => {
      try {
        const conversation = await this.detectionEngine.extractConversation();
        return { success: true, conversation };
      } catch (error) {
        this.logger.error('手动提取会话失败:', error instanceof Error ? error : new Error(String(error)));
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });

    // 获取平台信息
    this.messageBus.on('get-platform-info', () => {
      return {
        platform: this.detectionEngine.getCurrentPlatform(),
        supported: this.detectionEngine.isCurrentPageSupported(),
        url: window.location.href
      };
    });

    // 停止检测
    this.messageBus.on('stop-detection', () => {
      this.detectionEngine.stop();
      return { success: true };
    });

    // 重启检测
    this.messageBus.on('restart-detection', async () => {
      try {
        this.detectionEngine.stop();
        await this.detectionEngine.start();
        return { success: true };
      } catch (error) {
        this.logger.error('重启检测失败:', error instanceof Error ? error : new Error(String(error)));
        return { success: false, error: error instanceof Error ? error.message : String(error) };
      }
    });
  }

  /**
   * 处理Chrome runtime消息
   */
  private async handleRuntimeMessage(
    message: any,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      this.logger.debug('收到runtime消息:', message);

      switch (message.type) {
        case 'ping':
          const platform = this.detectionEngine.getCurrentPlatform();
          sendResponse({
            success: true,
            status: 'ok',
            platform: platform || 'unknown',
            supported: this.detectionEngine.isCurrentPageSupported(),
            url: window.location.href
          });
          break;

        case 'save-current-conversation':
          try {
            this.floatingIndicator.updateStatus('saving', '保存中...');
            const conversation = await this.detectionEngine.extractConversation();

            if (conversation) {
              // 发送会话数据到background script保存
              const saveResponse = await chrome.runtime.sendMessage({
                type: 'save-conversation',
                data: conversation
              });

              if (saveResponse && saveResponse.success) {
                this.floatingIndicator.updateStatus('saved', '已保存');
              } else {
                this.floatingIndicator.updateStatus('error', '保存失败');
              }

              // 3秒后恢复到监听状态
              setTimeout(() => {
                this.floatingIndicator.updateStatus('idle', '监听中...');
              }, 3000);

              sendResponse({
                success: true,
                conversation,
                message: '会话已成功保存'
              });
            } else {
              this.floatingIndicator.updateStatus('error', '提取失败');
              setTimeout(() => {
                this.floatingIndicator.updateStatus('idle', '监听中...');
              }, 3000);

              sendResponse({
                success: false,
                error: '无法提取当前页面的会话内容，请确保页面已完全加载且包含对话内容'
              });
            }
          } catch (error) {
            this.logger.error('保存会话失败:', error);
            this.floatingIndicator.updateStatus('error', '保存失败');
            setTimeout(() => {
              this.floatingIndicator.updateStatus('idle', '监听中...');
            }, 3000);

            sendResponse({
              success: false,
              error: error instanceof Error ? error.message : '保存会话时发生未知错误'
            });
          }
          break;

        case 'extract-conversation':
          try {
            const conversation = await this.detectionEngine.extractConversation();
            sendResponse({ success: true, conversation });
          } catch (error) {
            this.logger.error('提取会话失败:', error);
            sendResponse({
              success: false,
              error: error instanceof Error ? error.message : '提取会话失败'
            });
          }
          break;

        case 'get-platform-info':
          sendResponse({
            success: true,
            platform: this.detectionEngine.getCurrentPlatform(),
            supported: this.detectionEngine.isCurrentPageSupported(),
            url: window.location.href
          });
          break;

        default:
          this.logger.warn('未知的消息类型:', message.type);
          sendResponse({ success: false, error: '未知的消息类型' });
      }
    } catch (error) {
      this.logger.error('处理runtime消息失败:', error);
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : '处理消息时发生未知错误'
      });
    }
  }

  // 清理资源
  cleanup(): void {
    console.log('🧹 [AI Chat Memo] Cleaning up ContentService');
    this.detectionEngine.cleanup();
    this.floatingIndicator.destroy();
  }
}

// 初始化服务
const contentService = new ContentService();

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  contentService.cleanup();
});

// 导出服务实例
export default contentService;






