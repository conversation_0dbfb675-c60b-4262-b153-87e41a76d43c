/**
 * 设置存储管理器
 * 负责用户设置的存储和管理
 */

import { DatabaseManager } from './database';
import { Logger } from '@shared/logger';

export interface AppSettings {
  // 自动保存设置
  autoSave: {
    enabled: boolean;
    interval: number; // 检查间隔（秒）
    platforms: string[]; // 启用的平台
  };

  // 标签设置
  tags: {
    autoTagging: boolean;
    suggestedTags: string[];
    maxTags: number;
  };

  // 搜索设置
  search: {
    maxResults: number;
    highlightMatches: boolean;
    searchHistory: string[];
    maxHistorySize: number;
  };

  // 导出设置
  export: {
    defaultFormat: 'markdown' | 'pdf' | 'json';
    includeMetadata: boolean;
    includeImages: boolean;
    dateFormat: string;
  };

  // UI设置
  ui: {
    theme: 'light' | 'dark' | 'auto';
    language: 'zh-CN' | 'en-US';
    showFloatingIndicator: boolean;
    indicatorPosition: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
    compactMode: boolean;
  };

  // 隐私设置
  privacy: {
    anonymizeData: boolean;
    excludePatterns: string[]; // 排除的内容模式
    dataRetentionDays: number; // 数据保留天数，0表示永久保留
  };

  // 同步设置
  sync: {
    enabled: boolean;
    provider: 'none' | 'google-drive' | 'dropbox' | 'onedrive';
    lastSyncTime?: Date;
    autoSync: boolean;
    syncInterval: number; // 小时
  };

  // 通知设置
  notifications: {
    enabled: boolean;
    showSaveSuccess: boolean;
    showErrors: boolean;
    position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  };
}

export class SettingsStore {
  private db: DatabaseManager;
  private logger: Logger;
  private cache: Map<string, any> = new Map();
  private defaultSettings: AppSettings;

  constructor(db: DatabaseManager) {
    this.db = db;
    this.logger = new Logger('SettingsStore');
    this.defaultSettings = this.getDefaultSettings();
  }

  /**
   * 初始化设置存储
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('初始化设置存储...');
      
      // 加载所有设置到缓存
      await this.loadAllSettings();
      
      // 确保所有默认设置都存在
      await this.ensureDefaultSettings();
      
      this.logger.info('设置存储初始化完成');
    } catch (error) {
      this.logger.error('设置存储初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取默认设置
   */
  private getDefaultSettings(): AppSettings {
    return {
      autoSave: {
        enabled: true,
        interval: 5,
        platforms: ['ChatGPT', 'Claude', 'Gemini', 'Aistudio', 'Monica', 'Poe']
      },
      tags: {
        autoTagging: true,
        suggestedTags: ['工作', '学习', '编程', '创意', '问答', '翻译'],
        maxTags: 10
      },
      search: {
        maxResults: 50,
        highlightMatches: true,
        searchHistory: [],
        maxHistorySize: 20
      },
      export: {
        defaultFormat: 'markdown',
        includeMetadata: true,
        includeImages: false,
        dateFormat: 'YYYY-MM-DD HH:mm:ss'
      },
      ui: {
        theme: 'auto',
        language: 'zh-CN',
        showFloatingIndicator: true,
        indicatorPosition: 'bottom-right',
        compactMode: false
      },
      privacy: {
        anonymizeData: false,
        excludePatterns: [],
        dataRetentionDays: 0
      },
      sync: {
        enabled: false,
        provider: 'none',
        autoSync: false,
        syncInterval: 24
      },
      notifications: {
        enabled: true,
        showSaveSuccess: false,
        showErrors: true,
        position: 'top-right'
      }
    };
  }

  /**
   * 加载所有设置到缓存
   */
  private async loadAllSettings(): Promise<void> {
    try {
      const settings = await this.db.getAll('settings');
      this.cache.clear();
      
      settings.forEach(setting => {
        this.cache.set(setting.key, setting.value);
      });
      
      this.logger.debug(`加载了 ${settings.length} 个设置项到缓存`);
    } catch (error) {
      this.logger.error('加载设置失败:', error);
      throw error;
    }
  }

  /**
   * 确保所有默认设置都存在
   */
  private async ensureDefaultSettings(): Promise<void> {
    const operations = [];
    
    const flattenSettings = (obj: any, prefix = ''): Array<{key: string, value: any}> => {
      const result: Array<{key: string, value: any}> = [];
      
      for (const [key, value] of Object.entries(obj)) {
        const fullKey = prefix ? `${prefix}.${key}` : key;
        
        if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
          result.push(...flattenSettings(value, fullKey));
        } else {
          result.push({ key: fullKey, value });
        }
      }
      
      return result;
    };

    const defaultEntries = flattenSettings(this.defaultSettings);
    
    for (const { key, value } of defaultEntries) {
      if (!this.cache.has(key)) {
        operations.push({
          type: 'add' as const,
          storeName: 'settings' as const,
          data: {
            key,
            value,
            updatedAt: new Date()
          }
        });
        
        this.cache.set(key, value);
      }
    }

    if (operations.length > 0) {
      await this.db.batch(operations);
      this.logger.info(`添加了 ${operations.length} 个默认设置`);
    }
  }

  /**
   * 获取设置值
   */
  async get<T = any>(key: string, defaultValue?: T): Promise<T> {
    try {
      if (this.cache.has(key)) {
        return this.cache.get(key) as T;
      }

      const setting = await this.db.get('settings', key);
      const value = setting ? setting.value : defaultValue;
      
      if (value !== undefined) {
        this.cache.set(key, value);
      }
      
      return value as T;
    } catch (error) {
      this.logger.error(`获取设置失败: ${key}`, error);
      return defaultValue as T;
    }
  }

  /**
   * 设置值
   */
  async set(key: string, value: any): Promise<void> {
    try {
      const settingData = {
        key,
        value,
        updatedAt: new Date()
      };

      await this.db.put('settings', settingData);
      this.cache.set(key, value);
      
      this.logger.debug(`设置已更新: ${key} = ${JSON.stringify(value)}`);
    } catch (error) {
      this.logger.error(`设置值失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 批量设置
   */
  async setMultiple(settings: Record<string, any>): Promise<void> {
    try {
      const operations = Object.entries(settings).map(([key, value]) => ({
        type: 'put' as const,
        storeName: 'settings' as const,
        data: {
          key,
          value,
          updatedAt: new Date()
        }
      }));

      await this.db.batch(operations);
      
      // 更新缓存
      Object.entries(settings).forEach(([key, value]) => {
        this.cache.set(key, value);
      });
      
      this.logger.info(`批量更新了 ${operations.length} 个设置`);
    } catch (error) {
      this.logger.error('批量设置失败:', error);
      throw error;
    }
  }

  /**
   * 删除设置
   */
  async delete(key: string): Promise<void> {
    try {
      await this.db.delete('settings', key);
      this.cache.delete(key);
      
      this.logger.debug(`设置已删除: ${key}`);
    } catch (error) {
      this.logger.error(`删除设置失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 获取完整的应用设置
   */
  async getAppSettings(): Promise<AppSettings> {
    try {
      const settings: any = {};
      
      // 重建嵌套对象结构
      for (const [key, value] of this.cache) {
        const keys = key.split('.');
        let current = settings;
        
        for (let i = 0; i < keys.length - 1; i++) {
          if (!current[keys[i]]) {
            current[keys[i]] = {};
          }
          current = current[keys[i]];
        }
        
        current[keys[keys.length - 1]] = value;
      }
      
      // 合并默认设置以确保完整性
      return this.mergeWithDefaults(settings, this.defaultSettings);
    } catch (error) {
      this.logger.error('获取应用设置失败:', error);
      return this.defaultSettings;
    }
  }

  /**
   * 更新应用设置
   */
  async updateAppSettings(settings: Partial<AppSettings>): Promise<void> {
    try {
      const flattenedSettings: Record<string, any> = {};
      
      const flatten = (obj: any, prefix = '') => {
        for (const [key, value] of Object.entries(obj)) {
          const fullKey = prefix ? `${prefix}.${key}` : key;
          
          if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
            flatten(value, fullKey);
          } else {
            flattenedSettings[fullKey] = value;
          }
        }
      };
      
      flatten(settings);
      await this.setMultiple(flattenedSettings);
      
      this.logger.info('应用设置已更新');
    } catch (error) {
      this.logger.error('更新应用设置失败:', error);
      throw error;
    }
  }

  /**
   * 重置为默认设置
   */
  async resetToDefaults(): Promise<void> {
    try {
      // 清空现有设置
      await this.db.clear('settings');
      this.cache.clear();
      
      // 重新初始化默认设置
      await this.ensureDefaultSettings();
      
      this.logger.info('设置已重置为默认值');
    } catch (error) {
      this.logger.error('重置设置失败:', error);
      throw error;
    }
  }

  /**
   * 导出设置
   */
  async exportSettings(): Promise<Record<string, any>> {
    try {
      const settings: Record<string, any> = {};
      
      for (const [key, value] of this.cache) {
        settings[key] = value;
      }
      
      return {
        settings,
        exportTime: new Date().toISOString(),
        version: '1.0.0'
      };
    } catch (error) {
      this.logger.error('导出设置失败:', error);
      throw error;
    }
  }

  /**
   * 导入设置
   */
  async importSettings(data: { settings: Record<string, any>; version?: string }): Promise<void> {
    try {
      if (!data.settings) {
        throw new Error('无效的设置数据');
      }
      
      await this.setMultiple(data.settings);
      
      this.logger.info(`导入了 ${Object.keys(data.settings).length} 个设置`);
    } catch (error) {
      this.logger.error('导入设置失败:', error);
      throw error;
    }
  }

  /**
   * 合并默认设置
   */
  private mergeWithDefaults(current: any, defaults: any): any {
    const result = { ...defaults };
    
    for (const [key, value] of Object.entries(current)) {
      if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
        result[key] = this.mergeWithDefaults(value, defaults[key] || {});
      } else {
        result[key] = value;
      }
    }
    
    return result;
  }

  /**
   * 获取所有设置键
   */
  getAllKeys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * 检查设置是否存在
   */
  has(key: string): boolean {
    return this.cache.has(key);
  }

  /**
   * 获取设置数量
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * 清理日志
   */
  async cleanupLogs(beforeDate: Date): Promise<void> {
    try {
      // 清理应用日志
      const logs = await this.get('appLogs', []);
      const filteredLogs = logs.filter((log: any) => new Date(log.timestamp) >= beforeDate);
      await this.set('appLogs', filteredLogs);

      // 清理错误日志
      const errorLogs = await this.get('errorLogs', []);
      const filteredErrorLogs = errorLogs.filter((log: any) => new Date(log.timestamp) >= beforeDate);
      await this.set('errorLogs', filteredErrorLogs);

      // 清理性能日志
      const performanceLogs = await this.get('performanceLogs', []);
      const filteredPerformanceLogs = performanceLogs.filter((log: any) => new Date(log.timestamp) >= beforeDate);
      await this.set('performanceLogs', filteredPerformanceLogs);

      this.logger.info(`日志清理完成，清理了 ${beforeDate.toISOString()} 之前的日志`);
    } catch (error) {
      this.logger.error('日志清理失败:', error);
      throw error;
    }
  }
}
