/**
 * 平台适配器集成测试
 */

import { AdapterManager } from '../../src/content/adapters/adapter-manager';
import { ChatGPTAdapter } from '../../src/content/adapters/chatgpt';
import { ClaudeAdapter } from '../../src/content/adapters/claude';
import { GeminiAdapter } from '../../src/content/adapters/gemini';
import { createMockElement } from '../setup';

describe('平台适配器集成测试', () => {
  let adapterManager: AdapterManager;

  beforeEach(() => {
    adapterManager = new AdapterManager();
    
    // 清理DOM
    document.body.innerHTML = '';
    document.head.innerHTML = '';
  });

  afterEach(() => {
    document.body.innerHTML = '';
    document.head.innerHTML = '';
  });

  describe('适配器检测', () => {
    test('应该能够检测ChatGPT平台', () => {
      // 模拟ChatGPT页面
      Object.defineProperty(window, 'location', {
        value: { hostname: 'chat.openai.com', href: 'https://chat.openai.com/c/123' },
        writable: true
      });

      const adapter = adapterManager.detectPlatform();
      expect(adapter).toBeInstanceOf(ChatGPTAdapter);
      expect(adapter?.getPlatformName()).toBe('ChatGPT');
    });

    test('应该能够检测Claude平台', () => {
      Object.defineProperty(window, 'location', {
        value: { hostname: 'claude.ai', href: 'https://claude.ai/chat/456' },
        writable: true
      });

      const adapter = adapterManager.detectPlatform();
      expect(adapter).toBeInstanceOf(ClaudeAdapter);
      expect(adapter?.getPlatformName()).toBe('Claude');
    });

    test('应该能够检测Gemini平台', () => {
      Object.defineProperty(window, 'location', {
        value: { hostname: 'gemini.google.com', href: 'https://gemini.google.com/app/789' },
        writable: true
      });

      const adapter = adapterManager.detectPlatform();
      expect(adapter).toBeInstanceOf(GeminiAdapter);
      expect(adapter?.getPlatformName()).toBe('Gemini');
    });

    test('应该在未知平台返回null', () => {
      Object.defineProperty(window, 'location', {
        value: { hostname: 'unknown.com', href: 'https://unknown.com' },
        writable: true
      });

      const adapter = adapterManager.detectPlatform();
      expect(adapter).toBeNull();
    });
  });

  describe('ChatGPT适配器集成', () => {
    let chatgptAdapter: ChatGPTAdapter;

    beforeEach(() => {
      chatgptAdapter = new ChatGPTAdapter();
      
      // 模拟ChatGPT页面环境
      Object.defineProperty(window, 'location', {
        value: { hostname: 'chat.openai.com', href: 'https://chat.openai.com/c/test-123' },
        writable: true
      });
    });

    test('应该能够检测对话容器', () => {
      // 创建模拟的ChatGPT DOM结构
      const mainContainer = createMockElement('main', { 'data-testid': 'conversation-turn-3' });
      document.body.appendChild(mainContainer);

      const container = chatgptAdapter.detectConversationContainer();
      expect(container).toBe(mainContainer);
    });

    test('应该能够提取对话标题', () => {
      // 创建标题元素
      const titleElement = createMockElement('h1');
      titleElement.textContent = 'JavaScript异步编程讨论';
      document.body.appendChild(titleElement);

      const title = chatgptAdapter.extractConversationTitle();
      expect(title).toBe('JavaScript异步编程讨论');
    });

    test('应该能够提取消息', () => {
      // 创建消息容器
      const container = createMockElement('main');
      
      // 用户消息
      const userMessage = createMockElement('div', { 'data-message-author-role': 'user' });
      const userContent = createMockElement('div');
      userContent.textContent = '请解释Promise的工作原理';
      userMessage.appendChild(userContent);
      container.appendChild(userMessage);

      // AI消息
      const aiMessage = createMockElement('div', { 'data-message-author-role': 'assistant' });
      const aiContent = createMockElement('div');
      aiContent.textContent = 'Promise是JavaScript中处理异步操作的对象...';
      aiMessage.appendChild(aiContent);
      container.appendChild(aiMessage);

      document.body.appendChild(container);

      const messages = chatgptAdapter.extractMessages();
      expect(messages).toHaveLength(2);
      expect(messages[0].role).toBe('user');
      expect(messages[0].content).toBe('请解释Promise的工作原理');
      expect(messages[1].role).toBe('assistant');
      expect(messages[1].content).toBe('Promise是JavaScript中处理异步操作的对象...');
    });

    test('应该能够提取完整对话数据', () => {
      // 设置完整的页面结构
      const title = createMockElement('h1');
      title.textContent = 'Promise学习笔记';
      document.head.appendChild(title);

      const container = createMockElement('main');
      
      const userMsg = createMockElement('div', { 'data-message-author-role': 'user' });
      userMsg.textContent = '什么是Promise？';
      container.appendChild(userMsg);

      const aiMsg = createMockElement('div', { 'data-message-author-role': 'assistant' });
      aiMsg.textContent = 'Promise是异步编程的解决方案';
      container.appendChild(aiMsg);

      document.body.appendChild(container);

      const conversationData = chatgptAdapter.extractConversationData();
      
      expect(conversationData).not.toBeNull();
      expect(conversationData!.platform).toBe('ChatGPT');
      expect(conversationData!.title).toBe('Promise学习笔记');
      expect(conversationData!.url).toBe('https://chat.openai.com/c/test-123');
      expect(conversationData!.messages).toHaveLength(2);
    });
  });

  describe('适配器管理器集成', () => {
    test('应该能够自动检测并提取对话', () => {
      // 模拟Claude页面
      Object.defineProperty(window, 'location', {
        value: { hostname: 'claude.ai', href: 'https://claude.ai/chat/test-456' },
        writable: true
      });

      // 创建Claude页面结构
      const title = createMockElement('h1');
      title.textContent = 'React性能优化';
      document.body.appendChild(title);

      const container = createMockElement('div', { 'class': 'conversation-container' });
      
      const userMsg = createMockElement('div', { 'data-role': 'user' });
      userMsg.textContent = '如何优化React性能？';
      container.appendChild(userMsg);

      const aiMsg = createMockElement('div', { 'data-role': 'assistant' });
      aiMsg.textContent = '可以使用React.memo、useMemo等方法';
      container.appendChild(aiMsg);

      document.body.appendChild(container);

      const conversationData = adapterManager.extractCurrentConversation();
      
      expect(conversationData).not.toBeNull();
      expect(conversationData!.platform).toBe('Claude');
      expect(conversationData!.title).toBe('React性能优化');
    });

    test('应该能够监听页面变化', (done) => {
      // 模拟ChatGPT页面
      Object.defineProperty(window, 'location', {
        value: { hostname: 'chat.openai.com', href: 'https://chat.openai.com/c/test' },
        writable: true
      });

      let changeDetected = false;
      adapterManager.startMonitoring((conversationData) => {
        if (conversationData && !changeDetected) {
          changeDetected = true;
          expect(conversationData.platform).toBe('ChatGPT');
          adapterManager.stopMonitoring();
          done();
        }
      });

      // 模拟页面内容变化
      setTimeout(() => {
        const container = createMockElement('main');
        const message = createMockElement('div', { 'data-message-author-role': 'user' });
        message.textContent = '新消息';
        container.appendChild(message);
        document.body.appendChild(container);

        // 触发MutationObserver
        const event = new Event('DOMContentLoaded');
        document.dispatchEvent(event);
      }, 100);
    });

    test('应该能够处理多个平台切换', () => {
      // 首先在ChatGPT
      Object.defineProperty(window, 'location', {
        value: { hostname: 'chat.openai.com', href: 'https://chat.openai.com/c/1' },
        writable: true
      });

      let adapter = adapterManager.detectPlatform();
      expect(adapter?.getPlatformName()).toBe('ChatGPT');

      // 切换到Claude
      Object.defineProperty(window, 'location', {
        value: { hostname: 'claude.ai', href: 'https://claude.ai/chat/2' },
        writable: true
      });

      adapter = adapterManager.detectPlatform();
      expect(adapter?.getPlatformName()).toBe('Claude');

      // 切换到Gemini
      Object.defineProperty(window, 'location', {
        value: { hostname: 'gemini.google.com', href: 'https://gemini.google.com/app/3' },
        writable: true
      });

      adapter = adapterManager.detectPlatform();
      expect(adapter?.getPlatformName()).toBe('Gemini');
    });
  });

  describe('错误处理和边界情况', () => {
    test('应该处理空页面', () => {
      Object.defineProperty(window, 'location', {
        value: { hostname: 'chat.openai.com', href: 'https://chat.openai.com/c/empty' },
        writable: true
      });

      const conversationData = adapterManager.extractCurrentConversation();
      expect(conversationData).toBeNull();
    });

    test('应该处理损坏的DOM结构', () => {
      Object.defineProperty(window, 'location', {
        value: { hostname: 'claude.ai', href: 'https://claude.ai/chat/broken' },
        writable: true
      });

      // 创建不完整的DOM结构
      const brokenElement = createMockElement('div');
      brokenElement.innerHTML = '<span>broken</span>';
      document.body.appendChild(brokenElement);

      expect(() => {
        adapterManager.extractCurrentConversation();
      }).not.toThrow();
    });

    test('应该处理网络错误', () => {
      Object.defineProperty(window, 'location', {
        value: { hostname: 'offline.com', href: 'https://offline.com' },
        writable: true
      });

      const adapter = adapterManager.detectPlatform();
      expect(adapter).toBeNull();
    });

    test('应该处理动态加载的内容', (done) => {
      Object.defineProperty(window, 'location', {
        value: { hostname: 'chat.openai.com', href: 'https://chat.openai.com/c/dynamic' },
        writable: true
      });

      adapterManager.startMonitoring((conversationData) => {
        if (conversationData) {
          expect(conversationData.messages).toHaveLength(1);
          adapterManager.stopMonitoring();
          done();
        }
      });

      // 模拟动态内容加载
      setTimeout(() => {
        const container = createMockElement('main');
        const message = createMockElement('div', { 'data-message-author-role': 'user' });
        message.textContent = '动态加载的消息';
        container.appendChild(message);
        document.body.appendChild(container);
      }, 50);
    });
  });

  describe('性能测试', () => {
    test('应该能够快速检测平台', () => {
      const startTime = performance.now();
      
      for (let i = 0; i < 1000; i++) {
        Object.defineProperty(window, 'location', {
          value: { hostname: 'chat.openai.com', href: `https://chat.openai.com/c/${i}` },
          writable: true
        });
        adapterManager.detectPlatform();
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(100); // 应该在100ms内完成1000次检测
    });

    test('应该能够处理大量消息', () => {
      Object.defineProperty(window, 'location', {
        value: { hostname: 'chat.openai.com', href: 'https://chat.openai.com/c/large' },
        writable: true
      });

      const container = createMockElement('main');
      
      // 创建大量消息
      for (let i = 0; i < 1000; i++) {
        const message = createMockElement('div', { 
          'data-message-author-role': i % 2 === 0 ? 'user' : 'assistant' 
        });
        message.textContent = `消息 ${i + 1}`;
        container.appendChild(message);
      }
      
      document.body.appendChild(container);

      const startTime = performance.now();
      const conversationData = adapterManager.extractCurrentConversation();
      const endTime = performance.now();
      
      expect(conversationData).not.toBeNull();
      expect(conversationData!.messages).toHaveLength(1000);
      expect(endTime - startTime).toBeLessThan(1000); // 应该在1秒内完成
    });
  });
});
