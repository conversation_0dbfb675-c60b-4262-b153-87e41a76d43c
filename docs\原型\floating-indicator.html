<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>悬浮状态指示器 - AI会话管理插件</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @keyframes pulse-ring {
            0% { transform: scale(0.8); opacity: 1; }
            100% { transform: scale(2.4); opacity: 0; }
        }
        
        @keyframes pulse-dot {
            0% { transform: scale(0.8); }
            50% { transform: scale(1.0); }
            100% { transform: scale(0.8); }
        }
        
        .pulse-ring {
            animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
        }
        
        .pulse-dot {
            animation: pulse-dot 1.25s cubic-bezier(0.455, 0.03, 0.515, 0.955) -.4s infinite;
        }
        
        @keyframes slideInUp {
            from { transform: translateY(100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        @keyframes slideOutDown {
            from { transform: translateY(0); opacity: 1; }
            to { transform: translateY(100%); opacity: 0; }
        }
        
        .slide-in-up {
            animation: slideInUp 0.3s ease-out;
        }
        
        .slide-out-down {
            animation: slideOutDown 0.3s ease-in;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen p-8">
    <!-- 模拟页面内容 -->
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">悬浮状态指示器设计</h1>
        
        <!-- 控制面板 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">状态控制</h2>
            <div class="flex flex-wrap gap-3">
                <button onclick="showStatus('idle')" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    空闲状态
                </button>
                <button onclick="showStatus('saving')" class="bg-blue-100 hover:bg-blue-200 text-blue-700 px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    保存中
                </button>
                <button onclick="showStatus('success')" class="bg-green-100 hover:bg-green-200 text-green-700 px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    保存成功
                </button>
                <button onclick="showStatus('error')" class="bg-red-100 hover:bg-red-200 text-red-700 px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    保存失败
                </button>
                <button onclick="showStatus('offline')" class="bg-yellow-100 hover:bg-yellow-200 text-yellow-700 px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    离线模式
                </button>
                <button onclick="showFirstTime()" class="bg-purple-100 hover:bg-purple-200 text-purple-700 px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    首次使用提示
                </button>
            </div>
        </div>

        <!-- 示例内容 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">模拟聊天页面</h2>
            <div class="space-y-4">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <p class="text-gray-800">用户: 如何设计一个好的用户界面？</p>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <p class="text-gray-800">AI: 设计好的用户界面需要考虑以下几个方面...</p>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg">
                    <p class="text-gray-800">用户: 能具体说说颜色搭配吗？</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 悬浮状态指示器 - 右下角 -->
    <div id="floatingIndicator" class="fixed bottom-6 right-6 z-50 hidden">
        <!-- 空闲状态 -->
        <div id="idleState" class="hidden">
            <div class="w-12 h-12 bg-gray-500 rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:bg-gray-600 transition-colors" onclick="togglePanel()">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
            </div>
        </div>

        <!-- 保存中状态 -->
        <div id="savingState" class="hidden">
            <div class="relative w-12 h-12">
                <div class="absolute inset-0 w-12 h-12 bg-blue-200 rounded-full pulse-ring"></div>
                <div class="relative w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center shadow-lg pulse-dot">
                    <svg class="w-6 h-6 text-white animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- 成功状态 -->
        <div id="successState" class="hidden">
            <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:bg-green-600 transition-colors" onclick="togglePanel()">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
        </div>

        <!-- 错误状态 -->
        <div id="errorState" class="hidden">
            <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:bg-red-600 transition-colors" onclick="togglePanel()">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
        </div>

        <!-- 离线状态 -->
        <div id="offlineState" class="hidden">
            <div class="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:bg-yellow-600 transition-colors" onclick="togglePanel()">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 1v6m0 10v6m11-7h-6M7 12H1"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- 详细信息面板 -->
    <div id="detailPanel" class="fixed bottom-20 right-6 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-50 hidden">
        <div class="p-4">
            <div class="flex items-center justify-between mb-3">
                <h3 class="text-lg font-medium text-gray-900">AI会话管理</h3>
                <button onclick="closePanel()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div id="panelContent">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>

    <!-- 首次使用引导 -->
    <div id="firstTimeGuide" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="absolute bottom-6 right-6">
            <!-- 指向悬浮按钮的箭头 -->
            <div class="absolute -top-8 right-6 w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-white"></div>
            
            <div class="bg-white rounded-lg shadow-xl p-6 w-80">
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-medium text-gray-900 mb-2">欢迎使用AI会话管理插件！</h3>
                        <p class="text-sm text-gray-600 mb-4">我们会自动保存您的AI对话，点击这个按钮可以查看保存状态和管理您的会话。</p>
                        <div class="flex space-x-3">
                            <button onclick="closeFirstTime()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                                知道了
                            </button>
                            <button onclick="closeFirstTime()" class="text-gray-500 hover:text-gray-700 text-sm">
                                不再提示
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知消息 -->
    <div id="notification" class="fixed top-6 right-6 z-50 hidden">
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
            <div class="flex items-start space-x-3">
                <div id="notificationIcon" class="flex-shrink-0 mt-0.5">
                    <!-- 动态图标 -->
                </div>
                <div class="flex-1">
                    <p id="notificationText" class="text-sm text-gray-800"></p>
                </div>
                <button onclick="closeNotification()" class="text-gray-400 hover:text-gray-600 flex-shrink-0">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentState = 'idle';
        let panelOpen = false;

        function showStatus(state) {
            currentState = state;
            
            // 隐藏所有状态
            document.querySelectorAll('#floatingIndicator > div').forEach(el => {
                el.classList.add('hidden');
            });
            
            // 显示指示器
            document.getElementById('floatingIndicator').classList.remove('hidden');
            
            // 显示对应状态
            document.getElementById(state + 'State').classList.remove('hidden');
            
            // 更新面板内容
            updatePanelContent(state);
            
            // 显示通知（某些状态）
            if (state === 'success') {
                showNotification('success', '会话已成功保存到本地');
            } else if (state === 'error') {
                showNotification('error', '保存失败，请检查网络连接');
            }
            
            // 自动隐藏成功状态
            if (state === 'success') {
                setTimeout(() => {
                    if (currentState === 'success') {
                        showStatus('idle');
                    }
                }, 3000);
            }
        }

        function updatePanelContent(state) {
            const content = document.getElementById('panelContent');
            
            switch(state) {
                case 'idle':
                    content.innerHTML = `
                        <div class="space-y-3">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                                <span class="text-sm text-gray-600">等待新的对话内容</span>
                            </div>
                            <div class="text-xs text-gray-500">
                                <p>今日已保存: 5 条对话</p>
                                <p>总计: 1,234 条对话</p>
                            </div>
                            <button class="w-full bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm font-medium transition-colors">
                                打开管理面板
                            </button>
                        </div>
                    `;
                    break;
                case 'saving':
                    content.innerHTML = `
                        <div class="space-y-3">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                <span class="text-sm text-gray-800">正在保存对话...</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full animate-pulse" style="width: 65%"></div>
                            </div>
                            <div class="text-xs text-gray-500">
                                <p>检测到新的问答内容</p>
                                <p>正在处理和保存...</p>
                            </div>
                        </div>
                    `;
                    break;
                case 'success':
                    content.innerHTML = `
                        <div class="space-y-3">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span class="text-sm text-gray-800">保存成功</span>
                            </div>
                            <div class="text-xs text-gray-500">
                                <p>✓ 1 个新的问答对已保存</p>
                                <p>✓ 标签已自动生成</p>
                                <p>✓ 内容已索引</p>
                            </div>
                            <button class="w-full bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm font-medium transition-colors">
                                查看保存的内容
                            </button>
                        </div>
                    `;
                    break;
                case 'error':
                    content.innerHTML = `
                        <div class="space-y-3">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                <span class="text-sm text-gray-800">保存失败</span>
                            </div>
                            <div class="text-xs text-gray-500">
                                <p>⚠ 网络连接异常</p>
                                <p>内容已暂存到本地缓存</p>
                            </div>
                            <div class="flex space-x-2">
                                <button class="flex-1 bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-sm font-medium transition-colors">
                                    重试
                                </button>
                                <button class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded text-sm font-medium transition-colors">
                                    稍后
                                </button>
                            </div>
                        </div>
                    `;
                    break;
                case 'offline':
                    content.innerHTML = `
                        <div class="space-y-3">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                <span class="text-sm text-gray-800">离线模式</span>
                            </div>
                            <div class="text-xs text-gray-500">
                                <p>📱 内容保存到本地存储</p>
                                <p>🔄 网络恢复后自动同步</p>
                            </div>
                            <div class="bg-yellow-50 border border-yellow-200 rounded p-2">
                                <p class="text-xs text-yellow-800">缓存队列: 3 条待同步</p>
                            </div>
                        </div>
                    `;
                    break;
            }
        }

        function togglePanel() {
            const panel = document.getElementById('detailPanel');
            if (panelOpen) {
                closePanel();
            } else {
                panel.classList.remove('hidden');
                panel.classList.add('slide-in-up');
                panelOpen = true;
            }
        }

        function closePanel() {
            const panel = document.getElementById('detailPanel');
            panel.classList.add('slide-out-down');
            setTimeout(() => {
                panel.classList.add('hidden');
                panel.classList.remove('slide-in-up', 'slide-out-down');
                panelOpen = false;
            }, 300);
        }

        function showFirstTime() {
            document.getElementById('firstTimeGuide').classList.remove('hidden');
            showStatus('idle');
        }

        function closeFirstTime() {
            document.getElementById('firstTimeGuide').classList.add('hidden');
        }

        function showNotification(type, message) {
            const notification = document.getElementById('notification');
            const icon = document.getElementById('notificationIcon');
            const text = document.getElementById('notificationText');
            
            // 设置图标
            if (type === 'success') {
                icon.innerHTML = `
                    <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                `;
            } else if (type === 'error') {
                icon.innerHTML = `
                    <div class="w-5 h-5 bg-red-100 rounded-full flex items-center justify-center">
                        <svg class="w-3 h-3 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                `;
            }
            
            text.textContent = message;
            notification.classList.remove('hidden');
            notification.classList.add('slide-in-up');
            
            // 自动隐藏
            setTimeout(() => {
                closeNotification();
            }, 4000);
        }

        function closeNotification() {
            const notification = document.getElementById('notification');
            notification.classList.add('slide-out-down');
            setTimeout(() => {
                notification.classList.add('hidden');
                notification.classList.remove('slide-in-up', 'slide-out-down');
            }, 300);
        }

        // 初始化
        showStatus('idle');
    </script>
</body>
</html>
