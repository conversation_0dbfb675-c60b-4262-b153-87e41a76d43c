/**
 * 搜索索引管理器
 * 负责全文搜索索引的创建、更新和查询
 */

import { ConversationData, MessageData } from '@types/conversation';
import { DatabaseManager } from './database';
import { Logger } from '@shared/logger';

export interface SearchResult {
  conversationId: string;
  title: string;
  platform: string;
  matches: SearchMatch[];
  score: number;
  createdAt: Date;
}

export interface SearchMatch {
  type: 'title' | 'message' | 'tag';
  content: string;
  highlight: string;
  messageId?: string;
  messageType?: 'user' | 'assistant';
}

export interface SearchOptions {
  platforms?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  messageTypes?: ('user' | 'assistant')[];
  limit?: number;
  offset?: number;
}

export class SearchIndexManager {
  private db: DatabaseManager;
  private logger: Logger;

  constructor(db: DatabaseManager) {
    this.db = db;
    this.logger = new Logger('SearchIndexManager');
  }

  /**
   * 为会话创建搜索索引
   */
  async indexConversation(conversation: ConversationData): Promise<void> {
    try {
      const operations = [];

      // 索引会话标题
      if (conversation.title.trim()) {
        operations.push({
          type: 'add' as const,
          storeName: 'searchIndex' as const,
          data: {
            id: `${conversation.id}_title`,
            conversationId: conversation.id,
            content: this.normalizeText(conversation.title),
            type: 'title' as const,
            platform: conversation.platform,
            createdAt: conversation.createdAt
          }
        });
      }

      // 索引标签
      conversation.tags.forEach((tag, index) => {
        operations.push({
          type: 'add' as const,
          storeName: 'searchIndex' as const,
          data: {
            id: `${conversation.id}_tag_${index}`,
            conversationId: conversation.id,
            content: this.normalizeText(tag),
            type: 'tag' as const,
            platform: conversation.platform,
            createdAt: conversation.createdAt
          }
        });
      });

      // 索引消息内容
      conversation.messages.forEach(message => {
        if (message.content.trim()) {
          operations.push({
            type: 'add' as const,
            storeName: 'searchIndex' as const,
            data: {
              id: `${conversation.id}_message_${message.id}`,
              conversationId: conversation.id,
              content: this.normalizeText(this.extractTextFromMessage(message)),
              type: 'message' as const,
              platform: conversation.platform,
              createdAt: conversation.createdAt,
              messageId: message.id,
              messageType: message.type
            }
          });
        }
      });

      // 批量执行索引操作
      if (operations.length > 0) {
        await this.db.batch(operations);
        this.logger.debug(`为会话 ${conversation.title} 创建了 ${operations.length} 个搜索索引`);
      }
    } catch (error) {
      this.logger.error('创建搜索索引失败:', error);
      throw error;
    }
  }

  /**
   * 更新会话搜索索引
   */
  async updateConversationIndex(conversation: ConversationData): Promise<void> {
    try {
      // 先删除现有索引
      await this.removeConversationIndex(conversation.id);
      
      // 重新创建索引
      await this.indexConversation(conversation);
    } catch (error) {
      this.logger.error('更新搜索索引失败:', error);
      throw error;
    }
  }

  /**
   * 删除会话搜索索引
   */
  async removeConversationIndex(conversationId: string): Promise<void> {
    try {
      const indices = await this.db.getByIndex('searchIndex', 'conversationId', conversationId);
      
      const operations = indices.map(index => ({
        type: 'delete' as const,
        storeName: 'searchIndex' as const,
        key: index.id
      }));

      if (operations.length > 0) {
        await this.db.batch(operations);
        this.logger.debug(`删除了会话 ${conversationId} 的 ${operations.length} 个搜索索引`);
      }
    } catch (error) {
      this.logger.error('删除搜索索引失败:', error);
      throw error;
    }
  }

  /**
   * 执行全文搜索
   */
  async search(query: string, options: SearchOptions = {}): Promise<SearchResult[]> {
    try {
      if (!query.trim()) {
        return [];
      }

      const normalizedQuery = this.normalizeText(query);
      const searchTerms = this.tokenize(normalizedQuery);
      
      if (searchTerms.length === 0) {
        return [];
      }

      // 获取所有搜索索引
      let indices = await this.db.getAll('searchIndex');

      // 应用平台过滤
      if (options.platforms && options.platforms.length > 0) {
        indices = indices.filter(index => options.platforms!.includes(index.platform));
      }

      // 应用日期范围过滤
      if (options.dateRange) {
        indices = indices.filter(index => 
          index.createdAt >= options.dateRange!.start && 
          index.createdAt <= options.dateRange!.end
        );
      }

      // 执行搜索匹配
      const matches = new Map<string, SearchMatch[]>();
      const scores = new Map<string, number>();

      for (const index of indices) {
        const matchResult = this.matchContent(index.content, searchTerms, query);
        
        if (matchResult.score > 0) {
          const conversationId = index.conversationId;
          
          if (!matches.has(conversationId)) {
            matches.set(conversationId, []);
            scores.set(conversationId, 0);
          }

          // 创建搜索匹配项
          const searchMatch: SearchMatch = {
            type: index.type,
            content: index.content,
            highlight: matchResult.highlight,
            messageId: (index as any).messageId,
            messageType: (index as any).messageType
          };

          matches.get(conversationId)!.push(searchMatch);
          
          // 累加分数，标题匹配权重更高
          const weight = index.type === 'title' ? 3 : index.type === 'tag' ? 2 : 1;
          scores.set(conversationId, scores.get(conversationId)! + matchResult.score * weight);
        }
      }

      // 获取匹配的会话信息
      const results: SearchResult[] = [];
      
      for (const [conversationId, conversationMatches] of matches) {
        try {
          const conversation = await this.db.get('conversations', conversationId);
          if (conversation) {
            results.push({
              conversationId,
              title: conversation.title,
              platform: conversation.platform,
              matches: conversationMatches,
              score: scores.get(conversationId)!,
              createdAt: conversation.createdAt
            });
          }
        } catch (error) {
          this.logger.warn(`获取会话信息失败: ${conversationId}`, error);
        }
      }

      // 按分数排序
      results.sort((a, b) => b.score - a.score);

      // 应用分页
      const offset = options.offset || 0;
      const limit = options.limit || 50;
      
      return results.slice(offset, offset + limit);
    } catch (error) {
      this.logger.error('搜索失败:', error);
      throw error;
    }
  }

  /**
   * 获取搜索建议
   */
  async getSuggestions(query: string, limit: number = 10): Promise<string[]> {
    try {
      if (!query.trim()) {
        return [];
      }

      const normalizedQuery = this.normalizeText(query).toLowerCase();
      const indices = await this.db.getAll('searchIndex');
      
      const suggestions = new Set<string>();
      
      for (const index of indices) {
        const content = index.content.toLowerCase();
        
        // 查找包含查询词的内容
        if (content.includes(normalizedQuery)) {
          // 提取相关词汇
          const words = this.tokenize(content);
          words.forEach(word => {
            if (word.includes(normalizedQuery) && word.length > normalizedQuery.length) {
              suggestions.add(word);
            }
          });
        }
        
        if (suggestions.size >= limit) {
          break;
        }
      }

      return Array.from(suggestions).slice(0, limit);
    } catch (error) {
      this.logger.error('获取搜索建议失败:', error);
      return [];
    }
  }

  /**
   * 文本标准化
   */
  private normalizeText(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, ' ') // 保留字母、数字、空格和中文
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 文本分词
   */
  private tokenize(text: string): string[] {
    // 简单的分词实现，可以根据需要改进
    const words = text.split(/\s+/).filter(word => word.length > 1);
    
    // 对中文进行简单的字符分割
    const result: string[] = [];
    
    for (const word of words) {
      if (/[\u4e00-\u9fff]/.test(word)) {
        // 中文字符，按字符分割
        for (let i = 0; i < word.length; i++) {
          result.push(word[i]);
          // 也添加双字组合
          if (i < word.length - 1) {
            result.push(word.substring(i, i + 2));
          }
        }
      } else {
        // 英文单词
        result.push(word);
      }
    }
    
    return [...new Set(result)]; // 去重
  }

  /**
   * 内容匹配
   */
  private matchContent(content: string, searchTerms: string[], originalQuery: string): {
    score: number;
    highlight: string;
  } {
    let score = 0;
    let highlight = content;
    
    // 完全匹配得分最高
    if (content.includes(originalQuery.toLowerCase())) {
      score += 10;
      highlight = this.highlightText(highlight, originalQuery);
    }
    
    // 词汇匹配
    for (const term of searchTerms) {
      if (content.includes(term)) {
        score += term.length > 1 ? 2 : 1; // 长词汇得分更高
        highlight = this.highlightText(highlight, term);
      }
    }
    
    return { score, highlight };
  }

  /**
   * 高亮文本
   */
  private highlightText(text: string, term: string): string {
    const regex = new RegExp(`(${this.escapeRegex(term)})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }

  /**
   * 转义正则表达式特殊字符
   */
  private escapeRegex(text: string): string {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * 从消息中提取纯文本
   */
  private extractTextFromMessage(message: MessageData): string {
    // 移除HTML标签和Markdown格式
    return message.content
      .replace(/<[^>]*>/g, '') // 移除HTML标签
      .replace(/```[\s\S]*?```/g, '[代码块]') // 替换代码块
      .replace(/`[^`]*`/g, '[代码]') // 替换行内代码
      .replace(/!\[.*?\]\(.*?\)/g, '[图片]') // 替换图片
      .replace(/\[.*?\]\(.*?\)/g, '[链接]') // 替换链接
      .replace(/[#*_~`]/g, '') // 移除Markdown标记
      .replace(/\s+/g, ' ') // 合并空白字符
      .trim();
  }

  /**
   * 重建所有搜索索引
   */
  async rebuildIndex(): Promise<void> {
    try {
      this.logger.info('开始重建搜索索引...');
      
      // 清空现有索引
      await this.db.clear('searchIndex');
      
      // 获取所有会话
      const conversations = await this.db.getAll('conversations');
      
      // 为每个会话重建索引
      for (const conversation of conversations) {
        // 获取会话的完整消息
        const messages = await this.db.getByIndex('messages', 'conversationId', conversation.id);
        conversation.messages = messages;
        
        await this.indexConversation(conversation);
      }
      
      this.logger.info(`搜索索引重建完成，处理了 ${conversations.length} 个会话`);
    } catch (error) {
      this.logger.error('重建搜索索引失败:', error);
      throw error;
    }
  }

  /**
   * 清理过期缓存
   */
  async clearExpiredCache(): Promise<void> {
    try {
      // 清理搜索结果缓存
      this.searchCache.clear();

      // 清理过期的搜索索引（超过30天未更新的）
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const allIndexes = await this.db.getAll('searchIndex');

      const expiredIndexes = allIndexes.filter(index => {
        // 如果索引没有更新时间，使用创建时间
        const updateTime = index.createdAt || new Date(0);
        return updateTime < thirtyDaysAgo;
      });

      if (expiredIndexes.length > 0) {
        const deleteOps = expiredIndexes.map(index => ({
          type: 'delete' as const,
          storeName: 'searchIndex' as const,
          key: index.id
        }));

        await this.db.batch(deleteOps);
        this.logger.info(`清理了 ${expiredIndexes.length} 条过期搜索索引`);
      }

      this.logger.debug('搜索缓存清理完成');
    } catch (error) {
      this.logger.error('搜索缓存清理失败:', error);
      throw error;
    }
  }
}
