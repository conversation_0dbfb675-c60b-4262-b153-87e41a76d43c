/**
 * 标签管理器辅助方法
 * 包含标签管理器的私有辅助方法
 */

import { ConversationData } from '@types/conversation';
import { TagRule, TagCondition, TagSuggestion } from './tag-manager';
import { storageService } from '@storage/index';
import { Logger } from '@shared/logger';

export class TagManagerHelpers {
  private logger: Logger;

  constructor() {
    this.logger = new Logger('TagManagerHelpers');
  }

  /**
   * 生成基于规则的标签
   */
  async generateRuleTags(conversation: ConversationData, tagRules: Map<string, TagRule>): Promise<TagSuggestion[]> {
    const suggestions: TagSuggestion[] = [];
    
    for (const rule of tagRules.values()) {
      if (!rule.enabled) continue;
      
      const matches = await this.evaluateTagRule(rule, conversation);
      if (matches) {
        rule.actions.forEach(action => {
          if (action.type === 'add_tag') {
            suggestions.push({
              tag: action.value,
              confidence: 0.85,
              reason: `匹配规则: ${rule.name}`,
              source: 'auto'
            });
          }
        });
      }
    }
    
    return suggestions;
  }

  /**
   * 生成基于历史的标签
   */
  async generateHistoryTags(conversation: ConversationData): Promise<TagSuggestion[]> {
    const suggestions: TagSuggestion[] = [];
    
    try {
      // 查找相似的会话
      const result = await storageService.conversations.getConversations({
        platforms: [conversation.platform]
      });
      
      const similarConversations = result.conversations.filter(conv => {
        if (conv.id === conversation.id) return false;
        
        // 简单的相似度计算
        const titleSimilarity = this.calculateTextSimilarity(
          conversation.title.toLowerCase(),
          conv.title.toLowerCase()
        );
        
        return titleSimilarity > 0.3;
      });
      
      // 统计相似会话的标签
      const tagCounts = new Map<string, number>();
      similarConversations.forEach(conv => {
        if (conv.tags) {
          conv.tags.forEach(tag => {
            tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
          });
        }
      });
      
      // 生成建议
      tagCounts.forEach((count, tag) => {
        const confidence = Math.min(0.7, count / similarConversations.length);
        if (confidence > 0.3) {
          suggestions.push({
            tag,
            confidence,
            reason: `基于 ${count} 个相似会话`,
            source: 'user_history'
          });
        }
      });
    } catch (error) {
      this.logger.error('生成历史标签失败:', error);
    }
    
    return suggestions;
  }

  /**
   * 评估标签规则
   */
  async evaluateTagRule(rule: TagRule, conversation: ConversationData): Promise<boolean> {
    try {
      for (const condition of rule.conditions) {
        const result = await this.evaluateCondition(condition, conversation);
        if (!result) {
          return false; // 所有条件都必须满足
        }
      }
      return true;
    } catch (error) {
      this.logger.error('评估标签规则失败:', error);
      return false;
    }
  }

  /**
   * 评估单个条件
   */
  async evaluateCondition(condition: TagCondition, conversation: ConversationData): Promise<boolean> {
    try {
      switch (condition.type) {
        case 'title_contains':
          return this.evaluateTextCondition(conversation.title, condition);
        
        case 'content_contains':
          const content = conversation.messages.map(m => m.content).join(' ');
          return this.evaluateTextCondition(content, condition);
        
        case 'platform_is':
          return conversation.platform === condition.value;
        
        case 'message_count':
          return this.evaluateNumberCondition(conversation.messages.length, condition);
        
        case 'duration':
          const duration = this.calculateConversationDuration(conversation);
          return this.evaluateNumberCondition(duration, condition);
        
        case 'time_range':
          return this.evaluateTimeCondition(conversation.metadata.lastActivity, condition);
        
        default:
          return false;
      }
    } catch (error) {
      this.logger.error('评估条件失败:', error);
      return false;
    }
  }

  /**
   * 评估文本条件
   */
  evaluateTextCondition(text: string, condition: TagCondition): boolean {
    const value = condition.value as string;
    const targetText = condition.caseSensitive ? text : text.toLowerCase();
    const targetValue = condition.caseSensitive ? value : value.toLowerCase();
    
    switch (condition.operator) {
      case 'equals':
        return targetText === targetValue;
      case 'contains':
        return targetText.includes(targetValue);
      case 'starts_with':
        return targetText.startsWith(targetValue);
      case 'ends_with':
        return targetText.endsWith(targetValue);
      case 'regex':
        try {
          const regex = new RegExp(value, condition.caseSensitive ? 'g' : 'gi');
          return regex.test(text);
        } catch {
          return false;
        }
      default:
        return false;
    }
  }

  /**
   * 评估数字条件
   */
  evaluateNumberCondition(value: number, condition: TagCondition): boolean {
    const targetValue = condition.value as number;
    
    switch (condition.operator) {
      case 'equals':
        return value === targetValue;
      case 'greater_than':
        return value > targetValue;
      case 'less_than':
        return value < targetValue;
      case 'between':
        const [min, max] = condition.value as [number, number];
        return value >= min && value <= max;
      default:
        return false;
    }
  }

  /**
   * 评估时间条件
   */
  evaluateTimeCondition(date: Date, condition: TagCondition): boolean {
    const now = new Date();
    const targetDate = new Date(condition.value);
    
    switch (condition.operator) {
      case 'greater_than':
        return date > targetDate;
      case 'less_than':
        return date < targetDate;
      case 'between':
        const [start, end] = condition.value as [string, string];
        return date >= new Date(start) && date <= new Date(end);
      default:
        return false;
    }
  }

  /**
   * 计算会话持续时间（分钟）
   */
  calculateConversationDuration(conversation: ConversationData): number {
    if (conversation.messages.length < 2) return 0;
    
    const timestamps = conversation.messages.map(m => m.timestamp.getTime()).sort();
    const duration = (timestamps[timestamps.length - 1] - timestamps[0]) / (1000 * 60);
    return Math.round(duration);
  }

  /**
   * 计算文本相似度
   */
  calculateTextSimilarity(text1: string, text2: string): number {
    if (!text1 || !text2) return 0;
    
    const words1 = new Set(text1.split(/\s+/));
    const words2 = new Set(text2.split(/\s+/));
    
    const intersection = new Set([...words1].filter(word => words2.has(word)));
    const union = new Set([...words1, ...words2]);
    
    return union.size > 0 ? intersection.size / union.size : 0;
  }

  /**
   * 去重并排序标签建议
   */
  deduplicateAndSort(suggestions: TagSuggestion[]): TagSuggestion[] {
    const tagMap = new Map<string, TagSuggestion>();
    
    suggestions.forEach(suggestion => {
      const existing = tagMap.get(suggestion.tag);
      if (!existing || suggestion.confidence > existing.confidence) {
        tagMap.set(suggestion.tag, suggestion);
      }
    });
    
    return Array.from(tagMap.values()).sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * 标准化标签
   */
  normalizeTag(tag: string): string {
    return tag.trim().replace(/\s+/g, ' ');
  }

  /**
   * 生成规则ID
   */
  generateRuleId(): string {
    return `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
