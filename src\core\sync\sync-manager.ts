/**
 * 数据同步管理器
 * 实现跨设备数据同步，包括云存储集成、冲突解决和增量同步
 */

import { ConversationData } from '@types/conversation';
import { Logger } from '@utils/logger';
import { EventEmitter } from '@utils/event-emitter';

export type SyncProvider = 'chrome-storage' | 'google-drive' | 'dropbox' | 'onedrive' | 'webdav';

export interface SyncConfig {
  provider: SyncProvider;
  enabled: boolean;
  autoSync: boolean;
  syncInterval: number; // 分钟
  credentials?: {
    apiKey?: string;
    accessToken?: string;
    refreshToken?: string;
    clientId?: string;
    clientSecret?: string;
  };
  settings?: {
    conflictResolution: 'local' | 'remote' | 'merge' | 'ask';
    maxRetries: number;
    timeout: number;
  };
}

export interface SyncStatus {
  isEnabled: boolean;
  isConnected: boolean;
  isSyncing: boolean;
  lastSyncTime?: Date;
  lastSyncResult?: 'success' | 'error' | 'conflict';
  pendingChanges: number;
  totalConversations: number;
  error?: string;
}

export interface SyncConflict {
  conversationId: string;
  localData: ConversationData;
  remoteData: ConversationData;
  conflictType: 'content' | 'metadata' | 'both';
  timestamp: Date;
}

export interface SyncResult {
  success: boolean;
  syncedCount: number;
  conflictCount: number;
  errorCount: number;
  conflicts: SyncConflict[];
  duration: number;
  error?: string;
}

export class SyncManager extends EventEmitter {
  private logger: Logger;
  private config: SyncConfig;
  private status: SyncStatus;
  private syncTimer?: NodeJS.Timeout;
  private pendingChanges: Set<string> = new Set();

  constructor(config: SyncConfig) {
    super();
    this.logger = new Logger('SyncManager');
    this.config = config;
    this.status = {
      isEnabled: config.enabled,
      isConnected: false,
      isSyncing: false,
      pendingChanges: 0,
      totalConversations: 0
    };

    if (config.enabled && config.autoSync) {
      this.startAutoSync();
    }
  }

  /**
   * 初始化同步服务
   */
  async initialize(): Promise<void> {
    this.logger.info('初始化同步服务', { provider: this.config.provider });

    try {
      await this.connectToProvider();
      this.status.isConnected = true;
      this.emit('connected');
      
      this.logger.info('同步服务初始化成功');
    } catch (error) {
      this.logger.error('同步服务初始化失败:', error);
      this.status.error = error instanceof Error ? error.message : '初始化失败';
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 连接到同步提供商
   */
  private async connectToProvider(): Promise<void> {
    switch (this.config.provider) {
      case 'chrome-storage':
        await this.connectToChromeStorage();
        break;
      case 'google-drive':
        await this.connectToGoogleDrive();
        break;
      case 'dropbox':
        await this.connectToDropbox();
        break;
      case 'onedrive':
        await this.connectToOneDrive();
        break;
      case 'webdav':
        await this.connectToWebDAV();
        break;
      default:
        throw new Error(`不支持的同步提供商: ${this.config.provider}`);
    }
  }

  /**
   * 连接到Chrome存储
   */
  private async connectToChromeStorage(): Promise<void> {
    if (!chrome?.storage?.sync) {
      throw new Error('Chrome存储API不可用');
    }

    // 测试连接
    try {
      await chrome.storage.sync.get(['test']);
      this.logger.debug('Chrome存储连接成功');
    } catch (error) {
      throw new Error('Chrome存储连接失败');
    }
  }

  /**
   * 连接到Google Drive
   */
  private async connectToGoogleDrive(): Promise<void> {
    if (!this.config.credentials?.accessToken) {
      throw new Error('Google Drive访问令牌未配置');
    }

    // 这里应该实现Google Drive API连接逻辑
    this.logger.debug('Google Drive连接功能待实现');
    throw new Error('Google Drive同步功能正在开发中');
  }

  /**
   * 连接到Dropbox
   */
  private async connectToDropbox(): Promise<void> {
    if (!this.config.credentials?.accessToken) {
      throw new Error('Dropbox访问令牌未配置');
    }

    // 这里应该实现Dropbox API连接逻辑
    this.logger.debug('Dropbox连接功能待实现');
    throw new Error('Dropbox同步功能正在开发中');
  }

  /**
   * 连接到OneDrive
   */
  private async connectToOneDrive(): Promise<void> {
    if (!this.config.credentials?.accessToken) {
      throw new Error('OneDrive访问令牌未配置');
    }

    // 这里应该实现OneDrive API连接逻辑
    this.logger.debug('OneDrive连接功能待实现');
    throw new Error('OneDrive同步功能正在开发中');
  }

  /**
   * 连接到WebDAV
   */
  private async connectToWebDAV(): Promise<void> {
    if (!this.config.credentials?.apiKey) {
      throw new Error('WebDAV服务器地址未配置');
    }

    // 这里应该实现WebDAV连接逻辑
    this.logger.debug('WebDAV连接功能待实现');
    throw new Error('WebDAV同步功能正在开发中');
  }

  /**
   * 执行同步
   */
  async sync(conversations: ConversationData[]): Promise<SyncResult> {
    if (!this.status.isConnected) {
      throw new Error('同步服务未连接');
    }

    if (this.status.isSyncing) {
      throw new Error('同步正在进行中');
    }

    this.logger.info('开始同步', { 
      conversationCount: conversations.length,
      pendingChanges: this.pendingChanges.size
    });

    const startTime = Date.now();
    this.status.isSyncing = true;
    this.emit('syncStart');

    try {
      const result = await this.performSync(conversations);
      
      this.status.lastSyncTime = new Date();
      this.status.lastSyncResult = result.success ? 'success' : 'error';
      this.status.totalConversations = conversations.length;
      this.status.pendingChanges = this.pendingChanges.size;

      if (result.success) {
        this.pendingChanges.clear();
        this.emit('syncSuccess', result);
      } else {
        this.emit('syncError', result);
      }

      this.logger.info('同步完成', {
        success: result.success,
        duration: result.duration,
        syncedCount: result.syncedCount,
        conflictCount: result.conflictCount
      });

      return result;

    } catch (error) {
      this.logger.error('同步失败:', error);
      this.status.lastSyncResult = 'error';
      this.status.error = error instanceof Error ? error.message : '同步失败';
      
      const result: SyncResult = {
        success: false,
        syncedCount: 0,
        conflictCount: 0,
        errorCount: 1,
        conflicts: [],
        duration: Date.now() - startTime,
        error: this.status.error
      };

      this.emit('syncError', result);
      return result;

    } finally {
      this.status.isSyncing = false;
      this.emit('syncEnd');
    }
  }

  /**
   * 执行实际的同步操作
   */
  private async performSync(conversations: ConversationData[]): Promise<SyncResult> {
    const startTime = Date.now();
    let syncedCount = 0;
    let conflictCount = 0;
    let errorCount = 0;
    const conflicts: SyncConflict[] = [];

    try {
      // 获取远程数据
      const remoteData = await this.fetchRemoteData();
      
      // 比较本地和远程数据
      const { toUpload, toDownload, conflicted } = this.compareData(conversations, remoteData);

      // 处理冲突
      for (const conflict of conflicted) {
        const resolution = await this.resolveConflict(conflict);
        if (resolution) {
          if (resolution === 'local') {
            toUpload.push(conflict.localData);
          } else if (resolution === 'remote') {
            toDownload.push(conflict.remoteData);
          }
        } else {
          conflicts.push(conflict);
          conflictCount++;
        }
      }

      // 上传本地更改
      for (const conversation of toUpload) {
        try {
          await this.uploadConversation(conversation);
          syncedCount++;
        } catch (error) {
          this.logger.error('上传对话失败:', error);
          errorCount++;
        }
      }

      // 下载远程更改
      for (const conversation of toDownload) {
        try {
          await this.downloadConversation(conversation);
          syncedCount++;
        } catch (error) {
          this.logger.error('下载对话失败:', error);
          errorCount++;
        }
      }

      return {
        success: errorCount === 0 && conflictCount === 0,
        syncedCount,
        conflictCount,
        errorCount,
        conflicts,
        duration: Date.now() - startTime
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取远程数据
   */
  private async fetchRemoteData(): Promise<ConversationData[]> {
    switch (this.config.provider) {
      case 'chrome-storage':
        return await this.fetchFromChromeStorage();
      default:
        throw new Error(`获取远程数据功能未实现: ${this.config.provider}`);
    }
  }

  /**
   * 从Chrome存储获取数据
   */
  private async fetchFromChromeStorage(): Promise<ConversationData[]> {
    try {
      const result = await chrome.storage.sync.get(['conversations']);
      return result.conversations || [];
    } catch (error) {
      this.logger.error('从Chrome存储获取数据失败:', error);
      return [];
    }
  }

  /**
   * 比较本地和远程数据
   */
  private compareData(local: ConversationData[], remote: ConversationData[]): {
    toUpload: ConversationData[];
    toDownload: ConversationData[];
    conflicted: SyncConflict[];
  } {
    const toUpload: ConversationData[] = [];
    const toDownload: ConversationData[] = [];
    const conflicted: SyncConflict[] = [];

    const remoteMap = new Map(remote.map(conv => [conv.id, conv]));
    const localMap = new Map(local.map(conv => [conv.id, conv]));

    // 检查本地数据
    for (const localConv of local) {
      const remoteConv = remoteMap.get(localConv.id);
      
      if (!remoteConv) {
        // 本地有，远程没有 -> 上传
        toUpload.push(localConv);
      } else {
        // 两边都有 -> 检查冲突
        const conflict = this.detectConflict(localConv, remoteConv);
        if (conflict) {
          conflicted.push(conflict);
        }
      }
    }

    // 检查远程数据
    for (const remoteConv of remote) {
      if (!localMap.has(remoteConv.id)) {
        // 远程有，本地没有 -> 下载
        toDownload.push(remoteConv);
      }
    }

    return { toUpload, toDownload, conflicted };
  }

  /**
   * 检测冲突
   */
  private detectConflict(local: ConversationData, remote: ConversationData): SyncConflict | null {
    const localTime = new Date(local.timestamp).getTime();
    const remoteTime = new Date(remote.timestamp).getTime();

    // 如果时间戳相同，认为没有冲突
    if (localTime === remoteTime) {
      return null;
    }

    // 检查内容差异
    const hasContentDiff = local.messages.length !== remote.messages.length ||
                          local.messages.some((msg, index) => 
                            remote.messages[index]?.content !== msg.content);

    const hasMetadataDiff = JSON.stringify(local.metadata) !== JSON.stringify(remote.metadata);

    if (!hasContentDiff && !hasMetadataDiff) {
      return null;
    }

    let conflictType: 'content' | 'metadata' | 'both';
    if (hasContentDiff && hasMetadataDiff) {
      conflictType = 'both';
    } else if (hasContentDiff) {
      conflictType = 'content';
    } else {
      conflictType = 'metadata';
    }

    return {
      conversationId: local.id,
      localData: local,
      remoteData: remote,
      conflictType,
      timestamp: new Date()
    };
  }

  /**
   * 解决冲突
   */
  private async resolveConflict(conflict: SyncConflict): Promise<'local' | 'remote' | 'merge' | null> {
    const strategy = this.config.settings?.conflictResolution || 'ask';

    switch (strategy) {
      case 'local':
        this.logger.debug('使用本地数据解决冲突', { conversationId: conflict.conversationId });
        return 'local';

      case 'remote':
        this.logger.debug('使用远程数据解决冲突', { conversationId: conflict.conversationId });
        return 'remote';

      case 'merge':
        this.logger.debug('尝试合并数据解决冲突', { conversationId: conflict.conversationId });
        return await this.mergeConflict(conflict);

      case 'ask':
        this.logger.debug('请求用户解决冲突', { conversationId: conflict.conversationId });
        return await this.askUserForResolution(conflict);

      default:
        return null;
    }
  }

  /**
   * 合并冲突数据
   */
  private async mergeConflict(conflict: SyncConflict): Promise<'local' | 'remote' | 'merge'> {
    try {
      // 简单的合并策略：使用较新的时间戳
      const localTime = new Date(conflict.localData.timestamp).getTime();
      const remoteTime = new Date(conflict.remoteData.timestamp).getTime();

      if (localTime > remoteTime) {
        return 'local';
      } else if (remoteTime > localTime) {
        return 'remote';
      } else {
        // 时间戳相同，合并消息
        const mergedMessages = [...conflict.localData.messages];

        // 添加远程独有的消息
        for (const remoteMsg of conflict.remoteData.messages) {
          const exists = mergedMessages.some(localMsg => localMsg.id === remoteMsg.id);
          if (!exists) {
            mergedMessages.push(remoteMsg);
          }
        }

        // 按时间戳排序
        mergedMessages.sort((a, b) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
        );

        // 更新本地数据
        conflict.localData.messages = mergedMessages;
        return 'local';
      }
    } catch (error) {
      this.logger.error('合并冲突失败:', error);
      return 'local'; // 默认使用本地数据
    }
  }

  /**
   * 请求用户解决冲突
   */
  private async askUserForResolution(conflict: SyncConflict): Promise<'local' | 'remote' | null> {
    // 发出冲突事件，让UI层处理
    return new Promise((resolve) => {
      this.emit('conflict', conflict, resolve);

      // 设置超时，避免无限等待
      setTimeout(() => {
        this.logger.warn('冲突解决超时，使用本地数据');
        resolve('local');
      }, 30000); // 30秒超时
    });
  }

  /**
   * 上传对话到远程
   */
  private async uploadConversation(conversation: ConversationData): Promise<void> {
    switch (this.config.provider) {
      case 'chrome-storage':
        await this.uploadToChromeStorage(conversation);
        break;
      default:
        throw new Error(`上传功能未实现: ${this.config.provider}`);
    }
  }

  /**
   * 上传到Chrome存储
   */
  private async uploadToChromeStorage(conversation: ConversationData): Promise<void> {
    try {
      const result = await chrome.storage.sync.get(['conversations']);
      const conversations: ConversationData[] = result.conversations || [];

      // 更新或添加对话
      const index = conversations.findIndex(conv => conv.id === conversation.id);
      if (index >= 0) {
        conversations[index] = conversation;
      } else {
        conversations.push(conversation);
      }

      await chrome.storage.sync.set({ conversations });
      this.logger.debug('对话上传成功', { conversationId: conversation.id });
    } catch (error) {
      this.logger.error('上传到Chrome存储失败:', error);
      throw error;
    }
  }

  /**
   * 从远程下载对话
   */
  private async downloadConversation(conversation: ConversationData): Promise<void> {
    // 这里应该触发本地数据更新事件
    this.emit('conversationDownloaded', conversation);
    this.logger.debug('对话下载完成', { conversationId: conversation.id });
  }

  /**
   * 标记对话为待同步
   */
  markForSync(conversationId: string): void {
    this.pendingChanges.add(conversationId);
    this.status.pendingChanges = this.pendingChanges.size;
    this.emit('pendingChangesUpdated', this.status.pendingChanges);
  }

  /**
   * 开始自动同步
   */
  startAutoSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    const intervalMs = this.config.syncInterval * 60 * 1000; // 转换为毫秒
    this.syncTimer = setInterval(() => {
      if (this.status.isConnected && !this.status.isSyncing && this.pendingChanges.size > 0) {
        this.emit('autoSyncTriggered');
      }
    }, intervalMs);

    this.logger.info('自动同步已启动', { intervalMinutes: this.config.syncInterval });
  }

  /**
   * 停止自动同步
   */
  stopAutoSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = undefined;
    }
    this.logger.info('自动同步已停止');
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<SyncConfig>): void {
    this.config = { ...this.config, ...newConfig };

    if (newConfig.autoSync !== undefined) {
      if (newConfig.autoSync) {
        this.startAutoSync();
      } else {
        this.stopAutoSync();
      }
    }

    if (newConfig.syncInterval !== undefined && this.config.autoSync) {
      this.startAutoSync(); // 重启定时器
    }

    this.emit('configUpdated', this.config);
  }

  /**
   * 获取同步状态
   */
  getStatus(): SyncStatus {
    return { ...this.status };
  }

  /**
   * 获取配置
   */
  getConfig(): SyncConfig {
    return { ...this.config };
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    this.stopAutoSync();
    this.status.isConnected = false;
    this.status.isSyncing = false;
    this.pendingChanges.clear();
    this.status.pendingChanges = 0;

    this.emit('disconnected');
    this.logger.info('同步服务已断开');
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.stopAutoSync();
    this.removeAllListeners();
    this.logger.info('同步管理器已销毁');
  }

  /**
   * 获取支持的同步提供商
   */
  static getSupportedProviders(): { provider: SyncProvider; name: string; description: string }[] {
    return [
      {
        provider: 'chrome-storage',
        name: 'Chrome同步',
        description: '使用Chrome浏览器内置的同步功能，简单可靠'
      },
      {
        provider: 'google-drive',
        name: 'Google Drive',
        description: '使用Google Drive云存储，支持大容量数据'
      },
      {
        provider: 'dropbox',
        name: 'Dropbox',
        description: '使用Dropbox云存储，跨平台支持'
      },
      {
        provider: 'onedrive',
        name: 'OneDrive',
        description: '使用Microsoft OneDrive，与Office集成'
      },
      {
        provider: 'webdav',
        name: 'WebDAV',
        description: '使用WebDAV协议，支持自建服务器'
      }
    ];
  }
}
