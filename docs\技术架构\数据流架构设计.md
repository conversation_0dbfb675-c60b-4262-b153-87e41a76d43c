# AI会话管理插件 - 数据流架构设计

## 🔄 数据流概览

### 整体数据流向

```mermaid
graph TD
    A[用户在AI平台聊天] --> B[Content Script检测]
    B --> C[提取对话数据]
    C --> D[发送到Background]
    D --> E[数据处理与存储]
    E --> F[更新UI状态]
    F --> G[Popup/Options显示]
    
    H[用户操作UI] --> I[发送操作指令]
    I --> D
    D --> J[执行操作]
    J --> E
    
    K[定时任务] --> D
    D --> L[数据清理/同步]
    L --> E
```

## 📡 消息通信架构

### 1. 消息类型定义

```typescript
// src/types/messages.ts
export enum MessageType {
  // 内容检测相关
  CONTENT_DETECTED = 'content_detected',
  CONVERSATION_UPDATED = 'conversation_updated',
  
  // 数据操作相关
  SAVE_CONVERSATION = 'save_conversation',
  DELETE_CONVERSATION = 'delete_conversation',
  UPDATE_TAGS = 'update_tags',
  
  // UI状态相关
  UPDATE_STATUS = 'update_status',
  REFRESH_UI = 'refresh_ui',
  
  // 设置相关
  UPDATE_SETTINGS = 'update_settings',
  EXPORT_DATA = 'export_data',
  
  // 搜索相关
  SEARCH_CONVERSATIONS = 'search_conversations',
  FILTER_CONVERSATIONS = 'filter_conversations'
}

export interface BaseMessage {
  type: MessageType;
  timestamp: number;
  id: string;
}

export interface ContentDetectedMessage extends BaseMessage {
  type: MessageType.CONTENT_DETECTED;
  data: {
    platform: string;
    url: string;
    conversation: ConversationData;
  };
}

export interface SaveConversationMessage extends BaseMessage {
  type: MessageType.SAVE_CONVERSATION;
  data: {
    conversation: ConversationData;
    merge?: boolean;
  };
}
```

### 2. 消息总线实现

```typescript
// src/shared/messaging/message-bus.ts
export class MessageBus {
  private static instance: MessageBus;
  private listeners: Map<MessageType, Set<MessageHandler>> = new Map();
  
  static getInstance(): MessageBus {
    if (!MessageBus.instance) {
      MessageBus.instance = new MessageBus();
    }
    return MessageBus.instance;
  }
  
  // 发送消息到Background
  async sendToBackground<T extends BaseMessage>(message: T): Promise<any> {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  }
  
  // 发送消息到Content Script
  async sendToContent<T extends BaseMessage>(
    tabId: number, 
    message: T
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      chrome.tabs.sendMessage(tabId, message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  }
  
  // 广播消息到所有监听器
  broadcast<T extends BaseMessage>(message: T): void {
    const handlers = this.listeners.get(message.type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('Message handler error:', error);
        }
      });
    }
  }
  
  // 注册消息监听器
  subscribe<T extends BaseMessage>(
    type: MessageType, 
    handler: (message: T) => void
  ): () => void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set());
    }
    
    this.listeners.get(type)!.add(handler);
    
    // 返回取消订阅函数
    return () => {
      this.listeners.get(type)?.delete(handler);
    };
  }
}
```

## 🗄️ 数据存储架构

### 1. 存储层次结构

```typescript
// src/shared/storage/storage-manager.ts
export class StorageManager {
  private indexedDB: IndexedDBManager;
  private chromeStorage: ChromeStorageManager;
  
  constructor() {
    this.indexedDB = new IndexedDBManager();
    this.chromeStorage = new ChromeStorageManager();
  }
  
  // 大数据存储 (IndexedDB)
  async saveLargeData(key: string, data: any): Promise<void> {
    return this.indexedDB.save(key, data);
  }
  
  // 配置数据存储 (Chrome Storage)
  async saveConfig(key: string, data: any): Promise<void> {
    return this.chromeStorage.save(key, data);
  }
  
  // 智能存储选择
  async save(key: string, data: any): Promise<void> {
    const size = JSON.stringify(data).length;
    
    if (size > 1024 * 100) { // 大于100KB使用IndexedDB
      return this.saveLargeData(key, data);
    } else {
      return this.saveConfig(key, data);
    }
  }
}
```

### 2. 数据模型设计

```typescript
// src/types/storage.ts
export interface ConversationData {
  id: string;
  platform: Platform;
  url: string;
  title: string;
  messages: MessageData[];
  tags: string[];
  notes: string;
  createdAt: Date;
  updatedAt: Date;
  metadata: {
    messageCount: number;
    lastActivity: Date;
    isArchived: boolean;
  };
}

export interface MessageData {
  id: string;
  conversationId: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  metadata: {
    platform: Platform;
    originalElement?: string;
  };
}

export interface SearchIndex {
  id: string;
  conversationId: string;
  keywords: string[];
  content: string;
  weight: number;
}
```

## 🔄 状态管理架构

### 1. 全局状态管理

```typescript
// src/shared/state/global-state.ts
export interface GlobalState {
  // 应用状态
  isActive: boolean;
  currentPlatform: Platform | null;
  
  // 数据状态
  conversations: ConversationData[];
  totalCount: number;
  
  // UI状态
  currentView: 'list' | 'grid';
  searchQuery: string;
  selectedFilters: string[];
  
  // 设置状态
  settings: UserSettings;
  
  // 同步状态
  lastSyncTime: Date;
  syncStatus: 'idle' | 'syncing' | 'error';
}

export class StateManager {
  private state: GlobalState;
  private listeners: Map<string, Set<StateListener>> = new Map();
  
  constructor(initialState: GlobalState) {
    this.state = new Proxy(initialState, {
      set: (target, property, value) => {
        const oldValue = target[property as keyof GlobalState];
        target[property as keyof GlobalState] = value;
        this.notifyListeners(property as string, value, oldValue);
        return true;
      }
    });
  }
  
  getState(): Readonly<GlobalState> {
    return this.state;
  }
  
  setState(updates: Partial<GlobalState>): void {
    Object.assign(this.state, updates);
  }
  
  subscribe(key: string, listener: StateListener): () => void {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, new Set());
    }
    
    this.listeners.get(key)!.add(listener);
    
    return () => {
      this.listeners.get(key)?.delete(listener);
    };
  }
  
  private notifyListeners(key: string, newValue: any, oldValue: any): void {
    const keyListeners = this.listeners.get(key);
    if (keyListeners) {
      keyListeners.forEach(listener => {
        listener(newValue, oldValue);
      });
    }
  }
}
```

### 2. 模块状态管理

```typescript
// src/shared/state/module-state.ts
export abstract class ModuleState<T> {
  protected state: T;
  protected listeners: Set<(state: T) => void> = new Set();
  
  constructor(initialState: T) {
    this.state = initialState;
  }
  
  getState(): Readonly<T> {
    return this.state;
  }
  
  protected setState(updates: Partial<T>): void {
    this.state = { ...this.state, ...updates };
    this.notifyListeners();
  }
  
  subscribe(listener: (state: T) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }
  
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.state));
  }
}

// 具体模块状态示例
export class ConversationState extends ModuleState<{
  conversations: ConversationData[];
  loading: boolean;
  error: string | null;
}> {
  constructor() {
    super({
      conversations: [],
      loading: false,
      error: null
    });
  }
  
  setLoading(loading: boolean): void {
    this.setState({ loading });
  }
  
  setConversations(conversations: ConversationData[]): void {
    this.setState({ conversations, loading: false, error: null });
  }
  
  setError(error: string): void {
    this.setState({ error, loading: false });
  }
}
```

## 🎯 事件处理机制

### 1. 事件系统设计

```typescript
// src/shared/events/event-emitter.ts
export class EventEmitter {
  private events: Map<string, Set<EventHandler>> = new Map();
  
  on(event: string, handler: EventHandler): () => void {
    if (!this.events.has(event)) {
      this.events.set(event, new Set());
    }
    
    this.events.get(event)!.add(handler);
    
    return () => this.off(event, handler);
  }
  
  off(event: string, handler: EventHandler): void {
    this.events.get(event)?.delete(handler);
  }
  
  emit(event: string, data?: any): void {
    const handlers = this.events.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Event handler error for ${event}:`, error);
        }
      });
    }
  }
  
  once(event: string, handler: EventHandler): void {
    const onceHandler = (data: any) => {
      handler(data);
      this.off(event, onceHandler);
    };
    this.on(event, onceHandler);
  }
}
```

### 2. 领域事件定义

```typescript
// src/shared/events/domain-events.ts
export enum DomainEvent {
  // 会话事件
  CONVERSATION_CREATED = 'conversation:created',
  CONVERSATION_UPDATED = 'conversation:updated',
  CONVERSATION_DELETED = 'conversation:deleted',
  
  // 消息事件
  MESSAGE_ADDED = 'message:added',
  MESSAGE_UPDATED = 'message:updated',
  
  // 标签事件
  TAG_ADDED = 'tag:added',
  TAG_REMOVED = 'tag:removed',
  TAG_UPDATED = 'tag:updated',
  
  // 搜索事件
  SEARCH_PERFORMED = 'search:performed',
  FILTER_APPLIED = 'filter:applied',
  
  // 导出事件
  EXPORT_STARTED = 'export:started',
  EXPORT_COMPLETED = 'export:completed',
  EXPORT_FAILED = 'export:failed'
}

export interface DomainEventData {
  [DomainEvent.CONVERSATION_CREATED]: { conversation: ConversationData };
  [DomainEvent.CONVERSATION_UPDATED]: { 
    conversation: ConversationData; 
    changes: Partial<ConversationData> 
  };
  [DomainEvent.MESSAGE_ADDED]: { 
    message: MessageData; 
    conversationId: string 
  };
  // ... 其他事件数据类型
}
```

## 🔄 数据同步机制

### 1. 同步策略

```typescript
// src/shared/sync/sync-manager.ts
export class SyncManager {
  private syncQueue: SyncTask[] = [];
  private isProcessing = false;
  
  async queueSync(task: SyncTask): Promise<void> {
    this.syncQueue.push(task);
    
    if (!this.isProcessing) {
      await this.processSyncQueue();
    }
  }
  
  private async processSyncQueue(): Promise<void> {
    this.isProcessing = true;
    
    while (this.syncQueue.length > 0) {
      const task = this.syncQueue.shift()!;
      
      try {
        await this.executeSync(task);
      } catch (error) {
        console.error('Sync task failed:', error);
        // 重试逻辑
        if (task.retryCount < 3) {
          task.retryCount++;
          this.syncQueue.unshift(task);
        }
      }
    }
    
    this.isProcessing = false;
  }
  
  private async executeSync(task: SyncTask): Promise<void> {
    switch (task.type) {
      case 'save_conversation':
        await this.syncConversation(task.data);
        break;
      case 'update_search_index':
        await this.updateSearchIndex(task.data);
        break;
      // ... 其他同步任务
    }
  }
}
```

### 2. 冲突解决策略

```typescript
// src/shared/sync/conflict-resolver.ts
export class ConflictResolver {
  resolveConversationConflict(
    local: ConversationData, 
    remote: ConversationData
  ): ConversationData {
    // 基于时间戳的冲突解决
    if (local.updatedAt > remote.updatedAt) {
      return local;
    } else if (remote.updatedAt > local.updatedAt) {
      return remote;
    }
    
    // 时间戳相同时，合并数据
    return this.mergeConversations(local, remote);
  }
  
  private mergeConversations(
    conv1: ConversationData, 
    conv2: ConversationData
  ): ConversationData {
    return {
      ...conv1,
      messages: this.mergeMessages(conv1.messages, conv2.messages),
      tags: [...new Set([...conv1.tags, ...conv2.tags])],
      notes: conv1.notes || conv2.notes,
      updatedAt: new Date()
    };
  }
}
```

## 📊 性能优化策略

### 1. 数据分页加载

```typescript
// src/shared/data/pagination.ts
export class PaginationManager {
  private pageSize = 20;
  private cache: Map<string, any[]> = new Map();
  
  async loadPage(
    query: string, 
    page: number
  ): Promise<{ data: any[]; hasMore: boolean }> {
    const cacheKey = `${query}_${page}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }
    
    const offset = page * this.pageSize;
    const data = await this.fetchData(query, offset, this.pageSize + 1);
    
    const hasMore = data.length > this.pageSize;
    const pageData = data.slice(0, this.pageSize);
    
    const result = { data: pageData, hasMore };
    this.cache.set(cacheKey, result);
    
    return result;
  }
}
```

### 2. 内存管理

```typescript
// src/shared/memory/memory-manager.ts
export class MemoryManager {
  private static readonly MAX_CACHE_SIZE = 100;
  private cache: LRUCache<string, any>;
  
  constructor() {
    this.cache = new LRUCache(MemoryManager.MAX_CACHE_SIZE);
  }
  
  set(key: string, value: any): void {
    this.cache.set(key, value);
  }
  
  get(key: string): any {
    return this.cache.get(key);
  }
  
  clear(): void {
    this.cache.clear();
  }
  
  // 定期清理
  scheduleCleanup(): void {
    setInterval(() => {
      this.performCleanup();
    }, 5 * 60 * 1000); // 每5分钟清理一次
  }
  
  private performCleanup(): void {
    // 清理过期数据
    // 释放不必要的内存
  }
}
```

这个数据流架构设计确保了插件各个模块之间的高效通信、数据的一致性和系统的可扩展性。
