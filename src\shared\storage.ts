/**
 * 简单的存储管理器
 */

export class StorageManager {
  private prefix: string;

  constructor(prefix: string = 'ai-chat-memo') {
    this.prefix = prefix;
  }

  async get(key: string): Promise<any> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get(`${this.prefix}:${key}`);
        return result[`${this.prefix}:${key}`];
      } else {
        // 开发环境回退到localStorage
        const value = localStorage.getItem(`${this.prefix}:${key}`);
        return value ? JSON.parse(value) : undefined;
      }
    } catch (error) {
      console.error('Storage get error:', error);
      return undefined;
    }
  }

  async set(key: string, value: any): Promise<void> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.set({ [`${this.prefix}:${key}`]: value });
      } else {
        // 开发环境回退到localStorage
        localStorage.setItem(`${this.prefix}:${key}`, JSON.stringify(value));
      }
    } catch (error) {
      console.error('Storage set error:', error);
      throw error;
    }
  }

  async delete(key: string): Promise<void> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.remove(`${this.prefix}:${key}`);
      } else {
        // 开发环境回退到localStorage
        localStorage.removeItem(`${this.prefix}:${key}`);
      }
    } catch (error) {
      console.error('Storage delete error:', error);
      throw error;
    }
  }

  async clear(): Promise<void> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const items = await chrome.storage.local.get(null);
        const keysToRemove = Object.keys(items).filter(key => key.startsWith(`${this.prefix}:`));
        if (keysToRemove.length > 0) {
          await chrome.storage.local.remove(keysToRemove);
        }
      } else {
        // 开发环境回退到localStorage
        const keys = Object.keys(localStorage).filter(key => key.startsWith(`${this.prefix}:`));
        keys.forEach(key => localStorage.removeItem(key));
      }
    } catch (error) {
      console.error('Storage clear error:', error);
      throw error;
    }
  }
}
