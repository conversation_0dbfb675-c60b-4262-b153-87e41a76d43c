# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# TypeScript and JavaScript files
[*.{ts,tsx,js,jsx}]
indent_size = 2
max_line_length = 100

# JSON files
[*.json]
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = 80

# HTML files
[*.html]
indent_size = 2

# CSS and SCSS files
[*.{css,scss,sass}]
indent_size = 2

# Configuration files
[*.{config.js,config.ts}]
indent_size = 2

# Package files
[package.json]
indent_size = 2

# Manifest files
[manifest.json]
indent_size = 2

# Makefile
[Makefile]
indent_style = tab

# Shell scripts
[*.sh]
indent_size = 2

# Python files (for scripts)
[*.py]
indent_size = 4

# Docker files
[Dockerfile*]
indent_size = 2

# GitHub Actions
[.github/workflows/*.{yml,yaml}]
indent_size = 2
