/**
 * 页面结构分析器 - 基于 Playwright MCP
 * 用于分析各个AI网站的页面结构，提取关键选择器和特征
 */

import { chromium, <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

interface PlatformConfig {
  name: string;
  url: string;
  loginRequired: boolean;
  conversationUrl: string;
}

interface ElementInfo {
  selector: string;
  tagName: string;
  className: string;
  id: string;
  dataAttributes: { name: string; value: string }[];
  textContent: string;
  childCount: number;
  index: number;
}

interface PlatformAnalysis {
  platform: string;
  url: string;
  timestamp: string;
  messageContainers: ElementInfo[];
  messageStructure: {
    userElements: ElementInfo[];
    assistantElements: ElementInfo[];
  };
  inputElements: {
    inputs: ElementInfo[];
    buttons: ElementInfo[];
  };
  screenshot: string;
  htmlStructure: string;
  recommendations: AdapterRecommendations;
}

interface AdapterRecommendations {
  conversationContainer: string;
  userMessage: string;
  assistantMessage: string;
  messageInput: string;
  sendButton: string;
  confidence: number;
}

export class PageStructureAnalyzer {
  private browser: Browser | null = null;
  private platforms: PlatformConfig[] = [
    {
      name: 'ChatGPT',
      url: 'https://chatgpt.com',
      loginRequired: true,
      conversationUrl: 'https://chatgpt.com'
    },
    {
      name: 'Claude',
      url: 'https://claude.ai',
      loginRequired: true,
      conversationUrl: 'https://claude.ai/chat'
    },
    {
      name: 'Gemini',
      url: 'https://gemini.google.com',
      loginRequired: false,
      conversationUrl: 'https://gemini.google.com/app'
    }
  ];

  async initialize(): Promise<void> {
    console.log('🚀 初始化浏览器...');
    this.browser = await chromium.launch({
      headless: false, // 开发时可视化
      slowMo: 1000     // 便于观察
    });
    console.log('✅ 浏览器已启动');
  }

  async analyzeAllPlatforms(): Promise<void> {
    if (!this.browser) {
      throw new Error('Browser not initialized');
    }

    for (const platform of this.platforms) {
      console.log(`\n🔍 分析平台: ${platform.name}`);

      try {
        const analysis = await this.analyzePlatform(platform.name);
        if (analysis) {
          console.log(`✅ ${platform.name} 分析完成`);
        } else {
          console.log(`⚠️ ${platform.name} 分析失败`);
        }
      } catch (error) {
        console.error(`❌ ${platform.name} 分析失败:`, error);
      }
    }
  }

  async analyzePlatform(platformName: string): Promise<PlatformAnalysis | null> {
    const platform = this.platforms.find(p => p.name === platformName);
    if (!platform) {
      throw new Error(`未找到平台配置: ${platformName}`);
    }

    if (!this.browser) {
      throw new Error('Browser not initialized');
    }

    console.log(`\n🔍 分析平台: ${platform.name}`);
    
    const page = await this.browser.newPage();
    
    try {
      // 设置用户代理和视口
      await page.setExtraHTTPHeaders({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      });
      await page.setViewportSize({ width: 1920, height: 1080 });

      console.log(`📱 访问 ${platform.url}...`);
      await page.goto(platform.url, { waitUntil: 'networkidle', timeout: 30000 });
      
      // 等待页面加载完成
      await page.waitForTimeout(3000);

      // 检查是否需要登录
      const needsLogin = await this.checkLoginRequired(page);
      
      if (needsLogin && platform.loginRequired) {
        console.log(`⚠️  ${platform.name} 需要登录，请手动登录后按回车继续...`);
        console.log('提示：请在浏览器中完成登录，然后回到终端按回车键');
        await this.waitForUserInput();
        
        // 登录后等待页面加载
        await page.waitForTimeout(2000);
      }

      // 尝试访问会话页面或创建新会话
      if (platform.conversationUrl !== platform.url) {
        console.log(`🔄 导航到会话页面: ${platform.conversationUrl}`);
        await page.goto(platform.conversationUrl, { waitUntil: 'networkidle', timeout: 30000 });
        await page.waitForTimeout(2000);
      }

      // 尝试发送一条测试消息来生成对话结构
      await this.createTestConversation(page, platform);

      // 分析页面结构
      const analysis = await this.performStructureAnalysis(page, platform.name);
      
      // 保存分析结果
      this.saveAnalysis(platform.name, analysis);
      console.log(`✅ ${platform.name} 分析完成`);
      
      return analysis;
    } catch (error) {
      console.error(`❌ ${platform.name} 分析失败:`, error);
      return null;
    } finally {
      await page.close();
    }
  }

  private async createTestConversation(page: Page, platform: PlatformConfig): Promise<void> {
    console.log('💬 尝试创建测试对话...');
    
    try {
      // 查找输入框
      const inputSelectors = [
        'textarea[placeholder*="Message"]',
        'textarea[placeholder*="message"]',
        'textarea[placeholder*="Talk"]',
        'textarea[data-testid*="chat-input"]',
        'textarea',
        'input[type="text"]',
        '[contenteditable="true"]'
      ];

      let inputFound = false;
      for (const selector of inputSelectors) {
        try {
          const input = page.locator(selector).first();
          if (await input.isVisible({ timeout: 2000 })) {
            console.log(`📝 找到输入框: ${selector}`);
            
            // 输入测试消息
            await input.fill('Hello! This is a test message for structure analysis.');
            await page.waitForTimeout(1000);
            
            // 查找发送按钮
            const sendSelectors = [
              'button[data-testid*="send"]',
              'button[aria-label*="Send"]',
              'button[type="submit"]',
              'button:has-text("Send")',
              'button:has-text("发送")',
              '[data-testid="send-button"]'
            ];

            for (const sendSelector of sendSelectors) {
              try {
                const sendButton = page.locator(sendSelector).first();
                if (await sendButton.isVisible({ timeout: 1000 })) {
                  console.log(`🚀 找到发送按钮: ${sendSelector}`);
                  await sendButton.click();
                  
                  // 等待回复
                  console.log('⏳ 等待AI回复...');
                  await page.waitForTimeout(5000);
                  
                  inputFound = true;
                  break;
                }
              } catch (e) {
                // 继续尝试下一个选择器
              }
            }
            
            if (inputFound) break;
          }
        } catch (e) {
          // 继续尝试下一个选择器
        }
      }

      if (!inputFound) {
        console.log('⚠️  未能创建测试对话，将分析现有页面结构');
      } else {
        console.log('✅ 测试对话已创建');
      }
    } catch (error) {
      console.log('⚠️  创建测试对话失败，将分析现有页面结构:', error.message);
    }
  }

  private async performStructureAnalysis(page: Page, platformName: string): Promise<PlatformAnalysis> {
    console.log(`📊 正在分析 ${platformName} 页面结构...`);

    // 确保分析目录存在
    const analysisDir = 'analysis';
    const screenshotsDir = join(analysisDir, 'screenshots');
    if (!existsSync(screenshotsDir)) {
      mkdirSync(screenshotsDir, { recursive: true });
    }

    // 1. 提取所有可能的消息容器
    const messageContainers = await page.evaluate(() => {
      const selectors = [
        '[data-testid*="message"]',
        '[data-testid*="conversation"]',
        '[data-testid*="chat"]',
        '[class*="message"]',
        '[class*="chat"]',
        '[class*="conversation"]',
        '[role="main"]',
        'main',
        '.chat-container',
        '.conversation-container',
        '.messages-container'
      ];

      const found: any[] = [];
      
      selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((el, index) => {
          if (el instanceof HTMLElement) {
            found.push({
              selector,
              tagName: el.tagName.toLowerCase(),
              className: el.className,
              id: el.id,
              dataAttributes: Array.from(el.attributes)
                .filter(attr => attr.name.startsWith('data-'))
                .map(attr => ({ name: attr.name, value: attr.value })),
              textContent: el.textContent?.slice(0, 100) || '',
              childCount: el.children.length,
              index
            });
          }
        });
      });

      return found;
    });

    // 2. 分析消息结构
    const messageStructure = await page.evaluate(() => {
      // 查找可能的用户消息和AI回复
      const userPatterns = [
        '[data-message-author-role="user"]',
        '[data-author="user"]',
        '[class*="user"]',
        '.human-message',
        '.user-message'
      ];

      const assistantPatterns = [
        '[data-message-author-role="assistant"]',
        '[data-author="assistant"]',
        '[class*="assistant"]',
        '[class*="bot"]',
        '.ai-message',
        '.assistant-message',
        '.bot-message'
      ];

      const userElements: any[] = [];
      const assistantElements: any[] = [];

      userPatterns.forEach(pattern => {
        const elements = document.querySelectorAll(pattern);
        elements.forEach((el, index) => {
          if (el instanceof HTMLElement) {
            userElements.push({
              selector: pattern,
              tagName: el.tagName.toLowerCase(),
              className: el.className,
              id: el.id,
              dataAttributes: Array.from(el.attributes)
                .filter(attr => attr.name.startsWith('data-'))
                .map(attr => ({ name: attr.name, value: attr.value })),
              textContent: el.textContent?.slice(0, 200) || '',
              childCount: el.children.length,
              index
            });
          }
        });
      });

      assistantPatterns.forEach(pattern => {
        const elements = document.querySelectorAll(pattern);
        elements.forEach((el, index) => {
          if (el instanceof HTMLElement) {
            assistantElements.push({
              selector: pattern,
              tagName: el.tagName.toLowerCase(),
              className: el.className,
              id: el.id,
              dataAttributes: Array.from(el.attributes)
                .filter(attr => attr.name.startsWith('data-'))
                .map(attr => ({ name: attr.name, value: attr.value })),
              textContent: el.textContent?.slice(0, 200) || '',
              childCount: el.children.length,
              index
            });
          }
        });
      });

      return { userElements, assistantElements };
    });

    // 3. 分析输入框和按钮
    const inputElements = await page.evaluate(() => {
      const inputSelectors = [
        'textarea',
        'input[type="text"]',
        '[contenteditable="true"]',
        '[data-testid*="input"]',
        '[placeholder*="message"]',
        '[placeholder*="chat"]'
      ];

      const buttonSelectors = [
        'button[type="submit"]',
        '[data-testid*="send"]',
        '[aria-label*="send"]',
        '.send-button'
      ];

      const inputs: any[] = [];
      const buttons: any[] = [];

      inputSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((el, index) => {
          if (el instanceof HTMLElement) {
            inputs.push({
              selector,
              tagName: el.tagName.toLowerCase(),
              className: el.className,
              id: el.id,
              dataAttributes: Array.from(el.attributes)
                .filter(attr => attr.name.startsWith('data-'))
                .map(attr => ({ name: attr.name, value: attr.value })),
              textContent: (el as HTMLInputElement).placeholder || '',
              childCount: el.children.length,
              index
            });
          }
        });
      });

      buttonSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((el, index) => {
          if (el instanceof HTMLElement) {
            buttons.push({
              selector,
              tagName: el.tagName.toLowerCase(),
              className: el.className,
              id: el.id,
              dataAttributes: Array.from(el.attributes)
                .filter(attr => attr.name.startsWith('data-'))
                .map(attr => ({ name: attr.name, value: attr.value })),
              textContent: el.textContent?.slice(0, 50) || '',
              childCount: el.children.length,
              index
            });
          }
        });
      });

      return { inputs, buttons };
    });

    // 4. 获取页面截图
    const screenshotPath = join(screenshotsDir, `${platformName.toLowerCase()}.png`);
    await page.screenshot({ 
      path: screenshotPath,
      fullPage: true 
    });

    // 5. 获取页面HTML结构
    const htmlStructure = await page.content();

    return {
      platform: platformName,
      url: page.url(),
      timestamp: new Date().toISOString(),
      messageContainers,
      messageStructure,
      inputElements,
      screenshot: `screenshots/${platformName.toLowerCase()}.png`,
      htmlStructure: htmlStructure.slice(0, 10000), // 限制大小
      recommendations: this.generateRecommendations(messageContainers, messageStructure, inputElements)
    };
  }

  private generateRecommendations(
    containers: ElementInfo[],
    structure: { userElements: ElementInfo[]; assistantElements: ElementInfo[] },
    inputs: { inputs: ElementInfo[]; buttons: ElementInfo[] }
  ): AdapterRecommendations {
    // 分析并生成适配器建议
    const bestContainer = containers.find(c => 
      c.dataAttributes.some(attr => attr.name.includes('conversation') || attr.name.includes('message'))
    ) || containers[0];

    const bestUserSelector = structure.userElements.find(e =>
      e.dataAttributes.some(attr => attr.value === 'user')
    )?.selector || structure.userElements[0]?.selector;

    const bestAssistantSelector = structure.assistantElements.find(e =>
      e.dataAttributes.some(attr => attr.value === 'assistant')
    )?.selector || structure.assistantElements[0]?.selector;

    const bestInput = inputs.inputs.find(i => 
      i.tagName === 'textarea' || i.textContent.toLowerCase().includes('message')
    ) || inputs.inputs[0];

    return {
      conversationContainer: bestContainer?.selector || '[role="main"]',
      userMessage: bestUserSelector || '.user-message',
      assistantMessage: bestAssistantSelector || '.assistant-message',
      messageInput: bestInput?.selector || 'textarea',
      sendButton: inputs.buttons[0]?.selector || 'button[type="submit"]',
      confidence: this.calculateConfidence(containers, structure, inputs)
    };
  }

  private calculateConfidence(
    containers: ElementInfo[],
    structure: { userElements: ElementInfo[]; assistantElements: ElementInfo[] },
    inputs: { inputs: ElementInfo[]; buttons: ElementInfo[] }
  ): number {
    let score = 0;
    
    // 有明确的data属性 +30分
    if (containers.some(c => c.dataAttributes.length > 0)) score += 30;
    if (structure.userElements.some(e => e.dataAttributes.length > 0)) score += 25;
    if (structure.assistantElements.some(e => e.dataAttributes.length > 0)) score += 25;
    
    // 有语义化的类名 +20分
    if (containers.some(c => c.className.includes('message') || c.className.includes('chat'))) score += 10;
    if (inputs.inputs.some(i => i.tagName === 'textarea')) score += 10;
    
    return Math.min(score, 100);
  }

  private async checkLoginRequired(page: Page): Promise<boolean> {
    // 检查常见的登录指示器
    const loginIndicators = [
      'button:has-text("Sign in")',
      'button:has-text("Log in")',
      'button:has-text("Login")',
      'a:has-text("Sign in")',
      'a:has-text("Log in")',
      '.login-button',
      '.signin-button'
    ];

    for (const indicator of loginIndicators) {
      try {
        const element = await page.locator(indicator).first();
        if (await element.isVisible({ timeout: 1000 })) {
          return true;
        }
      } catch {
        // 忽略错误，继续检查下一个
      }
    }

    return false;
  }

  private async waitForUserInput(): Promise<void> {
    return new Promise((resolve) => {
      process.stdin.setRawMode(true);
      process.stdin.resume();
      process.stdin.once('data', () => {
        process.stdin.setRawMode(false);
        process.stdin.pause();
        resolve();
      });
    });
  }

  private saveAnalysis(platformName: string, analysis: PlatformAnalysis): void {
    const outputDir = 'analysis/results';
    
    if (!existsSync(outputDir)) {
      mkdirSync(outputDir, { recursive: true });
    }

    const filename = join(outputDir, `${platformName.toLowerCase()}.json`);
    writeFileSync(filename, JSON.stringify(analysis, null, 2), 'utf-8');
    
    console.log(`📁 分析结果已保存到: ${filename}`);
  }

  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      console.log('🔒 浏览器已关闭');
    }
  }
}
