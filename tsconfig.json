{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmitOnError": true,
    
    /* Linting */
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    
    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@types/*": ["src/types/*"],
      "@utils/*": ["src/utils/*"],
      "@content/*": ["src/content/*"],
      "@background/*": ["src/background/*"],
      "@popup/*": ["src/popup/*"],
      "@options/*": ["src/options/*"],
      "@shared/*": ["src/shared/*"],
      "@storage/*": ["src/storage/*"],
      "@conversation/*": ["src/conversation/*"],
      "@tags/*": ["src/tags/*"]
    },
    
    /* Type definitions */
    "types": ["chrome", "jest", "node"],
    
    /* Additional options */
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": false,
    "declarationMap": false,
    "sourceMap": true
  },
  
  "include": [
    "src/**/*",
    "scripts/**/*",
    "tests/**/*",
    "vite.config.ts"
  ],
  
  "exclude": [
    "node_modules",
    "dist",
    "analysis"
  ]
}
