# 页面结构分析工具 - 基于 Playwright MCP

## 🎯 工具目标

使用 Playwright MCP 在开发阶段自动分析各个AI网站的页面结构，提取关键选择器和特征，为适配器开发提供准确的数据基础。

## 🔧 工具架构

### 1. 页面分析器

```typescript
// scripts/page-analyzer/analyzer.ts
import { chromium, Browser, Page } from 'playwright';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

export class PageStructureAnalyzer {
  private browser: Browser | null = null;
  private platforms: PlatformConfig[] = [
    {
      name: 'ChatGPT',
      url: 'https://chat.openai.com',
      loginRequired: true,
      conversationUrl: 'https://chat.openai.com/c/example'
    },
    {
      name: '<PERSON>',
      url: 'https://claude.ai',
      loginRequired: true,
      conversationUrl: 'https://claude.ai/chat/example'
    },
    {
      name: '<PERSON>',
      url: 'https://gemini.google.com',
      loginRequired: false,
      conversationUrl: 'https://gemini.google.com/app'
    },
    {
      name: 'Ai<PERSON><PERSON><PERSON>',
      url: 'https://aistudio.google.com',
      loginRequired: false,
      conversationUrl: 'https://aistudio.google.com/app'
    },
    {
      name: 'Monica',
      url: 'https://monica.im',
      loginRequired: true,
      conversationUrl: 'https://monica.im/chat'
    },
    {
      name: 'Poe',
      url: 'https://poe.com',
      loginRequired: true,
      conversationUrl: 'https://poe.com/chat'
    }
  ];

  async initialize(): Promise<void> {
    this.browser = await chromium.launch({
      headless: false, // 开发时可视化
      slowMo: 1000     // 便于观察
    });
  }

  async analyzeAllPlatforms(): Promise<void> {
    if (!this.browser) {
      throw new Error('Browser not initialized');
    }

    for (const platform of this.platforms) {
      console.log(`\n🔍 分析平台: ${platform.name}`);
      
      try {
        const analysis = await this.analyzePlatform(platform);
        this.saveAnalysis(platform.name, analysis);
        console.log(`✅ ${platform.name} 分析完成`);
      } catch (error) {
        console.error(`❌ ${platform.name} 分析失败:`, error);
      }
    }
  }

  private async analyzePlatform(platform: PlatformConfig): Promise<PlatformAnalysis> {
    const page = await this.browser!.newPage();
    
    try {
      // 设置用户代理和视口
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      await page.setViewportSize({ width: 1920, height: 1080 });

      // 访问平台首页
      await page.goto(platform.url, { waitUntil: 'networkidle' });
      
      // 等待页面加载完成
      await page.waitForTimeout(3000);

      // 检查是否需要登录
      const needsLogin = await this.checkLoginRequired(page);
      
      if (needsLogin && platform.loginRequired) {
        console.log(`⚠️  ${platform.name} 需要登录，请手动登录后按回车继续...`);
        await this.waitForUserInput();
      }

      // 尝试访问会话页面
      if (platform.conversationUrl !== platform.url) {
        await page.goto(platform.conversationUrl, { waitUntil: 'networkidle' });
        await page.waitForTimeout(2000);
      }

      // 分析页面结构
      const analysis = await this.performStructureAnalysis(page, platform.name);
      
      return analysis;
    } finally {
      await page.close();
    }
  }

  private async performStructureAnalysis(page: Page, platformName: string): Promise<PlatformAnalysis> {
    console.log(`📊 正在分析 ${platformName} 页面结构...`);

    // 1. 提取所有可能的消息容器
    const messageContainers = await page.evaluate(() => {
      const selectors = [
        '[data-testid*="message"]',
        '[data-testid*="conversation"]',
        '[data-testid*="chat"]',
        '[class*="message"]',
        '[class*="chat"]',
        '[class*="conversation"]',
        '[role="main"]',
        'main',
        '.chat-container',
        '.conversation-container',
        '.messages-container'
      ];

      const found: ElementInfo[] = [];
      
      selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((el, index) => {
          if (el instanceof HTMLElement) {
            found.push({
              selector,
              tagName: el.tagName.toLowerCase(),
              className: el.className,
              id: el.id,
              dataAttributes: Array.from(el.attributes)
                .filter(attr => attr.name.startsWith('data-'))
                .map(attr => ({ name: attr.name, value: attr.value })),
              textContent: el.textContent?.slice(0, 100) || '',
              childCount: el.children.length,
              index
            });
          }
        });
      });

      return found;
    });

    // 2. 分析消息结构
    const messageStructure = await page.evaluate(() => {
      // 查找可能的用户消息和AI回复
      const userPatterns = [
        '[data-message-author-role="user"]',
        '[data-author="user"]',
        '[class*="user"]',
        '.human-message',
        '.user-message'
      ];

      const assistantPatterns = [
        '[data-message-author-role="assistant"]',
        '[data-author="assistant"]',
        '[class*="assistant"]',
        '[class*="bot"]',
        '.ai-message',
        '.assistant-message',
        '.bot-message'
      ];

      const userElements: ElementInfo[] = [];
      const assistantElements: ElementInfo[] = [];

      userPatterns.forEach(pattern => {
        const elements = document.querySelectorAll(pattern);
        elements.forEach((el, index) => {
          if (el instanceof HTMLElement) {
            userElements.push({
              selector: pattern,
              tagName: el.tagName.toLowerCase(),
              className: el.className,
              id: el.id,
              dataAttributes: Array.from(el.attributes)
                .filter(attr => attr.name.startsWith('data-'))
                .map(attr => ({ name: attr.name, value: attr.value })),
              textContent: el.textContent?.slice(0, 200) || '',
              childCount: el.children.length,
              index
            });
          }
        });
      });

      assistantPatterns.forEach(pattern => {
        const elements = document.querySelectorAll(pattern);
        elements.forEach((el, index) => {
          if (el instanceof HTMLElement) {
            assistantElements.push({
              selector: pattern,
              tagName: el.tagName.toLowerCase(),
              className: el.className,
              id: el.id,
              dataAttributes: Array.from(el.attributes)
                .filter(attr => attr.name.startsWith('data-'))
                .map(attr => ({ name: attr.name, value: attr.value })),
              textContent: el.textContent?.slice(0, 200) || '',
              childCount: el.children.length,
              index
            });
          }
        });
      });

      return { userElements, assistantElements };
    });

    // 3. 分析输入框和按钮
    const inputElements = await page.evaluate(() => {
      const inputSelectors = [
        'textarea',
        'input[type="text"]',
        '[contenteditable="true"]',
        '[data-testid*="input"]',
        '[placeholder*="message"]',
        '[placeholder*="chat"]'
      ];

      const buttonSelectors = [
        'button[type="submit"]',
        '[data-testid*="send"]',
        '[aria-label*="send"]',
        '.send-button'
      ];

      const inputs: ElementInfo[] = [];
      const buttons: ElementInfo[] = [];

      inputSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((el, index) => {
          if (el instanceof HTMLElement) {
            inputs.push({
              selector,
              tagName: el.tagName.toLowerCase(),
              className: el.className,
              id: el.id,
              dataAttributes: Array.from(el.attributes)
                .filter(attr => attr.name.startsWith('data-'))
                .map(attr => ({ name: attr.name, value: attr.value })),
              textContent: (el as HTMLInputElement).placeholder || '',
              childCount: el.children.length,
              index
            });
          }
        });
      });

      buttonSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((el, index) => {
          if (el instanceof HTMLElement) {
            buttons.push({
              selector,
              tagName: el.tagName.toLowerCase(),
              className: el.className,
              id: el.id,
              dataAttributes: Array.from(el.attributes)
                .filter(attr => attr.name.startsWith('data-'))
                .map(attr => ({ name: attr.name, value: attr.value })),
              textContent: el.textContent?.slice(0, 50) || '',
              childCount: el.children.length,
              index
            });
          }
        });
      });

      return { inputs, buttons };
    });

    // 4. 获取页面截图
    const screenshot = await page.screenshot({ 
      path: `analysis/screenshots/${platformName.toLowerCase()}.png`,
      fullPage: true 
    });

    // 5. 获取页面HTML结构
    const htmlStructure = await page.content();

    return {
      platform: platformName,
      url: page.url(),
      timestamp: new Date().toISOString(),
      messageContainers,
      messageStructure,
      inputElements,
      screenshot: `screenshots/${platformName.toLowerCase()}.png`,
      htmlStructure: htmlStructure.slice(0, 10000), // 限制大小
      recommendations: this.generateRecommendations(messageContainers, messageStructure, inputElements)
    };
  }

  private generateRecommendations(
    containers: ElementInfo[],
    structure: { userElements: ElementInfo[]; assistantElements: ElementInfo[] },
    inputs: { inputs: ElementInfo[]; buttons: ElementInfo[] }
  ): AdapterRecommendations {
    // 分析并生成适配器建议
    const bestContainer = containers.find(c => 
      c.dataAttributes.some(attr => attr.name.includes('conversation') || attr.name.includes('message'))
    ) || containers[0];

    const bestUserSelector = structure.userElements.find(e =>
      e.dataAttributes.some(attr => attr.value === 'user')
    )?.selector || structure.userElements[0]?.selector;

    const bestAssistantSelector = structure.assistantElements.find(e =>
      e.dataAttributes.some(attr => attr.value === 'assistant')
    )?.selector || structure.assistantElements[0]?.selector;

    const bestInput = inputs.inputs.find(i => 
      i.tagName === 'textarea' || i.textContent.toLowerCase().includes('message')
    ) || inputs.inputs[0];

    return {
      conversationContainer: bestContainer?.selector || '[role="main"]',
      userMessage: bestUserSelector || '.user-message',
      assistantMessage: bestAssistantSelector || '.assistant-message',
      messageInput: bestInput?.selector || 'textarea',
      sendButton: inputs.buttons[0]?.selector || 'button[type="submit"]',
      confidence: this.calculateConfidence(containers, structure, inputs)
    };
  }

  private calculateConfidence(
    containers: ElementInfo[],
    structure: { userElements: ElementInfo[]; assistantElements: ElementInfo[] },
    inputs: { inputs: ElementInfo[]; buttons: ElementInfo[] }
  ): number {
    let score = 0;
    
    // 有明确的data属性 +30分
    if (containers.some(c => c.dataAttributes.length > 0)) score += 30;
    if (structure.userElements.some(e => e.dataAttributes.length > 0)) score += 25;
    if (structure.assistantElements.some(e => e.dataAttributes.length > 0)) score += 25;
    
    // 有语义化的类名 +20分
    if (containers.some(c => c.className.includes('message') || c.className.includes('chat'))) score += 10;
    if (inputs.inputs.some(i => i.tagName === 'textarea')) score += 10;
    
    return Math.min(score, 100);
  }

  private async checkLoginRequired(page: Page): boolean {
    // 检查常见的登录指示器
    const loginIndicators = [
      'button:has-text("Sign in")',
      'button:has-text("Log in")',
      'button:has-text("Login")',
      'a:has-text("Sign in")',
      'a:has-text("Log in")',
      '.login-button',
      '.signin-button'
    ];

    for (const indicator of loginIndicators) {
      try {
        const element = await page.locator(indicator).first();
        if (await element.isVisible()) {
          return true;
        }
      } catch {
        // 忽略错误，继续检查下一个
      }
    }

    return false;
  }

  private async waitForUserInput(): Promise<void> {
    return new Promise((resolve) => {
      process.stdin.once('data', () => {
        resolve();
      });
    });
  }

  private saveAnalysis(platformName: string, analysis: PlatformAnalysis): void {
    const outputDir = 'analysis/results';
    
    if (!existsSync(outputDir)) {
      mkdirSync(outputDir, { recursive: true });
    }

    const filename = join(outputDir, `${platformName.toLowerCase()}.json`);
    writeFileSync(filename, JSON.stringify(analysis, null, 2), 'utf-8');
    
    console.log(`📁 分析结果已保存到: ${filename}`);
  }

  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// 类型定义
interface PlatformConfig {
  name: string;
  url: string;
  loginRequired: boolean;
  conversationUrl: string;
}

interface ElementInfo {
  selector: string;
  tagName: string;
  className: string;
  id: string;
  dataAttributes: { name: string; value: string }[];
  textContent: string;
  childCount: number;
  index: number;
}

interface PlatformAnalysis {
  platform: string;
  url: string;
  timestamp: string;
  messageContainers: ElementInfo[];
  messageStructure: {
    userElements: ElementInfo[];
    assistantElements: ElementInfo[];
  };
  inputElements: {
    inputs: ElementInfo[];
    buttons: ElementInfo[];
  };
  screenshot: string;
  htmlStructure: string;
  recommendations: AdapterRecommendations;
}

interface AdapterRecommendations {
  conversationContainer: string;
  userMessage: string;
  assistantMessage: string;
  messageInput: string;
  sendButton: string;
  confidence: number;
}

### 2. 适配器代码生成器

```typescript
// scripts/page-analyzer/adapter-generator.ts
export class AdapterGenerator {
  static generateAdapterCode(analysis: PlatformAnalysis): string {
    const className = `${analysis.platform}Adapter`;
    const recommendations = analysis.recommendations;

    return `// Auto-generated adapter for ${analysis.platform}
// Generated on: ${analysis.timestamp}
// Confidence: ${recommendations.confidence}%

import { BasePlatformAdapter, PlatformSelectors } from './base';
import { ConversationData, MessageData } from '@types/conversation';

export class ${className} extends BasePlatformAdapter {
  readonly platform = '${analysis.platform}';
  readonly selectors: PlatformSelectors = {
    conversationContainer: '${recommendations.conversationContainer}',
    messageElements: '${recommendations.conversationContainer} > *',
    userMessage: '${recommendations.userMessage}',
    assistantMessage: '${recommendations.assistantMessage}',
    messageContent: '.message-content, .text-content, p',
    messageInput: '${recommendations.messageInput}',
    sendButton: '${recommendations.sendButton}'
  };

  protected isPageReady(): boolean {
    return document.querySelector(this.selectors.conversationContainer) !== null;
  }

  protected extractConversation(): ConversationData | null {
    const messages = this.extractMessages();

    if (messages.length === 0) {
      return null;
    }

    const title = this.extractTitle();
    const url = window.location.href;

    return {
      id: this.generateConversationId(url),
      platform: this.platform,
      url,
      title,
      messages,
      tags: [this.platform],
      notes: '',
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        messageCount: messages.length,
        lastActivity: new Date(),
        isArchived: false
      }
    };
  }

  private extractMessages(): MessageData[] {
    const messages: MessageData[] = [];

    // 提取用户消息
    const userElements = document.querySelectorAll(this.selectors.userMessage);
    userElements.forEach((element, index) => {
      const content = this.extractMessageContent(element);
      if (content) {
        messages.push({
          id: \`user_\${index}\`,
          conversationId: '',
          type: 'user',
          content,
          timestamp: new Date(),
          metadata: {
            platform: this.platform,
            originalElement: element.outerHTML
          }
        });
      }
    });

    // 提取AI回复
    const assistantElements = document.querySelectorAll(this.selectors.assistantMessage);
    assistantElements.forEach((element, index) => {
      const content = this.extractMessageContent(element);
      if (content) {
        messages.push({
          id: \`assistant_\${index}\`,
          conversationId: '',
          type: 'assistant',
          content,
          timestamp: new Date(),
          metadata: {
            platform: this.platform,
            originalElement: element.outerHTML
          }
        });
      }
    });

    return this.sortMessagesByDOMOrder(messages);
  }

  private extractMessageContent(element: Element): string {
    const contentElement = element.querySelector(this.selectors.messageContent) || element;

    if (!contentElement) return '';

    const cloned = contentElement.cloneNode(true) as Element;

    // 处理代码块、图片、链接等
    const codeBlocks = cloned.querySelectorAll('pre, code');
    codeBlocks.forEach((block) => {
      const language = this.detectCodeLanguage(block);
      block.textContent = \`[代码块:\${language}]\\n\${block.textContent}\`;
    });

    const images = cloned.querySelectorAll('img');
    images.forEach(img => {
      img.replaceWith(document.createTextNode('[图片]'));
    });

    return cloned.textContent?.trim() || '';
  }

  private generateConversationId(url: string): string {
    const patterns = [
      /\\/c\\/([a-zA-Z0-9-]+)/,
      /\\/chat\\/([a-zA-Z0-9-]+)/,
      /\\/conversation\\/([a-zA-Z0-9-]+)/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }

    return btoa(url).slice(0, 16);
  }
}`;
  }
}

### 3. 批量分析脚本

```typescript
// scripts/analyze-platforms.ts
import { PageStructureAnalyzer } from './page-analyzer/analyzer';
import { AdapterGenerator } from './page-analyzer/adapter-generator';

async function main() {
  console.log('🚀 开始分析AI平台页面结构...\n');

  const analyzer = new PageStructureAnalyzer();

  try {
    await analyzer.initialize();
    await analyzer.analyzeAllPlatforms();

    console.log('\n📊 生成适配器代码...');
    await generateAdapters();

    console.log('\n✅ 所有分析完成！');

  } catch (error) {
    console.error('❌ 分析过程中出现错误:', error);
  } finally {
    await analyzer.cleanup();
  }
}

async function generateAdapters() {
  // 读取分析结果并生成适配器代码
  // 实现细节...
}

if (require.main === module) {
  main().catch(console.error);
}
```
```
