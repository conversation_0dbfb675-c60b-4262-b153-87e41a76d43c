#!/usr/bin/env ts-node

/**
 * 单个平台分析脚本
 * 用法: npm run analyze:single -- --platform=ChatGPT --url=https://chat.openai.com
 */

import { chromium, <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { PageStructureAnalyzer } from './page-analyzer/analyzer';

interface AnalysisOptions {
  platform: string;
  url: string;
  loginRequired?: boolean;
  conversationUrl?: string;
  headless?: boolean;
  timeout?: number;
}

class SinglePlatformAnalyzer {
  private browser: Browser | null = null;
  private options: AnalysisOptions;

  constructor(options: AnalysisOptions) {
    this.options = {
      loginRequired: false,
      headless: false,
      timeout: 30000,
      ...options
    };
  }

  async analyze(): Promise<void> {
    console.log(`🔍 开始分析平台: ${this.options.platform}`);
    console.log(`🌐 URL: ${this.options.url}`);

    try {
      await this.initialize();
      const analysis = await this.performAnalysis();
      this.saveResults(analysis);
      
      console.log(`✅ ${this.options.platform} 分析完成`);
      console.log(`📁 结果保存在: analysis/results/${this.options.platform.toLowerCase()}.json`);
      
    } catch (error) {
      console.error(`❌ 分析失败:`, error);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  private async initialize(): Promise<void> {
    this.browser = await chromium.launch({
      headless: this.options.headless,
      slowMo: 1000,
      timeout: this.options.timeout
    });
  }

  private async performAnalysis(): Promise<any> {
    if (!this.browser) {
      throw new Error('Browser not initialized');
    }

    const page = await this.browser.newPage();
    
    try {
      // 设置用户代理和视口
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      await page.setViewportSize({ width: 1920, height: 1080 });

      console.log(`📡 正在访问: ${this.options.url}`);
      await page.goto(this.options.url, { 
        waitUntil: 'networkidle',
        timeout: this.options.timeout 
      });
      
      // 等待页面加载
      await page.waitForTimeout(3000);

      // 检查登录状态
      if (this.options.loginRequired) {
        const needsLogin = await this.checkLoginRequired(page);
        if (needsLogin) {
          console.log(`⚠️  需要登录，请在浏览器中完成登录后按回车继续...`);
          await this.waitForUserInput();
        }
      }

      // 如果有会话URL，尝试访问
      if (this.options.conversationUrl && this.options.conversationUrl !== this.options.url) {
        console.log(`📡 正在访问会话页面: ${this.options.conversationUrl}`);
        await page.goto(this.options.conversationUrl, { 
          waitUntil: 'networkidle',
          timeout: this.options.timeout 
        });
        await page.waitForTimeout(2000);
      }

      // 执行详细分析
      console.log(`📊 正在分析页面结构...`);
      const analysis = await this.performDetailedAnalysis(page);
      
      return analysis;
      
    } finally {
      await page.close();
    }
  }

  private async performDetailedAnalysis(page: Page): Promise<any> {
    // 1. 基础页面信息
    const pageInfo = await page.evaluate(() => ({
      title: document.title,
      url: window.location.href,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    }));

    // 2. 查找所有可能的消息容器
    const messageContainers = await page.evaluate(() => {
      const selectors = [
        // 通用选择器
        '[data-testid*="message"]',
        '[data-testid*="conversation"]',
        '[data-testid*="chat"]',
        '[class*="message"]',
        '[class*="chat"]',
        '[class*="conversation"]',
        '[role="main"]',
        'main',
        
        // 特定平台选择器
        '.chat-container',
        '.conversation-container',
        '.messages-container',
        '.dialogue-container',
        '.thread-container'
      ];

      const found: any[] = [];
      
      selectors.forEach(selector => {
        try {
          const elements = document.querySelectorAll(selector);
          elements.forEach((el, index) => {
            if (el instanceof HTMLElement && el.children.length > 0) {
              found.push({
                selector,
                tagName: el.tagName.toLowerCase(),
                className: el.className,
                id: el.id,
                dataAttributes: Array.from(el.attributes)
                  .filter(attr => attr.name.startsWith('data-'))
                  .map(attr => ({ name: attr.name, value: attr.value })),
                textContent: el.textContent?.slice(0, 200) || '',
                childCount: el.children.length,
                index,
                rect: el.getBoundingClientRect(),
                isVisible: el.offsetParent !== null
              });
            }
          });
        } catch (e) {
          console.warn(`Selector failed: ${selector}`, e);
        }
      });

      return found.filter(item => item.isVisible && item.childCount > 0);
    });

    // 3. 分析消息结构
    const messageAnalysis = await page.evaluate(() => {
      const userPatterns = [
        '[data-message-author-role="user"]',
        '[data-author="user"]',
        '[data-role="user"]',
        '[class*="user"]',
        '.human-message',
        '.user-message',
        '.user-bubble'
      ];

      const assistantPatterns = [
        '[data-message-author-role="assistant"]',
        '[data-author="assistant"]',
        '[data-role="assistant"]',
        '[class*="assistant"]',
        '[class*="bot"]',
        '[class*="ai"]',
        '.ai-message',
        '.assistant-message',
        '.bot-message',
        '.bot-bubble'
      ];

      const analyzeElements = (patterns: string[], type: string) => {
        const results: any[] = [];
        
        patterns.forEach(pattern => {
          try {
            const elements = document.querySelectorAll(pattern);
            elements.forEach((el, index) => {
              if (el instanceof HTMLElement && el.textContent?.trim()) {
                results.push({
                  selector: pattern,
                  type,
                  tagName: el.tagName.toLowerCase(),
                  className: el.className,
                  id: el.id,
                  dataAttributes: Array.from(el.attributes)
                    .filter(attr => attr.name.startsWith('data-'))
                    .map(attr => ({ name: attr.name, value: attr.value })),
                  textContent: el.textContent.slice(0, 300),
                  childCount: el.children.length,
                  index,
                  rect: el.getBoundingClientRect(),
                  isVisible: el.offsetParent !== null
                });
              }
            });
          } catch (e) {
            console.warn(`Pattern failed: ${pattern}`, e);
          }
        });
        
        return results.filter(item => item.isVisible);
      };

      return {
        userElements: analyzeElements(userPatterns, 'user'),
        assistantElements: analyzeElements(assistantPatterns, 'assistant')
      };
    });

    // 4. 分析输入控件
    const inputAnalysis = await page.evaluate(() => {
      const inputSelectors = [
        'textarea',
        'input[type="text"]',
        '[contenteditable="true"]',
        '[data-testid*="input"]',
        '[data-testid*="textarea"]',
        '[placeholder*="message"]',
        '[placeholder*="chat"]',
        '[placeholder*="type"]'
      ];

      const buttonSelectors = [
        'button[type="submit"]',
        '[data-testid*="send"]',
        '[aria-label*="send"]',
        '[aria-label*="submit"]',
        '.send-button',
        '.submit-button'
      ];

      const analyzeInputs = (selectors: string[], type: string) => {
        const results: any[] = [];
        
        selectors.forEach(selector => {
          try {
            const elements = document.querySelectorAll(selector);
            elements.forEach((el, index) => {
              if (el instanceof HTMLElement) {
                results.push({
                  selector,
                  type,
                  tagName: el.tagName.toLowerCase(),
                  className: el.className,
                  id: el.id,
                  placeholder: (el as HTMLInputElement).placeholder || '',
                  ariaLabel: el.getAttribute('aria-label') || '',
                  dataAttributes: Array.from(el.attributes)
                    .filter(attr => attr.name.startsWith('data-'))
                    .map(attr => ({ name: attr.name, value: attr.value })),
                  rect: el.getBoundingClientRect(),
                  isVisible: el.offsetParent !== null,
                  index
                });
              }
            });
          } catch (e) {
            console.warn(`Input selector failed: ${selector}`, e);
          }
        });
        
        return results.filter(item => item.isVisible);
      };

      return {
        inputs: analyzeInputs(inputSelectors, 'input'),
        buttons: analyzeInputs(buttonSelectors, 'button')
      };
    });

    // 5. 生成推荐配置
    const recommendations = this.generateRecommendations(
      messageContainers,
      messageAnalysis,
      inputAnalysis
    );

    // 6. 截图
    const screenshotDir = 'analysis/screenshots';
    if (!existsSync(screenshotDir)) {
      mkdirSync(screenshotDir, { recursive: true });
    }
    
    const screenshotPath = join(screenshotDir, `${this.options.platform.toLowerCase()}.png`);
    await page.screenshot({ 
      path: screenshotPath,
      fullPage: true 
    });

    return {
      platform: this.options.platform,
      pageInfo,
      messageContainers,
      messageAnalysis,
      inputAnalysis,
      recommendations,
      screenshot: screenshotPath,
      timestamp: new Date().toISOString()
    };
  }

  private generateRecommendations(containers: any[], messages: any, inputs: any): any {
    // 选择最佳的容器
    const bestContainer = containers
      .filter(c => c.childCount > 1)
      .sort((a, b) => {
        // 优先选择有data属性的
        const aScore = a.dataAttributes.length * 10 + a.childCount;
        const bScore = b.dataAttributes.length * 10 + b.childCount;
        return bScore - aScore;
      })[0];

    // 选择最佳的用户消息选择器
    const bestUserSelector = messages.userElements
      .filter((e: any) => e.dataAttributes.length > 0)
      .sort((a: any, b: any) => b.dataAttributes.length - a.dataAttributes.length)[0];

    // 选择最佳的AI消息选择器
    const bestAssistantSelector = messages.assistantElements
      .filter((e: any) => e.dataAttributes.length > 0)
      .sort((a: any, b: any) => b.dataAttributes.length - a.dataAttributes.length)[0];

    // 选择最佳的输入框
    const bestInput = inputs.inputs
      .filter((i: any) => i.tagName === 'textarea' || i.placeholder.toLowerCase().includes('message'))
      .sort((a: any, b: any) => {
        const aScore = (a.tagName === 'textarea' ? 10 : 0) + a.dataAttributes.length;
        const bScore = (b.tagName === 'textarea' ? 10 : 0) + b.dataAttributes.length;
        return bScore - aScore;
      })[0];

    // 选择最佳的发送按钮
    const bestButton = inputs.buttons
      .filter((b: any) => b.ariaLabel.toLowerCase().includes('send') || b.className.includes('send'))
      .sort((a: any, b: any) => b.dataAttributes.length - a.dataAttributes.length)[0];

    const confidence = this.calculateConfidence(containers, messages, inputs);

    return {
      conversationContainer: bestContainer?.selector || '[role="main"]',
      userMessage: bestUserSelector?.selector || '.user-message',
      assistantMessage: bestAssistantSelector?.selector || '.assistant-message',
      messageInput: bestInput?.selector || 'textarea',
      sendButton: bestButton?.selector || 'button[type="submit"]',
      confidence,
      details: {
        containerOptions: containers.slice(0, 3),
        userOptions: messages.userElements.slice(0, 3),
        assistantOptions: messages.assistantElements.slice(0, 3),
        inputOptions: inputs.inputs.slice(0, 3),
        buttonOptions: inputs.buttons.slice(0, 3)
      }
    };
  }

  private calculateConfidence(containers: any[], messages: any, inputs: any): number {
    let score = 0;
    
    // 容器质量评分
    if (containers.some(c => c.dataAttributes.length > 0)) score += 25;
    if (containers.some(c => c.childCount > 5)) score += 15;
    
    // 消息元素评分
    if (messages.userElements.length > 0) score += 20;
    if (messages.assistantElements.length > 0) score += 20;
    if (messages.userElements.some((e: any) => e.dataAttributes.length > 0)) score += 10;
    if (messages.assistantElements.some((e: any) => e.dataAttributes.length > 0)) score += 10;
    
    return Math.min(score, 100);
  }

  private async checkLoginRequired(page: Page): boolean {
    const loginIndicators = [
      'text=Sign in',
      'text=Log in',
      'text=Login',
      '.login-button',
      '.signin-button',
      '[data-testid*="login"]',
      '[data-testid*="signin"]'
    ];

    for (const indicator of loginIndicators) {
      try {
        const element = page.locator(indicator).first();
        if (await element.isVisible({ timeout: 1000 })) {
          return true;
        }
      } catch {
        // 忽略错误
      }
    }

    return false;
  }

  private async waitForUserInput(): Promise<void> {
    return new Promise((resolve) => {
      process.stdin.once('data', () => {
        resolve();
      });
    });
  }

  private saveResults(analysis: any): void {
    const outputDir = 'analysis/results';
    
    if (!existsSync(outputDir)) {
      mkdirSync(outputDir, { recursive: true });
    }

    const filename = join(outputDir, `${this.options.platform.toLowerCase()}.json`);
    writeFileSync(filename, JSON.stringify(analysis, null, 2), 'utf-8');
    
    console.log(`📁 分析结果已保存到: ${filename}`);
  }

  private async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// 命令行参数解析
function parseArgs(): AnalysisOptions {
  const args = process.argv.slice(2);
  const options: any = {};

  args.forEach(arg => {
    if (arg.startsWith('--')) {
      const [key, value] = arg.slice(2).split('=');
      options[key] = value || true;
    }
  });

  if (!options.platform || !options.url) {
    console.error('❌ 缺少必要参数');
    console.log('用法: npm run analyze:single -- --platform=ChatGPT --url=https://chat.openai.com');
    console.log('可选参数:');
    console.log('  --conversationUrl=<url>  会话页面URL');
    console.log('  --loginRequired=true     是否需要登录');
    console.log('  --headless=true          无头模式');
    console.log('  --timeout=30000          超时时间(ms)');
    process.exit(1);
  }

  return {
    platform: options.platform,
    url: options.url,
    conversationUrl: options.conversationUrl,
    loginRequired: options.loginRequired === 'true',
    headless: options.headless === 'true',
    timeout: parseInt(options.timeout) || 30000
  };
}

// 主函数
async function main() {
  const options = parseArgs();
  const analyzer = new SinglePlatformAnalyzer(options);
  
  try {
    await analyzer.analyze();
  } catch (error) {
    console.error('❌ 分析失败:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
