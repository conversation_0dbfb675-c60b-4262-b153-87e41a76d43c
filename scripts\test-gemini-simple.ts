/**
 * 简化的Gemini页面分析测试
 */

import { chromium } from 'playwright';

async function testGeminiAnalysis() {
  console.log('🚀 开始简化的Gemini分析测试...');

  let browser;
  try {
    console.log('📱 启动浏览器...');
    browser = await chromium.launch({
      headless: false,
      slowMo: 500
    });

    console.log('📄 创建新页面...');
    const page = await browser.newPage();

    console.log('🌐 访问Gemini网站...');
    await page.goto('https://gemini.google.com', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });

    console.log('⏰ 等待页面加载...');
    await page.waitForTimeout(3000);

    console.log('📊 分析页面结构...');
    
    // 检查基本页面信息
    const title = await page.title();
    const url = page.url();
    console.log(`页面标题: ${title}`);
    console.log(`当前URL: ${url}`);

    // 查找可能的消息容器
    const messageContainers = await page.$$eval('*', (elements) => {
      return elements
        .filter(el => {
          const text = el.textContent?.toLowerCase() || '';
          const className = el.className?.toLowerCase() || '';
          const tagName = el.tagName.toLowerCase();
          
          // 查找可能包含对话的元素
          return (
            (className.includes('message') || className.includes('conversation') || className.includes('chat') || 
             className.includes('response') || className.includes('prompt') || className.includes('content')) ||
            (tagName === 'main' || tagName === 'article' || tagName === 'section') ||
            (el.getAttribute('role') === 'main' || el.getAttribute('role') === 'dialog')
          ) && el.children.length > 0;
        })
        .slice(0, 15)
        .map((el, index) => ({
          index,
          tagName: el.tagName,
          className: el.className,
          id: el.id,
          role: el.getAttribute('role') || '',
          childCount: el.children.length,
          textPreview: el.textContent?.slice(0, 100) || ''
        }));
    });

    console.log(`找到 ${messageContainers.length} 个可能的消息容器:`);
    messageContainers.forEach(container => {
      console.log(`  ${container.index + 1}. <${container.tagName}> class="${container.className}" id="${container.id}" role="${container.role}"`);
      console.log(`     子元素: ${container.childCount}, 内容预览: ${container.textPreview}...`);
    });

    // 查找输入框
    const inputs = await page.$$eval('input, textarea', (elements) => {
      return elements.map((el, index) => ({
        index,
        tagName: el.tagName,
        type: el.getAttribute('type') || '',
        placeholder: el.getAttribute('placeholder') || '',
        className: el.className,
        id: el.id,
        name: el.getAttribute('name') || ''
      }));
    });

    console.log(`\n找到 ${inputs.length} 个输入元素:`);
    inputs.forEach(input => {
      console.log(`  ${input.index + 1}. <${input.tagName}> type="${input.type}" placeholder="${input.placeholder}"`);
      console.log(`     class="${input.className}" id="${input.id}" name="${input.name}"`);
    });

    // 查找按钮
    const buttons = await page.$$eval('button', (elements) => {
      return elements.map((el, index) => ({
        index,
        textContent: el.textContent?.trim() || '',
        className: el.className,
        id: el.id,
        type: el.getAttribute('type') || '',
        ariaLabel: el.getAttribute('aria-label') || ''
      }));
    });

    console.log(`\n找到 ${buttons.length} 个按钮:`);
    buttons.slice(0, 15).forEach(button => {
      console.log(`  ${button.index + 1}. "${button.textContent}" type="${button.type}" aria-label="${button.ariaLabel}"`);
      console.log(`     class="${button.className}" id="${button.id}"`);
    });

    // 查找特定的Gemini元素
    console.log('\n🔍 查找Gemini特定元素...');
    
    // 查找可能的对话区域
    const chatAreas = await page.$$eval('[class*="chat"], [class*="conversation"], [class*="message"], [role="main"]', (elements) => {
      return elements.map((el, index) => ({
        index,
        tagName: el.tagName,
        className: el.className,
        id: el.id,
        role: el.getAttribute('role') || '',
        dataAttributes: Array.from(el.attributes)
          .filter((attr: any) => attr.name.startsWith('data-'))
          .map((attr: any) => ({ name: attr.name, value: attr.value }))
      }));
    });

    console.log(`找到 ${chatAreas.length} 个对话相关区域:`);
    chatAreas.forEach(area => {
      console.log(`  ${area.index + 1}. <${area.tagName}> class="${area.className}" role="${area.role}"`);
      if (area.dataAttributes.length > 0) {
        console.log(`     数据属性: ${area.dataAttributes.map(attr => `${attr.name}="${attr.value}"`).join(', ')}`);
      }
    });

    // 截图
    console.log('\n📸 保存页面截图...');
    await page.screenshot({ 
      path: 'analysis/screenshots/gemini-simple.png',
      fullPage: true 
    });

    console.log('\n✅ 简化分析完成！');

    // 生成基本的选择器建议
    console.log('\n💡 基于分析的选择器建议:');
    
    if (inputs.length > 0) {
      const mainInput = inputs.find(input => 
        input.placeholder.toLowerCase().includes('ask') || 
        input.placeholder.toLowerCase().includes('enter') ||
        input.placeholder.toLowerCase().includes('message') ||
        input.tagName === 'TEXTAREA'
      ) || inputs[0];
      
      console.log(`消息输入框: ${mainInput.tagName.toLowerCase()}${mainInput.id ? `#${mainInput.id}` : ''}${mainInput.className ? `[class*="${mainInput.className.split(' ')[0]}"]` : ''}`);
    }

    if (buttons.length > 0) {
      const sendButton = buttons.find(button => 
        button.textContent.toLowerCase().includes('send') ||
        button.ariaLabel.toLowerCase().includes('send') ||
        button.type === 'submit'
      );
      
      if (sendButton) {
        console.log(`发送按钮: button${sendButton.id ? `#${sendButton.id}` : ''}${sendButton.className ? `[class*="${sendButton.className.split(' ')[0]}"]` : ''}`);
      }
    }

    if (messageContainers.length > 0) {
      const mainContainer = messageContainers.find(container => 
        container.tagName === 'MAIN' || 
        container.role === 'main' ||
        container.className.toLowerCase().includes('chat') ||
        container.className.toLowerCase().includes('conversation')
      ) || messageContainers[0];
      
      console.log(`对话容器: ${mainContainer.tagName.toLowerCase()}${mainContainer.id ? `#${mainContainer.id}` : ''}${mainContainer.className ? `[class*="${mainContainer.className.split(' ')[0]}"]` : ''}`);
    }

  } catch (error) {
    console.error('❌ 分析失败:', error);
  } finally {
    if (browser) {
      console.log('🔒 关闭浏览器...');
      await browser.close();
    }
  }
}

if (require.main === module) {
  testGeminiAnalysis().catch(console.error);
}
