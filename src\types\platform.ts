/**
 * 平台适配相关类型定义
 */

export interface PlatformConfig {
  name: string;                  // 平台名称
  displayName: string;           // 显示名称
  url: string;                   // 平台URL
  adapter: string;               // 适配器类名
  features: PlatformFeature[];   // 支持的功能
  color: string;                 // 平台主题色
  icon: string;                  // 平台图标
}

export type PlatformFeature = 
  | 'conversation_threads'       // 支持会话线程
  | 'message_streaming'          // 支持流式消息
  | 'code_blocks'               // 支持代码块
  | 'file_upload'               // 支持文件上传
  | 'multimodal'                // 支持多模态
  | 'suggestions'               // 支持建议回复
  | 'multiple_models'           // 支持多个模型
  | 'quick_actions';            // 支持快捷操作

export interface PlatformSelectors {
  conversationContainer: string; // 会话容器选择器
  messageElements: string;       // 消息元素选择器
  userMessage: string;           // 用户消息选择器
  assistantMessage: string;      // AI回复选择器
  messageContent: string;        // 消息内容选择器
  conversationTitle?: string;    // 会话标题选择器
  loadingIndicator?: string;     // 加载指示器选择器
  messageInput?: string;         // 消息输入框选择器
  sendButton?: string;           // 发送按钮选择器
}

export interface PlatformDetectionResult {
  platform: PlatformConfig | null;
  confidence: number;
  url: string;
  timestamp: Date;
}

export interface ContentExtractionResult {
  success: boolean;
  conversation: any | null;
  error?: string;
  warnings?: string[];
  metadata: {
    extractionTime: number;
    messageCount: number;
    platform: string;
    url: string;
  };
}

export interface AdapterStatus {
  platform: string;
  isActive: boolean;
  isObserving: boolean;
  lastActivity: Date;
  errorCount: number;
  lastError?: string;
}

export interface PlatformAnalysis {
  platform: string;
  url: string;
  timestamp: string;
  messageContainers: ElementInfo[];
  messageStructure: {
    userElements: ElementInfo[];
    assistantElements: ElementInfo[];
  };
  inputElements: {
    inputs: ElementInfo[];
    buttons: ElementInfo[];
  };
  screenshot?: string;
  htmlStructure?: string;
  recommendations: AdapterRecommendations;
}

export interface ElementInfo {
  selector: string;
  tagName: string;
  className: string;
  id: string;
  dataAttributes: { name: string; value: string }[];
  textContent: string;
  childCount: number;
  index: number;
  rect?: DOMRect;
  isVisible?: boolean;
}

export interface AdapterRecommendations {
  conversationContainer: string;
  userMessage: string;
  assistantMessage: string;
  messageInput: string;
  sendButton: string;
  confidence: number;
  details?: {
    containerOptions: ElementInfo[];
    userOptions: ElementInfo[];
    assistantOptions: ElementInfo[];
    inputOptions: ElementInfo[];
    buttonOptions: ElementInfo[];
  };
}

export interface StructureSignature {
  selectors: string[];
  attributes: string[];
  textPatterns: string[];
  timestamp: number;
}

export interface MutationAnalysis {
  isRelevant: boolean;
  type: 'message_added' | 'message_updated' | 'conversation_changed' | 'unknown';
  confidence: number;
  affectedElements: Element[];
}

// 支持的平台常量
export const SUPPORTED_PLATFORMS: Record<string, PlatformConfig> = {
  CHATGPT: {
    name: 'ChatGPT',
    displayName: 'ChatGPT',
    url: 'chat.openai.com',
    adapter: 'ChatGPTAdapter',
    features: ['conversation_threads', 'message_streaming', 'code_blocks'],
    color: '#10a37f',
    icon: 'chatgpt'
  },
  CLAUDE: {
    name: 'Claude',
    displayName: 'Claude',
    url: 'claude.ai',
    adapter: 'ClaudeAdapter',
    features: ['conversation_threads', 'message_streaming', 'file_upload'],
    color: '#cc785c',
    icon: 'claude'
  },
  GEMINI: {
    name: 'Gemini',
    displayName: 'Gemini',
    url: 'gemini.google.com',
    adapter: 'GeminiAdapter',
    features: ['conversation_threads', 'multimodal', 'suggestions'],
    color: '#4285f4',
    icon: 'gemini'
  },
  AISTUDIO: {
    name: 'Aistudio',
    displayName: 'AI Studio',
    url: 'aistudio.google.com',
    adapter: 'AistudioAdapter',
    features: ['conversation_threads', 'code_blocks'],
    color: '#ea4335',
    icon: 'aistudio'
  },
  MONICA: {
    name: 'Monica',
    displayName: 'Monica',
    url: 'monica.im',
    adapter: 'MonicaAdapter',
    features: ['conversation_threads', 'quick_actions'],
    color: '#6366f1',
    icon: 'monica'
  },
  POE: {
    name: 'Poe',
    displayName: 'Poe',
    url: 'poe.com',
    adapter: 'PoeAdapter',
    features: ['multiple_models', 'conversation_threads'],
    color: '#ff6b6b',
    icon: 'poe'
  }
};
