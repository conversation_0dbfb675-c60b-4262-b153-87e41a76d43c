<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Chat Memo - 设置</title>
  <link rel="stylesheet" href="options.css">
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
  <div class="min-h-screen flex">
    <!-- 侧边栏 -->
    <div class="w-64 bg-white shadow-sm border-r border-gray-200">
      <div class="p-6">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span class="text-white text-lg">💬</span>
          </div>
          <div>
            <h1 class="text-lg font-semibold text-gray-900">AI Chat Memo</h1>
            <p class="text-sm text-gray-500">设置</p>
          </div>
        </div>
      </div>

      <nav class="px-3 pb-6">
        <ul class="space-y-1">
          <li>
            <a href="#" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150" data-section="general">
              <span class="mr-3">⚙️</span>
              基本设置
            </a>
          </li>
          <li>
            <a href="#" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150" data-section="platforms">
              <span class="mr-3">🌐</span>
              平台设置
            </a>
          </li>
          <li>
            <a href="#" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150" data-section="tags">
              <span class="mr-3">🏷️</span>
              标签管理
            </a>
          </li>
          <li>
            <a href="#" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150" data-section="storage">
              <span class="mr-3">💾</span>
              存储管理
            </a>
          </li>
          <li>
            <a href="#" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150" data-section="export">
              <span class="mr-3">📤</span>
              导出设置
            </a>
          </li>
          <li>
            <a href="#" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150" data-section="privacy">
              <span class="mr-3">🔒</span>
              隐私设置
            </a>
          </li>
          <li>
            <a href="#" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150" data-section="advanced">
              <span class="mr-3">🔧</span>
              高级设置
            </a>
          </li>
        </ul>
      </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 flex flex-col">
      <!-- 头部 -->
      <header class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h2 id="page-title" class="text-xl font-semibold text-gray-900">基本设置</h2>
            <p id="page-description" class="text-sm text-gray-600 mt-1">配置插件的基本功能和行为</p>
          </div>
          <div class="flex items-center space-x-3">
            <button id="reset-btn" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              重置
            </button>
            <button id="save-btn" class="px-4 py-2 text-sm font-medium text-white bg-gray-400 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" disabled>
              保存设置
            </button>
          </div>
        </div>
      </header>

      <!-- 通知区域 -->
      <div class="px-6 py-4">
        <!-- 成功通知 -->
        <div id="success" class="hidden mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
          <div class="flex">
            <div class="flex-shrink-0">
              <span class="text-green-400">✓</span>
            </div>
            <div class="ml-3">
              <p id="success-message" class="text-sm font-medium text-green-800"></p>
            </div>
          </div>
        </div>

        <!-- 错误通知 -->
        <div id="error" class="hidden mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <div class="flex">
            <div class="flex-shrink-0">
              <span class="text-red-400">✗</span>
            </div>
            <div class="ml-3">
              <p id="error-message" class="text-sm font-medium text-red-800"></p>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div id="loading" class="flex-1 flex items-center justify-center">
        <div class="text-center">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p class="mt-2 text-sm text-gray-600">加载中...</p>
        </div>
      </div>

      <!-- 内容区域 -->
      <main id="content" class="flex-1 px-6 py-4 hidden">
        <!-- 动态内容将在这里渲染 -->
      </main>
    </div>
  </div>

  <script src="options.js"></script>
</body>
</html>
