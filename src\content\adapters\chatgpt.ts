/**
 * ChatGPT 平台适配器
 * 专门处理 chat.openai.com 的页面结构和内容提取
 */

import { BasePlatformAdapter, PlatformSelectors } from './base';
import { ConversationData, MessageData } from '@types/conversation';

export class ChatGPTAdapter extends BasePlatformAdapter {
  readonly platform = 'ChatGPT';
  readonly selectors: PlatformSelectors = {
    // 基于真实ChatGPT页面分析结果 (2025-08-01)
    conversationContainer: '[data-testid*="conversation"], [role="main"], main',
    messageElements: '[data-testid*="conversation-turn"], [data-message-author-role], article[data-testid*="conversation"]',
    userMessage: '[data-message-author-role="user"], [data-turn="user"], article[data-turn="user"]',
    assistantMessage: '[data-message-author-role="assistant"], [data-turn="assistant"], article[data-turn="assistant"]',
    messageContent: '.markdown, .whitespace-pre-wrap, .prose, .message-content, .text-message, div[data-message-author-role] > div',
    messageInput: 'textarea[placeholder*="询问"], textarea[placeholder*="Message"], #prompt-textarea, textarea[data-id="root"]',
    sendButton: '[data-testid*="send"], button[data-testid="send-button"], .send-button',
    conversationTitle: 'h1, .text-xl, .conversation-header h1, .chat-title',
    conversationList: 'nav ol li, .conversation-list li, .chat-list-item'
  };

  protected features = {
    hasConversationList: true,
    hasMessageTimestamps: false,
    hasCodeBlocks: true,
    hasImageSupport: true,
    hasFileUpload: true,
    hasConversationExport: false
  };

  isPageReady(): boolean {
    console.log('🔍 [AI Chat Memo] ChatGPTAdapter.isPageReady() called');

    // 检查是否在ChatGPT域名
    if (!this.isCurrentPlatform()) {
      console.log('❌ [AI Chat Memo] Not on ChatGPT platform');
      return false;
    }
    console.log('✅ [AI Chat Memo] On ChatGPT platform');

    // 检查是否在会话页面
    const hasConversationContainer = document.querySelector(this.selectors.conversationContainer) !== null;
    const hasMessageInput = document.querySelector(this.selectors.messageInput) !== null;
    const isConversationUrl = window.location.pathname.includes('/c/') ||
                             window.location.pathname === '/' ||
                             window.location.pathname === '/chat';

    console.log('🔍 [AI Chat Memo] Page elements check:', {
      hasConversationContainer,
      hasMessageInput,
      isConversationUrl,
      pathname: window.location.pathname
    });

    // 等待页面完全加载
    const isPageLoaded = document.readyState === 'complete' ||
                        document.readyState === 'interactive';

    this.logger.debug('页面检查状态:', {
      hasConversationContainer,
      hasMessageInput,
      isConversationUrl,
      isPageLoaded,
      pathname: window.location.pathname
    });

    return hasConversationContainer && hasMessageInput && isConversationUrl && isPageLoaded;
  }

  extractConversation(): ConversationData | null {
    if (!this.isPageReady()) {
      this.logger.debug('页面未准备就绪，跳过提取');
      return null;
    }

    const messages = this.extractMessages();
    
    if (messages.length === 0) {
      this.logger.debug('未找到消息，跳过提取');
      return null;
    }

    const title = this.extractTitle();
    const url = window.location.href;
    const conversationId = this.generateConversationId(url);

    return {
      id: conversationId,
      platform: this.platform,
      url,
      title,
      messages,
      tags: [this.platform.toLowerCase()],
      notes: '',
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        messageCount: messages.length,
        lastActivity: new Date(),
        isArchived: false,
        platformSpecific: {
          hasCodeBlocks: this.hasCodeBlocks(messages),
          hasImages: this.hasImages(messages),
          conversationType: this.detectConversationType(messages)
        }
      }
    };
  }

  private extractMessages(): MessageData[] {
    const messages: MessageData[] = [];

    // 尝试多种选择器来找到消息元素
    let messageElements = document.querySelectorAll(this.selectors.messageElements);

    // 如果没有找到，尝试备用选择器
    if (messageElements.length === 0) {
      const alternativeSelectors = [
        '.group\\/conversation-turn',
        '.conversation-turn',
        '[data-testid*="conversation-turn"]',
        '.flex.flex-col.items-start',
        '.w-full.text-token-text-primary'
      ];

      for (const selector of alternativeSelectors) {
        messageElements = document.querySelectorAll(selector);
        if (messageElements.length > 0) {
          this.logger.debug(`使用备用选择器找到消息: ${selector}`);
          break;
        }
      }
    }

    this.logger.debug(`找到 ${messageElements.length} 个消息元素`);

    messageElements.forEach((element, index) => {
      const messageData = this.extractSingleMessage(element, index);
      if (messageData) {
        messages.push(messageData);
      }
    });

    return this.sortMessagesByDOMOrder(messages);
  }

  private extractSingleMessage(element: Element, index: number): MessageData | null {
    // 检测消息类型
    const role = this.detectMessageRole(element);
    if (!role) {
      return null;
    }

    const content = this.extractMessageContent(element);
    if (!content.trim()) {
      this.logger.debug(`消息 ${index} 内容为空，跳过`);
      return null;
    }

    // 尝试提取时间戳
    const timestamp = this.extractMessageTimestamp(element) || new Date();

    return {
      id: `${role}_${index}_${Date.now()}`,
      conversationId: '',
      type: role as 'user' | 'assistant',
      content,
      timestamp,
      metadata: {
        platform: this.platform,
        originalElement: element.outerHTML.slice(0, 1000),
        elementIndex: index,
        hasCodeBlocks: this.elementHasCodeBlocks(element),
        hasImages: this.elementHasImages(element),
        wordCount: content.split(/\s+/).length,
        characterCount: content.length
      }
    };
  }

  private detectMessageRole(element: Element): string | null {
    // 方法1: 检查data属性
    const dataRole = element.getAttribute('data-message-author-role');
    if (dataRole === 'user' || dataRole === 'assistant') {
      return dataRole;
    }

    // 方法2: 检查类名和结构
    if (element.querySelector('.user-message') ||
        element.classList.contains('user-message') ||
        element.querySelector('[data-message-author-role="user"]')) {
      return 'user';
    }

    if (element.querySelector('.assistant-message') ||
        element.classList.contains('assistant-message') ||
        element.querySelector('[data-message-author-role="assistant"]')) {
      return 'assistant';
    }

    // 方法3: 通过位置和内容推断
    const textContent = element.textContent?.trim() || '';
    const hasAvatar = element.querySelector('img[alt*="User"], img[alt*="ChatGPT"], .avatar');

    // 检查是否包含典型的用户或助手指示器
    if (hasAvatar) {
      const avatarAlt = hasAvatar.getAttribute('alt') || '';
      if (avatarAlt.includes('User') || avatarAlt.includes('user')) {
        return 'user';
      }
      if (avatarAlt.includes('ChatGPT') || avatarAlt.includes('Assistant')) {
        return 'assistant';
      }
    }

    // 方法4: 通过父元素结构推断
    const parentClasses = element.parentElement?.className || '';
    if (parentClasses.includes('user') || element.className.includes('user')) {
      return 'user';
    }
    if (parentClasses.includes('assistant') || element.className.includes('assistant')) {
      return 'assistant';
    }

    // 方法5: 通过消息位置推断（用户消息通常在右侧，助手在左侧）
    const computedStyle = window.getComputedStyle(element);
    const textAlign = computedStyle.textAlign;
    const marginLeft = computedStyle.marginLeft;
    const marginRight = computedStyle.marginRight;

    if (textAlign === 'right' || marginLeft === 'auto') {
      return 'user';
    }

    // 默认假设是助手消息（ChatGPT的回复通常更多）
    if (textContent.length > 10) {
      return 'assistant';
    }

    this.logger.debug('无法确定消息角色:', element);
    return null;
  }

  private extractMessageTimestamp(element: Element): Date | null {
    // 尝试从各种可能的时间戳元素中提取时间
    const timeSelectors = [
      'time',
      '[datetime]',
      '.timestamp',
      '.message-time',
      '.time'
    ];

    for (const selector of timeSelectors) {
      const timeElement = element.querySelector(selector);
      if (timeElement) {
        const datetime = timeElement.getAttribute('datetime') ||
                        timeElement.getAttribute('title') ||
                        timeElement.textContent;

        if (datetime) {
          const date = new Date(datetime);
          if (!isNaN(date.getTime())) {
            return date;
          }
        }
      }
    }

    return null;
  }

  protected extractTitle(): string {
    // 尝试从侧边栏的当前会话标题获取
    const activeConversation = document.querySelector('nav ol li[class*="bg-"], nav ol li.active');
    if (activeConversation) {
      const titleElement = activeConversation.querySelector('a, span');
      if (titleElement) {
        const title = titleElement.textContent?.trim();
        if (title && title !== 'New chat') {
          return title;
        }
      }
    }

    // 尝试从页面主标题获取
    const mainTitle = document.querySelector('h1');
    if (mainTitle) {
      const title = mainTitle.textContent?.trim();
      if (title && title !== 'ChatGPT') {
        return title;
      }
    }

    // 从第一条用户消息生成标题
    const firstUserMessage = document.querySelector(this.selectors.userMessage);
    if (firstUserMessage) {
      const content = this.extractMessageContent(firstUserMessage);
      if (content) {
        return content.slice(0, 50) + (content.length > 50 ? '...' : '');
      }
    }

    return `ChatGPT 会话 - ${new Date().toLocaleDateString()}`;
  }

  protected extractMessageContent(element: Element): string {
    // ChatGPT 特殊处理
    const contentElement = element.querySelector('.markdown') || 
                          element.querySelector('.whitespace-pre-wrap') ||
                          element.querySelector('.prose') ||
                          element;

    if (!contentElement) return '';

    const cloned = contentElement.cloneNode(true) as Element;

    // 处理 ChatGPT 特有的元素
    this.processChatGPTSpecificElements(cloned);

    // 处理代码块
    this.processCodeBlocks(cloned);

    // 处理图片
    this.processImages(cloned);

    // 处理链接
    this.processLinks(cloned);

    return cloned.textContent?.trim() || '';
  }

  private processChatGPTSpecificElements(element: Element): void {
    // 处理思考过程（thinking）
    const thinkingElements = element.querySelectorAll('[data-message-author-role="thinking"]');
    thinkingElements.forEach(thinking => {
      thinking.textContent = `[思考过程]\n${thinking.textContent}`;
    });

    // 处理工具调用
    const toolElements = element.querySelectorAll('.tool-call, [data-tool]');
    toolElements.forEach(tool => {
      const toolName = tool.getAttribute('data-tool') || '工具';
      tool.textContent = `[${toolName}调用]\n${tool.textContent}`;
    });

    // 处理数学公式
    const mathElements = element.querySelectorAll('.katex, .math');
    mathElements.forEach(math => {
      math.textContent = `[数学公式: ${math.textContent}]`;
    });

    // 移除复制按钮等UI元素
    const uiElements = element.querySelectorAll('button, .copy-button, .edit-button');
    uiElements.forEach(ui => ui.remove());
  }

  private elementHasCodeBlocks(element: Element): boolean {
    return element.querySelectorAll('pre, code').length > 0;
  }

  private elementHasImages(element: Element): boolean {
    return element.querySelectorAll('img').length > 0;
  }

  private hasCodeBlocks(messages: MessageData[]): boolean {
    return messages.some(msg => msg.metadata?.hasCodeBlocks);
  }

  private hasImages(messages: MessageData[]): boolean {
    return messages.some(msg => msg.metadata?.hasImages);
  }

  private detectConversationType(messages: MessageData[]): string {
    const content = messages.map(m => m.content).join(' ').toLowerCase();
    
    if (content.includes('代码') || content.includes('code') || content.includes('编程')) {
      return 'coding';
    }
    if (content.includes('翻译') || content.includes('translate')) {
      return 'translation';
    }
    if (content.includes('写作') || content.includes('文章') || content.includes('writing')) {
      return 'writing';
    }
    if (content.includes('数学') || content.includes('计算') || content.includes('math')) {
      return 'math';
    }
    
    return 'general';
  }

  protected generateConversationId(url: string): string {
    // ChatGPT URL 格式: https://chat.openai.com/c/conversation-id
    const match = url.match(/\/c\/([a-zA-Z0-9-]+)/);
    if (match) {
      return match[1];
    }

    // 如果是新会话页面，生成临时ID
    if (url.includes('chat.openai.com')) {
      return `temp_${Date.now()}`;
    }

    return super.generateConversationId(url);
  }
}
