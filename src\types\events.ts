/**
 * 事件系统类型定义
 */

export interface EventData {
  [key: string]: any;
}

export interface EventListener<T = EventData> {
  (data: T): void | Promise<void>;
}

export interface EventEmitter {
  on<T = EventData>(event: string, listener: EventListener<T>): () => void;
  off<T = EventData>(event: string, listener: EventListener<T>): void;
  emit<T = EventData>(event: string, data?: T): Promise<void>;
  once<T = EventData>(event: string, listener: EventListener<T>): () => void;
  removeAllListeners(event?: string): void;
  listenerCount(event: string): number;
}

export interface MessageBusMessage {
  type: string;
  data?: any;
  id: string;
  timestamp: number;
  source: 'background' | 'content' | 'popup' | 'options';
  target?: 'background' | 'content' | 'popup' | 'options' | 'all';
}

export interface MessageBusResponse {
  id: string;
  success: boolean;
  data?: any;
  error?: string;
  timestamp: number;
}

// 具体事件类型定义
export interface ConversationCreatedEvent {
  conversation: any;
  source: 'auto' | 'manual';
}

export interface ConversationUpdatedEvent {
  conversationId: string;
  changes: Partial<any>;
  previousData: any;
}

export interface ConversationDeletedEvent {
  conversationId: string;
  conversation: any;
}

export interface MessageAddedEvent {
  conversationId: string;
  message: any;
  isNew: boolean;
}

export interface MessageUpdatedEvent {
  conversationId: string;
  messageId: string;
  changes: Partial<any>;
  previousData: any;
}

export interface PlatformDetectedEvent {
  platform: string;
  url: string;
  confidence: number;
  features: string[];
}

export interface PlatformChangedEvent {
  from: string | null;
  to: string;
  url: string;
}

export interface AdapterReadyEvent {
  platform: string;
  adapter: string;
  features: string[];
}

export interface AdapterErrorEvent {
  platform: string;
  adapter: string;
  error: string;
  stack?: string;
}

export interface StorageReadyEvent {
  version: number;
  migrated: boolean;
  stats: {
    conversations: number;
    messages: number;
    size: number;
  };
}

export interface StorageErrorEvent {
  operation: string;
  error: string;
  data?: any;
}

export interface StorageQuotaWarningEvent {
  used: number;
  total: number;
  percentage: number;
}

export interface SettingsChangedEvent {
  key: string;
  oldValue: any;
  newValue: any;
  source: 'user' | 'system' | 'sync';
}

export interface UIReadyEvent {
  component: string;
  loadTime: number;
}

export interface UIErrorEvent {
  component: string;
  error: string;
  stack?: string;
  context?: any;
}

export interface PopupOpenedEvent {
  tab: string;
  source: 'icon' | 'shortcut' | 'context_menu';
}

export interface PopupClosedEvent {
  tab: string;
  duration: number;
}

export interface ExtensionInstalledEvent {
  version: string;
  previousVersion?: string;
  isUpdate: boolean;
}

export interface ExtensionUpdatedEvent {
  from: string;
  to: string;
  features: string[];
  breaking: boolean;
}

// 事件类型映射
export interface EventTypeMap {
  // 会话事件
  'conversation:created': ConversationCreatedEvent;
  'conversation:updated': ConversationUpdatedEvent;
  'conversation:deleted': ConversationDeletedEvent;

  // 消息事件
  'message:added': MessageAddedEvent;
  'message:updated': MessageUpdatedEvent;
  'message:deleted': { conversationId: string; messageId: string };

  // 平台事件
  'platform:detected': PlatformDetectedEvent;
  'platform:changed': PlatformChangedEvent;
  'adapter:ready': AdapterReadyEvent;
  'adapter:error': AdapterErrorEvent;

  // 存储事件
  'storage:ready': StorageReadyEvent;
  'storage:error': StorageErrorEvent;
  'storage:quota_warning': StorageQuotaWarningEvent;

  // UI事件
  'ui:ready': UIReadyEvent;
  'ui:error': UIErrorEvent;
  'popup:opened': PopupOpenedEvent;
  'popup:closed': PopupClosedEvent;

  // 系统事件
  'extension:installed': ExtensionInstalledEvent;
  'extension:updated': ExtensionUpdatedEvent;
  'settings:changed': SettingsChangedEvent;
}

// 消息总线消息类型
export interface MessageBusMessageTypes {
  // 存储操作
  'storage:get': { key: string };
  'storage:set': { key: string; value: any };
  'storage:delete': { key: string };
  'storage:clear': {};

  // 会话操作
  'conversation:create': { conversation: any };
  'conversation:update': { id: string; changes: Partial<any> };
  'conversation:delete': { id: string };
  'conversation:get': { id: string };
  'conversation:list': { filters?: any; sort?: any };

  // 消息操作
  'message:add': { conversationId: string; message: any };
  'message:update': { conversationId: string; messageId: string; changes: Partial<any> };
  'message:delete': { conversationId: string; messageId: string };

  // 搜索操作
  'search:query': { query: string; filters?: any };
  'search:suggestions': { query: string };

  // 导出操作
  'export:conversations': { ids: string[]; format: string; options: any };
  'export:download': { filename: string; data: any };

  // 设置操作
  'settings:get': { key?: string };
  'settings:set': { key: string; value: any };
  'settings:reset': { key?: string };

  // 平台操作
  'platform:detect': {};
  'platform:get_status': {};
  'platform:enable': { platform: string };
  'platform:disable': { platform: string };

  // 通知操作
  'notification:show': { notification: any };
  'notification:clear': { id: string };
  'notification:clear_all': {};

  // 统计操作
  'stats:get': { timeRange?: string };
  'stats:platform': { platform: string };
  'stats:activity': { days: number };
}

export type MessageBusEventType = keyof MessageBusMessageTypes;
export type MessageBusEventData<T extends MessageBusEventType> = MessageBusMessageTypes[T];

// 事件常量
export const EVENTS = {
  // 会话相关事件
  CONVERSATION_CREATED: 'conversation:created',
  CONVERSATION_UPDATED: 'conversation:updated',
  CONVERSATION_DELETED: 'conversation:deleted',
  CONVERSATION_ARCHIVED: 'conversation:archived',
  CONVERSATION_UNARCHIVED: 'conversation:unarchived',
  CONVERSATIONS_MERGED: 'conversations:merged',

  // 消息相关事件
  MESSAGE_ADDED: 'message:added',
  MESSAGE_UPDATED: 'message:updated',
  MESSAGE_DELETED: 'message:deleted',

  // 平台检测事件
  PLATFORM_DETECTED: 'platform:detected',
  PLATFORM_CHANGED: 'platform:changed',

  // 存储相关事件
  STORAGE_READY: 'storage:ready',
  STORAGE_ERROR: 'storage:error',

  // 设置相关事件
  SETTINGS_UPDATED: 'settings:updated',
  SETTINGS_RESET: 'settings:reset'
} as const;
