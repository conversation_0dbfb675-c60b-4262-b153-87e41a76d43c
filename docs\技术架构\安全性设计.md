# AI会话管理插件 - 安全性设计

## 🔒 安全性概览

### 安全设计原则

1. **最小权限原则** - 只申请必要的权限
2. **数据本地化** - 所有数据存储在本地，不上传云端
3. **透明性** - 用户完全了解数据的使用和存储
4. **用户控制** - 用户可以随时删除或导出数据
5. **隐私保护** - 不收集任何个人身份信息

## 🛡️ 权限管理策略

### 1. 插件权限设计

```json
{
  "permissions": [
    "storage",           // 本地数据存储
    "activeTab"          // 当前活动标签页访问
  ],
  
  "host_permissions": [
    "https://chat.openai.com/*",
    "https://claude.ai/*",
    "https://gemini.google.com/*",
    "https://aistudio.google.com/*",
    "https://monica.im/*",
    "https://poe.com/*"
  ],
  
  "content_security_policy": {
    "extension_pages": "script-src 'self'; object-src 'self'"
  }
}
```

**权限说明:**
- ✅ **storage**: 用于本地数据存储，不涉及网络传输
- ✅ **activeTab**: 仅访问用户当前查看的标签页
- ✅ **host_permissions**: 仅限于指定的AI平台域名
- ❌ **不申请**: `tabs`、`history`、`bookmarks`等敏感权限

### 2. 权限使用监控

```typescript
// src/shared/security/permission-monitor.ts
export class PermissionMonitor {
  private static readonly ALLOWED_HOSTS = [
    'chat.openai.com',
    'claude.ai',
    'gemini.google.com',
    'aistudio.google.com',
    'monica.im',
    'poe.com'
  ];
  
  static validateHost(url: string): boolean {
    try {
      const hostname = new URL(url).hostname;
      return this.ALLOWED_HOSTS.some(host => 
        hostname === host || hostname.endsWith(`.${host}`)
      );
    } catch {
      return false;
    }
  }
  
  static logPermissionUsage(permission: string, context: string): void {
    console.log(`Permission used: ${permission} in ${context}`);
    // 可以添加到本地日志，供用户查看
  }
}
```

## 🔐 数据安全策略

### 1. 数据分类与保护

```typescript
// src/shared/security/data-classifier.ts
export enum DataSensitivity {
  PUBLIC = 'public',       // 公开数据：平台名称、图标等
  INTERNAL = 'internal',   // 内部数据：设置、偏好等
  PRIVATE = 'private',     // 私有数据：对话内容、标签等
  SENSITIVE = 'sensitive'  // 敏感数据：用户标识、个人信息等
}

export interface DataProtectionPolicy {
  sensitivity: DataSensitivity;
  encryption: boolean;
  retention: number; // 保留天数，-1表示永久
  exportable: boolean;
  deletable: boolean;
}

export const DATA_POLICIES: Record<string, DataProtectionPolicy> = {
  conversations: {
    sensitivity: DataSensitivity.PRIVATE,
    encryption: false, // 根据需求文档，不需要加密
    retention: -1,
    exportable: true,
    deletable: true
  },
  
  settings: {
    sensitivity: DataSensitivity.INTERNAL,
    encryption: false,
    retention: -1,
    exportable: true,
    deletable: true
  },
  
  searchIndex: {
    sensitivity: DataSensitivity.PRIVATE,
    encryption: false,
    retention: -1,
    exportable: false,
    deletable: true
  }
};
```

### 2. 数据存储安全

```typescript
// src/shared/security/secure-storage.ts
export class SecureStorage {
  private static readonly STORAGE_PREFIX = 'ai_chat_memo_';
  
  // 数据完整性验证
  static async saveWithIntegrity(key: string, data: any): Promise<void> {
    const serialized = JSON.stringify(data);
    const checksum = await this.calculateChecksum(serialized);
    
    const secureData = {
      data: serialized,
      checksum,
      timestamp: Date.now(),
      version: '1.0'
    };
    
    await chrome.storage.local.set({
      [this.STORAGE_PREFIX + key]: secureData
    });
  }
  
  static async loadWithIntegrity(key: string): Promise<any> {
    const result = await chrome.storage.local.get(this.STORAGE_PREFIX + key);
    const secureData = result[this.STORAGE_PREFIX + key];
    
    if (!secureData) {
      return null;
    }
    
    // 验证数据完整性
    const calculatedChecksum = await this.calculateChecksum(secureData.data);
    if (calculatedChecksum !== secureData.checksum) {
      throw new Error('Data integrity check failed');
    }
    
    return JSON.parse(secureData.data);
  }
  
  private static async calculateChecksum(data: string): Promise<string> {
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }
}
```

## 🚫 内容安全策略

### 1. XSS防护

```typescript
// src/shared/security/xss-protection.ts
export class XSSProtection {
  // HTML内容清理
  static sanitizeHTML(html: string): string {
    const div = document.createElement('div');
    div.textContent = html;
    return div.innerHTML;
  }
  
  // URL验证
  static validateURL(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return ['http:', 'https:'].includes(urlObj.protocol);
    } catch {
      return false;
    }
  }
  
  // 脚本注入防护
  static sanitizeUserInput(input: string): string {
    return input
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }
}
```

### 2. DOM操作安全

```typescript
// src/shared/security/dom-security.ts
export class DOMSecurity {
  // 安全的DOM查询
  static safeQuerySelector(selector: string): Element | null {
    try {
      // 验证选择器安全性
      if (this.isValidSelector(selector)) {
        return document.querySelector(selector);
      }
    } catch (error) {
      console.warn('Unsafe selector:', selector);
    }
    return null;
  }
  
  // 安全的元素创建
  static createElement(tagName: string, attributes?: Record<string, string>): HTMLElement {
    const allowedTags = ['div', 'span', 'p', 'button', 'input', 'textarea'];
    
    if (!allowedTags.includes(tagName.toLowerCase())) {
      throw new Error(`Tag ${tagName} is not allowed`);
    }
    
    const element = document.createElement(tagName);
    
    if (attributes) {
      Object.entries(attributes).forEach(([key, value]) => {
        if (this.isValidAttribute(key)) {
          element.setAttribute(key, XSSProtection.sanitizeUserInput(value));
        }
      });
    }
    
    return element;
  }
  
  private static isValidSelector(selector: string): boolean {
    // 简单的选择器验证
    const dangerousPatterns = [
      /javascript:/i,
      /data:/i,
      /vbscript:/i,
      /<script/i,
      /on\w+=/i
    ];
    
    return !dangerousPatterns.some(pattern => pattern.test(selector));
  }
  
  private static isValidAttribute(attribute: string): boolean {
    const allowedAttributes = [
      'class', 'id', 'data-*', 'aria-*', 'role',
      'title', 'alt', 'src', 'href', 'type', 'value'
    ];
    
    return allowedAttributes.some(allowed => {
      if (allowed.endsWith('*')) {
        return attribute.startsWith(allowed.slice(0, -1));
      }
      return attribute === allowed;
    });
  }
}
```

## 🔍 隐私保护措施

### 1. 数据匿名化

```typescript
// src/shared/security/privacy-protection.ts
export class PrivacyProtection {
  // 移除个人身份信息
  static anonymizeContent(content: string): string {
    // 移除邮箱地址
    content = content.replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]');
    
    // 移除电话号码
    content = content.replace(/\b\d{3}-\d{3}-\d{4}\b/g, '[PHONE]');
    
    // 移除身份证号等敏感信息
    content = content.replace(/\b\d{15}|\d{18}\b/g, '[ID_NUMBER]');
    
    return content;
  }
  
  // 生成匿名用户ID
  static generateAnonymousId(): string {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }
  
  // 数据脱敏
  static maskSensitiveData(data: any): any {
    if (typeof data === 'string') {
      return this.anonymizeContent(data);
    }
    
    if (Array.isArray(data)) {
      return data.map(item => this.maskSensitiveData(item));
    }
    
    if (typeof data === 'object' && data !== null) {
      const masked: any = {};
      Object.entries(data).forEach(([key, value]) => {
        masked[key] = this.maskSensitiveData(value);
      });
      return masked;
    }
    
    return data;
  }
}
```

### 2. 用户同意管理

```typescript
// src/shared/security/consent-manager.ts
export class ConsentManager {
  private static readonly CONSENT_VERSION = '1.0';
  
  static async getConsent(): Promise<boolean> {
    const consent = await chrome.storage.local.get('user_consent');
    return consent.user_consent?.version === this.CONSENT_VERSION;
  }
  
  static async setConsent(granted: boolean): Promise<void> {
    await chrome.storage.local.set({
      user_consent: {
        granted,
        version: this.CONSENT_VERSION,
        timestamp: Date.now()
      }
    });
  }
  
  static async revokeConsent(): Promise<void> {
    // 撤销同意时清除所有数据
    await chrome.storage.local.clear();
    
    // 清除IndexedDB数据
    const databases = await indexedDB.databases();
    await Promise.all(
      databases.map(db => {
        if (db.name?.startsWith('ai_chat_memo')) {
          return new Promise((resolve, reject) => {
            const deleteReq = indexedDB.deleteDatabase(db.name!);
            deleteReq.onsuccess = () => resolve(undefined);
            deleteReq.onerror = () => reject(deleteReq.error);
          });
        }
      })
    );
  }
}
```

## 🔒 网络安全

### 1. 请求验证

```typescript
// src/shared/security/network-security.ts
export class NetworkSecurity {
  // 验证请求来源
  static validateOrigin(url: string): boolean {
    return PermissionMonitor.validateHost(url);
  }
  
  // 安全的fetch包装
  static async secureFetch(url: string, options?: RequestInit): Promise<Response> {
    if (!this.validateOrigin(url)) {
      throw new Error('Invalid request origin');
    }
    
    // 添加安全头
    const secureOptions: RequestInit = {
      ...options,
      headers: {
        ...options?.headers,
        'X-Requested-With': 'XMLHttpRequest'
      }
    };
    
    return fetch(url, secureOptions);
  }
}
```

## 📊 安全审计

### 1. 安全日志

```typescript
// src/shared/security/security-logger.ts
export class SecurityLogger {
  private static readonly MAX_LOG_ENTRIES = 1000;
  
  static async logSecurityEvent(event: SecurityEvent): Promise<void> {
    const logs = await this.getLogs();
    
    logs.push({
      ...event,
      timestamp: Date.now(),
      id: crypto.randomUUID()
    });
    
    // 保持日志数量限制
    if (logs.length > this.MAX_LOG_ENTRIES) {
      logs.splice(0, logs.length - this.MAX_LOG_ENTRIES);
    }
    
    await chrome.storage.local.set({ security_logs: logs });
  }
  
  static async getLogs(): Promise<SecurityEvent[]> {
    const result = await chrome.storage.local.get('security_logs');
    return result.security_logs || [];
  }
  
  static async clearLogs(): Promise<void> {
    await chrome.storage.local.remove('security_logs');
  }
}

export interface SecurityEvent {
  type: 'permission_used' | 'data_access' | 'error' | 'warning';
  message: string;
  context?: string;
  severity: 'low' | 'medium' | 'high';
}
```

### 2. 定期安全检查

```typescript
// src/shared/security/security-checker.ts
export class SecurityChecker {
  static async performSecurityCheck(): Promise<SecurityReport> {
    const report: SecurityReport = {
      timestamp: Date.now(),
      issues: [],
      recommendations: []
    };
    
    // 检查权限使用
    await this.checkPermissions(report);
    
    // 检查数据完整性
    await this.checkDataIntegrity(report);
    
    // 检查存储使用
    await this.checkStorageUsage(report);
    
    return report;
  }
  
  private static async checkPermissions(report: SecurityReport): Promise<void> {
    // 验证当前权限是否符合最小权限原则
    const permissions = chrome.runtime.getManifest().permissions || [];
    
    permissions.forEach(permission => {
      if (!this.isNecessaryPermission(permission)) {
        report.issues.push({
          type: 'unnecessary_permission',
          message: `Permission ${permission} may not be necessary`,
          severity: 'medium'
        });
      }
    });
  }
  
  private static isNecessaryPermission(permission: string): boolean {
    const necessaryPermissions = ['storage', 'activeTab'];
    return necessaryPermissions.includes(permission);
  }
}

export interface SecurityReport {
  timestamp: number;
  issues: SecurityIssue[];
  recommendations: string[];
}

export interface SecurityIssue {
  type: string;
  message: string;
  severity: 'low' | 'medium' | 'high';
}
```

## ✅ 安全性检查清单

### 开发阶段
- [ ] 代码审查：检查XSS、注入漏洞
- [ ] 权限审查：确保最小权限原则
- [ ] 数据流审查：验证数据处理安全性
- [ ] 依赖审查：检查第三方库安全性

### 测试阶段
- [ ] 渗透测试：模拟攻击场景
- [ ] 数据泄露测试：验证数据隔离
- [ ] 权限测试：验证权限边界
- [ ] 性能测试：防止DoS攻击

### 发布阶段
- [ ] 安全扫描：自动化安全检测
- [ ] 代码混淆：保护源码
- [ ] 签名验证：确保完整性
- [ ] 文档审查：安全使用说明

### 运行阶段
- [ ] 监控异常：检测异常行为
- [ ] 日志审计：定期安全审计
- [ ] 更新机制：及时修复漏洞
- [ ] 用户反馈：收集安全问题

这个安全性设计确保了插件在保护用户隐私和数据安全方面达到了行业最佳实践标准。
