# AI会话管理插件 - 文件结构设计

## 📁 项目目录结构

```
ai-chat-memo/
├── 📁 src/                          # 源代码目录
│   ├── 📁 background/               # 后台脚本
│   │   ├── index.ts                 # 后台脚本入口
│   │   ├── storage.ts               # 数据存储管理
│   │   ├── message-handler.ts       # 消息处理
│   │   └── platform-detector.ts     # 平台检测
│   │
│   ├── 📁 content/                  # 内容脚本
│   │   ├── index.ts                 # 内容脚本入口
│   │   ├── 📁 platforms/            # 平台适配器
│   │   │   ├── base.ts              # 基础平台类
│   │   │   ├── chatgpt.ts           # ChatGPT适配器
│   │   │   ├── claude.ts            # Claude适配器
│   │   │   ├── gemini.ts            # Gemini适配器
│   │   │   ├── aistudio.ts          # Aistudio适配器
│   │   │   ├── monica.ts            # Monica适配器
│   │   │   └── poe.ts               # Poe适配器
│   │   ├── content-detector.ts      # 内容检测器
│   │   ├── dom-observer.ts          # DOM变化监听
│   │   └── floating-indicator.ts    # 悬浮状态指示器
│   │
│   ├── 📁 popup/                    # 弹窗界面
│   │   ├── index.html               # 弹窗HTML
│   │   ├── index.ts                 # 弹窗脚本入口
│   │   ├── popup.css                # 弹窗样式
│   │   ├── 📁 components/           # 弹窗组件
│   │   │   ├── conversation-list.ts # 会话列表组件
│   │   │   ├── search-bar.ts        # 搜索栏组件
│   │   │   ├── filter-tabs.ts       # 过滤标签组件
│   │   │   └── status-indicator.ts  # 状态指示器组件
│   │   └── 📁 pages/                # 弹窗页面
│   │       ├── main.ts              # 主页面
│   │       ├── settings.ts          # 设置页面
│   │       └── export.ts            # 导出页面
│   │
│   ├── 📁 options/                  # 设置页面
│   │   ├── index.html               # 设置页面HTML
│   │   ├── index.ts                 # 设置页面脚本
│   │   ├── options.css              # 设置页面样式
│   │   └── 📁 components/           # 设置页面组件
│   │       ├── platform-settings.ts # 平台设置组件
│   │       ├── export-settings.ts   # 导出设置组件
│   │       └── data-management.ts   # 数据管理组件
│   │
│   ├── 📁 shared/                   # 共享模块
│   │   ├── 📁 utils/                # 工具函数
│   │   │   ├── date.ts              # 日期工具
│   │   │   ├── string.ts            # 字符串工具
│   │   │   ├── dom.ts               # DOM工具
│   │   │   ├── export.ts            # 导出工具
│   │   │   └── validation.ts        # 验证工具
│   │   ├── 📁 storage/              # 存储模块
│   │   │   ├── database.ts          # 数据库操作
│   │   │   ├── indexeddb.ts         # IndexedDB封装
│   │   │   ├── chrome-storage.ts    # Chrome Storage封装
│   │   │   └── search-index.ts      # 搜索索引
│   │   ├── 📁 messaging/            # 消息通信
│   │   │   ├── message-bus.ts       # 消息总线
│   │   │   ├── events.ts            # 事件定义
│   │   │   └── handlers.ts          # 消息处理器
│   │   ├── 📁 ui/                   # UI组件库
│   │   │   ├── base-component.ts    # 基础组件类
│   │   │   ├── modal.ts             # 模态框组件
│   │   │   ├── toast.ts             # 提示组件
│   │   │   ├── button.ts            # 按钮组件
│   │   │   └── form.ts              # 表单组件
│   │   └── 📁 constants/            # 常量定义
│   │       ├── platforms.ts         # 平台常量
│   │       ├── storage-keys.ts      # 存储键名
│   │       ├── messages.ts          # 消息类型
│   │       └── config.ts            # 配置常量
│   │
│   ├── 📁 types/                    # TypeScript类型定义
│   │   ├── index.ts                 # 类型导出
│   │   ├── conversation.ts          # 会话类型
│   │   ├── message.ts               # 消息类型
│   │   ├── platform.ts              # 平台类型
│   │   ├── storage.ts               # 存储类型
│   │   └── ui.ts                    # UI类型
│   │
│   └── 📁 assets/                   # 静态资源
│       ├── 📁 icons/                # 图标文件
│       │   ├── icon-16.png          # 16x16图标
│       │   ├── icon-32.png          # 32x32图标
│       │   ├── icon-48.png          # 48x48图标
│       │   └── icon-128.png         # 128x128图标
│       └── 📁 images/               # 其他图片
│           └── logo.png             # Logo图片
│
├── 📁 public/                       # 公共文件
│   └── manifest.json                # 插件清单文件
│
├── 📁 dist/                         # 构建输出目录
│   ├── manifest.json                # 构建后的清单
│   ├── background.js                # 构建后的后台脚本
│   ├── content.js                   # 构建后的内容脚本
│   ├── popup.html                   # 构建后的弹窗页面
│   ├── popup.js                     # 构建后的弹窗脚本
│   ├── options.html                 # 构建后的设置页面
│   ├── options.js                   # 构建后的设置脚本
│   └── 📁 assets/                   # 构建后的静态资源
│
├── 📁 tests/                        # 测试文件
│   ├── 📁 unit/                     # 单元测试
│   │   ├── utils.test.ts            # 工具函数测试
│   │   ├── storage.test.ts          # 存储模块测试
│   │   └── platforms.test.ts        # 平台适配器测试
│   ├── 📁 integration/              # 集成测试
│   │   ├── content-detection.test.ts # 内容检测测试
│   │   └── data-flow.test.ts        # 数据流测试
│   └── 📁 e2e/                      # 端到端测试
│       └── user-workflow.test.ts    # 用户流程测试
│
├── 📁 docs/                         # 文档目录
│   ├── 📁 技术架构/                 # 技术架构文档
│   ├── 📁 原型/                     # UI原型文件
│   ├── 📁 API/                      # API文档
│   └── README.md                    # 项目说明
│
├── 📁 scripts/                      # 构建脚本
│   ├── build.ts                     # 构建脚本
│   ├── dev.ts                       # 开发脚本
│   └── package.ts                   # 打包脚本
│
├── package.json                     # 项目配置
├── tsconfig.json                    # TypeScript配置
├── vite.config.ts                   # Vite配置
├── tailwind.config.js               # Tailwind配置
├── postcss.config.js                # PostCSS配置
├── eslint.config.js                 # ESLint配置
├── prettier.config.js               # Prettier配置
├── vitest.config.ts                 # Vitest配置
└── .gitignore                       # Git忽略文件
```

## 📋 核心文件说明

### 1. 插件清单文件 (manifest.json)

```json
{
  "manifest_version": 3,
  "name": "AI会话管理助手",
  "version": "1.0.0",
  "description": "自动保存、管理和导出AI聊天记录",
  
  "permissions": [
    "storage",
    "activeTab"
  ],
  
  "host_permissions": [
    "https://chat.openai.com/*",
    "https://claude.ai/*",
    "https://gemini.google.com/*",
    "https://aistudio.google.com/*",
    "https://monica.im/*",
    "https://poe.com/*"
  ],
  
  "background": {
    "service_worker": "background.js"
  },
  
  "content_scripts": [
    {
      "matches": [
        "https://chat.openai.com/*",
        "https://claude.ai/*",
        "https://gemini.google.com/*",
        "https://aistudio.google.com/*",
        "https://monica.im/*",
        "https://poe.com/*"
      ],
      "js": ["content.js"],
      "run_at": "document_end"
    }
  ],
  
  "action": {
    "default_popup": "popup.html",
    "default_title": "AI会话管理",
    "default_icon": {
      "16": "assets/icon-16.png",
      "32": "assets/icon-32.png",
      "48": "assets/icon-48.png",
      "128": "assets/icon-128.png"
    }
  },
  
  "options_page": "options.html",
  
  "icons": {
    "16": "assets/icon-16.png",
    "32": "assets/icon-32.png",
    "48": "assets/icon-48.png",
    "128": "assets/icon-128.png"
  }
}
```

### 2. 构建配置 (vite.config.ts)

```typescript
import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  build: {
    rollupOptions: {
      input: {
        background: resolve(__dirname, 'src/background/index.ts'),
        content: resolve(__dirname, 'src/content/index.ts'),
        popup: resolve(__dirname, 'src/popup/index.html'),
        options: resolve(__dirname, 'src/options/index.html')
      },
      output: {
        entryFileNames: '[name].js',
        chunkFileNames: '[name].js',
        assetFileNames: '[name].[ext]'
      }
    },
    outDir: 'dist',
    emptyOutDir: true
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@shared': resolve(__dirname, 'src/shared'),
      '@types': resolve(__dirname, 'src/types')
    }
  }
})
```

### 3. TypeScript配置 (tsconfig.json)

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "allowJs": false,
    "skipLibCheck": true,
    "esModuleInterop": false,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@shared/*": ["src/shared/*"],
      "@types/*": ["src/types/*"]
    },
    "types": ["chrome", "node", "vitest/globals"]
  },
  "include": [
    "src/**/*",
    "tests/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}
```

## 🔧 模块职责划分

### Background Script (后台脚本)
- **职责**: 插件生命周期管理、跨页面数据同步
- **功能**: 
  - 监听插件安装/更新事件
  - 处理跨页面消息传递
  - 管理全局状态和配置
  - 定期数据清理和优化

### Content Script (内容脚本)
- **职责**: 页面内容检测和数据提取
- **功能**:
  - 监听DOM变化
  - 识别AI对话内容
  - 提取问答数据
  - 显示悬浮状态指示器

### Popup (弹窗界面)
- **职责**: 主要用户交互界面
- **功能**:
  - 显示会话列表
  - 提供搜索和过滤
  - 快速操作入口
  - 状态展示

### Options (设置页面)
- **职责**: 插件配置和数据管理
- **功能**:
  - 平台设置管理
  - 导出配置
  - 数据导入/导出
  - 高级设置

### Shared Modules (共享模块)
- **职责**: 提供通用功能和工具
- **功能**:
  - 数据存储抽象
  - 消息通信机制
  - UI组件库
  - 工具函数集

## 📦 模块依赖关系

```mermaid
graph TD
    A[Background] --> E[Shared/Storage]
    A --> F[Shared/Messaging]
    
    B[Content] --> E
    B --> F
    B --> G[Shared/Utils]
    
    C[Popup] --> E
    C --> F
    C --> H[Shared/UI]
    C --> G
    
    D[Options] --> E
    D --> F
    D --> H
    D --> G
    
    E --> I[Types]
    F --> I
    G --> I
    H --> I
```

## 🚀 构建流程

### 开发模式
```bash
npm run dev
# 启动开发服务器，支持热重载
# 自动监听文件变化并重新构建
```

### 生产构建
```bash
npm run build
# 构建优化后的生产版本
# 代码压缩、Tree-shaking、资源优化
```

### 打包发布
```bash
npm run package
# 创建可发布的zip文件
# 包含所有必要文件和资源
```

这个文件结构设计遵循了模块化、可维护性和可扩展性的原则，为后续的开发工作提供了清晰的组织架构。
