/**
 * UI相关类型定义
 */

export interface UIState {
  isLoading: boolean;
  error: string | null;
  notifications: Notification[];
  activeView: ViewType;
  selectedConversation: string | null;
  searchQuery: string;
  filters: FilterState;
  sortOptions: SortState;
}

export type ViewType = 
  | 'conversations'
  | 'search'
  | 'tags'
  | 'statistics'
  | 'settings'
  | 'export';

export interface FilterState {
  platforms: string[];
  tags: string[];
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  isArchived: boolean | null;
  messageCountRange: {
    min: number;
    max: number;
  };
}

export interface SortState {
  field: 'createdAt' | 'updatedAt' | 'title' | 'messageCount' | 'platform';
  order: 'asc' | 'desc';
}

export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  duration?: number;
  actions?: NotificationAction[];
  persistent?: boolean;
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

export interface PopupState {
  isOpen: boolean;
  currentTab: PopupTab;
  conversations: ConversationListItem[];
  recentActivity: ActivityItem[];
  quickStats: QuickStats;
}

export type PopupTab = 'recent' | 'search' | 'stats' | 'settings';

export interface ConversationListItem {
  id: string;
  title: string;
  platform: string;
  messageCount: number;
  lastActivity: Date;
  tags: string[];
  isArchived: boolean;
  preview: string;
}

export interface ActivityItem {
  id: string;
  type: 'conversation_created' | 'conversation_updated' | 'message_added';
  title: string;
  description: string;
  timestamp: Date;
  platform: string;
  conversationId: string;
}

export interface QuickStats {
  totalConversations: number;
  totalMessages: number;
  todayActivity: number;
  activePlatforms: string[];
  topTags: { tag: string; count: number }[];
}

export interface OptionsPageState {
  activeSection: OptionsSection;
  settings: any;
  isDirty: boolean;
  isSaving: boolean;
  validationErrors: Record<string, string>;
}

export type OptionsSection = 
  | 'general'
  | 'platforms'
  | 'storage'
  | 'export'
  | 'privacy'
  | 'advanced';

export interface FloatingIndicatorState {
  isVisible: boolean;
  status: IndicatorStatus;
  platform: string;
  lastSaved: Date | null;
  messageCount: number;
  isExpanded: boolean;
}

export type IndicatorStatus = 
  | 'idle'
  | 'detecting'
  | 'saving'
  | 'saved'
  | 'error';

export interface SearchState {
  query: string;
  results: SearchResultItem[];
  isSearching: boolean;
  totalResults: number;
  currentPage: number;
  filters: SearchFilters;
  suggestions: string[];
}

export interface SearchResultItem {
  conversationId: string;
  title: string;
  platform: string;
  matches: SearchMatch[];
  score: number;
  lastActivity: Date;
}

export interface SearchMatch {
  messageId: string;
  content: string;
  highlights: string[];
  context: string;
  type: 'user' | 'assistant';
}

export interface SearchFilters {
  platforms: string[];
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  messageType: 'all' | 'user' | 'assistant';
  hasCodeBlocks: boolean | null;
  hasImages: boolean | null;
}

export interface ExportState {
  selectedConversations: string[];
  format: 'markdown' | 'pdf' | 'json' | 'txt';
  options: ExportOptions;
  isExporting: boolean;
  progress: number;
  result: ExportResult | null;
}

export interface ExportOptions {
  includeMetadata: boolean;
  includeTimestamps: boolean;
  template: string;
  filename: string;
  groupByPlatform: boolean;
  sortBy: 'date' | 'platform' | 'title';
}

export interface ExportResult {
  success: boolean;
  filename: string;
  size: number;
  conversationCount: number;
  messageCount: number;
  error?: string;
}

export interface TagManagerState {
  tags: TagItem[];
  selectedTags: string[];
  isEditing: boolean;
  editingTag: string | null;
  newTagName: string;
  searchQuery: string;
}

export interface TagItem {
  name: string;
  count: number;
  color: string;
  lastUsed: Date;
  platforms: string[];
  isSystem: boolean;
}

export interface StatisticsState {
  overview: OverviewStats;
  platformStats: PlatformStats[];
  activityChart: ActivityChartData;
  tagCloud: TagCloudData;
  timeRange: TimeRange;
  isLoading: boolean;
}

export interface OverviewStats {
  totalConversations: number;
  totalMessages: number;
  totalSize: string;
  averageMessagesPerConversation: number;
  mostActiveDay: string;
  mostUsedPlatform: string;
}

export interface PlatformStats {
  platform: string;
  conversationCount: number;
  messageCount: number;
  percentage: number;
  averageLength: number;
  lastActivity: Date;
}

export interface ActivityChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor: string;
    borderColor: string;
  }[];
}

export interface TagCloudData {
  tags: {
    text: string;
    value: number;
    color: string;
  }[];
}

export type TimeRange = '7d' | '30d' | '90d' | '1y' | 'all';

export interface ThemeConfig {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
  };
  fonts: {
    primary: string;
    secondary: string;
    mono: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
  };
}
