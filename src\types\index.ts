/**
 * 类型定义入口文件
 */

// 导出所有类型定义
export * from './conversation';
export * from './platform';
export * from './storage';
export * from './ui';
export * from './events';

// 全局类型定义
export interface AppConfig {
  version: string;
  buildTime: string;
  environment: 'development' | 'production';
  features: {
    analytics: boolean;
    debugging: boolean;
    experimental: boolean;
  };
}

export interface Logger {
  debug(message: string, ...args: any[]): void;
  info(message: string, ...args: any[]): void;
  warn(message: string, ...args: any[]): void;
  error(message: string, error?: Error, ...args: any[]): void;
}

export interface PerformanceMetrics {
  startTime: number;
  endTime: number;
  duration: number;
  memoryUsage?: {
    used: number;
    total: number;
  };
  operations: {
    name: string;
    duration: number;
    success: boolean;
  }[];
}

export interface ErrorInfo {
  code: string;
  message: string;
  stack?: string;
  context?: Record<string, any>;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface FeatureFlag {
  name: string;
  enabled: boolean;
  description: string;
  rolloutPercentage?: number;
  conditions?: Record<string, any>;
}

// 工具类型
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type Awaitable<T> = T | Promise<T>;

export type EventCallback<T = any> = (data: T) => void | Promise<void>;

export type Unsubscribe = () => void;

// 常量定义
export const APP_NAME = 'AI Chat Memo';
export const APP_VERSION = '1.0.0';
export const STORAGE_KEYS = {
  CONVERSATIONS: 'conversations',
  SETTINGS: 'settings',
  TAGS: 'tags',
  STATISTICS: 'statistics',
  LAST_SYNC: 'lastSync'
} as const;

export const EVENTS = {
  // 会话事件
  CONVERSATION_CREATED: 'conversation:created',
  CONVERSATION_UPDATED: 'conversation:updated',
  CONVERSATION_DELETED: 'conversation:deleted',
  
  // 消息事件
  MESSAGE_ADDED: 'message:added',
  MESSAGE_UPDATED: 'message:updated',
  MESSAGE_DELETED: 'message:deleted',
  
  // 平台事件
  PLATFORM_DETECTED: 'platform:detected',
  PLATFORM_CHANGED: 'platform:changed',
  ADAPTER_READY: 'adapter:ready',
  ADAPTER_ERROR: 'adapter:error',
  
  // 存储事件
  STORAGE_READY: 'storage:ready',
  STORAGE_ERROR: 'storage:error',
  STORAGE_QUOTA_WARNING: 'storage:quota_warning',
  
  // UI事件
  UI_READY: 'ui:ready',
  UI_ERROR: 'ui:error',
  POPUP_OPENED: 'popup:opened',
  POPUP_CLOSED: 'popup:closed',
  
  // 系统事件
  EXTENSION_INSTALLED: 'extension:installed',
  EXTENSION_UPDATED: 'extension:updated',
  SETTINGS_CHANGED: 'settings:changed'
} as const;

export const PLATFORMS = {
  CHATGPT: 'ChatGPT',
  CLAUDE: 'Claude',
  GEMINI: 'Gemini',
  AISTUDIO: 'Aistudio',
  MONICA: 'Monica',
  POE: 'Poe'
} as const;

export const MESSAGE_TYPES = {
  USER: 'user',
  ASSISTANT: 'assistant'
} as const;

export const EXPORT_FORMATS = {
  MARKDOWN: 'markdown',
  PDF: 'pdf',
  JSON: 'json',
  TXT: 'txt'
} as const;
