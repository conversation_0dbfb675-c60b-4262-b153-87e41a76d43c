# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build outputs
dist/
build/
*.tgz
*.tar.gz

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Logs
logs
*.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# TypeScript cache
*.tsbuildinfo

# Jest cache
.jest/

# Webpack cache
.webpack/

# Rollup cache
.rollup.cache/

# Vite cache
.vite/

# Turbo cache
.turbo/

# Playwright
/test-results/
/playwright-report/
/playwright/.cache/

# Extension specific
*.crx
*.pem
*.zip

# Development files
.dev/
.tmp/

# Generated files
*.generated.*
auto-generated/

# Backup files
*.backup
*.bak

# Lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# Local configuration
.local/
local.config.*

# Documentation build
docs/build/
docs/.docusaurus/

# Storybook build outputs
storybook-static/

# Chromatic
chromatic.log

# Sentry
.sentryclirc
