/**
 * 存储相关类型定义
 */

export interface StorageConfig {
  maxConversations: number;      // 最大会话数量
  maxMessageLength: number;      // 最大消息长度
  autoCleanupDays: number;       // 自动清理天数
  compressionEnabled: boolean;   // 是否启用压缩
  backupEnabled: boolean;        // 是否启用备份
}

export interface StorageStats {
  totalConversations: number;
  totalMessages: number;
  totalSize: number;             // 总大小(字节)
  usedQuota: number;            // 已使用配额
  availableQuota: number;       // 可用配额
  lastCleanup: Date;            // 最后清理时间
  lastBackup?: Date;            // 最后备份时间
}

export interface StorageOperation {
  type: 'create' | 'update' | 'delete' | 'batch';
  target: 'conversation' | 'message' | 'tag' | 'settings';
  data: any;
  timestamp: Date;
  success: boolean;
  error?: string;
}

export interface BackupData {
  version: string;
  timestamp: Date;
  conversations: any[];
  settings: any;
  metadata: {
    totalConversations: number;
    totalMessages: number;
    platforms: string[];
    exportedBy: string;
  };
}

export interface ImportResult {
  success: boolean;
  imported: {
    conversations: number;
    messages: number;
    tags: number;
  };
  skipped: {
    conversations: number;
    messages: number;
    reasons: string[];
  };
  errors: string[];
  warnings: string[];
}

export interface SyncStatus {
  isEnabled: boolean;
  lastSync: Date;
  nextSync: Date;
  status: 'idle' | 'syncing' | 'error';
  error?: string;
  conflicts: SyncConflict[];
}

export interface SyncConflict {
  id: string;
  type: 'conversation' | 'message';
  local: any;
  remote: any;
  timestamp: Date;
  resolved: boolean;
}

// Chrome Storage API 扩展
export interface ChromeStorageData {
  conversations: Record<string, any>;
  settings: UserSettings;
  tags: string[];
  statistics: any;
  lastSync: number;
}

export interface UserSettings {
  // 基本设置
  autoSave: boolean;
  autoTag: boolean;
  showNotifications: boolean;
  
  // 平台设置
  enabledPlatforms: string[];
  platformSettings: Record<string, PlatformSettings>;
  
  // 存储设置
  storageConfig: StorageConfig;
  
  // 导出设置
  defaultExportFormat: 'markdown' | 'pdf' | 'json' | 'txt';
  exportTemplate: string;
  
  // 界面设置
  theme: 'light' | 'dark' | 'auto';
  language: string;
  compactMode: boolean;
  
  // 隐私设置
  anonymizeData: boolean;
  excludePersonalInfo: boolean;
  
  // 高级设置
  debugMode: boolean;
  experimentalFeatures: boolean;
}

export interface PlatformSettings {
  enabled: boolean;
  autoSave: boolean;
  saveInterval: number;          // 保存间隔(秒)
  maxMessages: number;           // 最大消息数
  excludePatterns: string[];     // 排除模式
  customSelectors?: Partial<any>; // 自定义选择器
}

export interface DatabaseSchema {
  version: number;
  stores: {
    conversations: {
      keyPath: 'id';
      indexes: {
        platform: 'platform';
        createdAt: 'createdAt';
        updatedAt: 'updatedAt';
        tags: 'tags';
      };
    };
    messages: {
      keyPath: 'id';
      indexes: {
        conversationId: 'conversationId';
        timestamp: 'timestamp';
        type: 'type';
      };
    };
    tags: {
      keyPath: 'name';
      indexes: {
        count: 'count';
        lastUsed: 'lastUsed';
      };
    };
    settings: {
      keyPath: 'key';
    };
    statistics: {
      keyPath: 'date';
    };
  };
}

export interface QueryOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  filters?: Record<string, any>;
}

export interface BatchOperation {
  type: 'insert' | 'update' | 'delete';
  table: string;
  data: any[];
  options?: {
    ignoreErrors: boolean;
    validateData: boolean;
  };
}

export interface StorageEvent {
  type: 'created' | 'updated' | 'deleted' | 'batch';
  target: string;
  data: any;
  timestamp: Date;
  source: 'local' | 'sync' | 'import';
}
